{"name": "ehs-partner-mgr", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:release": "vite build --mode release", "preview": "vite preview", "prepare": "husky", "tsc": "vue-tsc --noEmit", "lint": "eslint . --ext .js,.ts,.vue --ignore-path .eslint<PERSON>ore", "lint:fix": "eslint . --ext .js,.ts,.vue --fix", "lint-staged": "lint-staged --no-stash", "commitlint": "commitlint --edit"}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tanzerfe/ifm-child": "^1.0.5", "@tanzerfe/tanzer-ui": "^0.0.6", "@types/node": "^20.14.9", "@vue-office/docx": "^1.6.2", "@vue-office/pptx": "^0.0.6", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "ali-oss": "^6.16.0", "artplayer": "5.1.0", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-plus": "^2.7.6", "flv.js": "1.6.2", "hel-iso": "^4.3.2", "hel-micro": "^4.9.10", "hls.js": "1.5.3", "js-base64": "^3.7.7", "lodash": "^4.17.21", "path": "^0.12.7", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode-vue3": "^1.6.8", "sass": "1.85.0", "swiper": "^10.3.1", "sysend": "^1.17.4", "vue": "^3.4.29", "vue-quill-editor": "^3.0.6", "vue-router": "^4.4.0", "vue3-count-to": "^1.1.2"}, "devDependencies": {"@tanzerfe/eslint-config-lint": "^0.0.5", "@tanzerfe/plugins-vite": "^0.0.1", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^8.36.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.3", "vue-tsc": "^2.0.21", "windicss": "^3.5.6"}}