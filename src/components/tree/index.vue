<template>
  <div class="org-tree">
    <titleTag title="所属单位" />
    <el-input
      v-model="filterText"
      style="width: 100%; margin: 0 0 15px 0; height: 40px"
      placeholder="请输入组织名称"
      :suffix-icon="Search"
      v-if="props.collapsed"
      clearable
    />
    <div
      class="tree-w overflow-hidden"
      :style="{
        height:
          props.type === 'train'
            ? 'calc(100vh - 400px)'
            : props.type === 'must'
              ? 'calc(100vh - 356px)'
              : 'calc(100vh - 260px)'
      }"
    >
      <el-scrollbar>
        <div>
          <el-tree
            ref="treeRef"
            :data="data"
            :props="{ children: 'children', label: 'text' }"
            @node-click="handleNodeClick"
            :highlight-current="true"
            default-expand-all
            node-key="id"
            :expand-on-click-node="false"
            :current-node-key="ui.unitId"
            :filter-node-method="filterNode"
            :render-content="renderContent"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="left-wrap">
                  <span :class="node.isLeaf ? 'leaf-node-icon' : 'parent-node-icon'">{{ data.text }}</span>
                </div>
              </div>
            </template>
            <template #empty>
              <div class="empty-wrap relative">
                <span class="empty-text">暂无数据</span>
                <!-- <div class="empty-text w-100px text-center m-auto">
                  <img src="@/assets/image/no-data-new.png" alt="" />
                  <div>暂无数据</div>
                </div> -->
              </div>
            </template>
          </el-tree>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { ElTree } from 'element-plus'
import { useUserInfo } from '@/store'
import { Search } from '@element-plus/icons-vue'
import titleTag from '@/components/titleTag.vue'
import jianguan from '@/assets/image/examManagement/menu-father.png'
import yewu from '@/assets/image/examManagement/menu-child.png'
import dept from '@/assets/image/examManagement/dept.png'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: ''
  }
})
const ui = useUserInfo()
const filterText = ref('')
const treeRef = ref<InstanceType<typeof ElTree>>()
const isAdd = ref<boolean>(false) // 是否新增
const orgCode = ref<string>(ui.value.unitId)
const orgName = ref<string>(ui.value.unitName)
const data = ref<any>([]) // 树形结构数据
const emits = defineEmits(['serach'])

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.text.includes(value)
}
const handleNodeClick = (data) => {
  orgCode.value = data.id
  orgName.value = data.text
  // 获取点击的节点数据 没有子级 为业务单位 可以新增
  let list = treeRef.value!.getCurrentNode()
  if (list.attributes.orgType === '1') {
    isAdd.value = true
  } else {
    isAdd.value = false
  }
  let treeInfo = {
    orgName: orgName.value,
    orgCode: orgCode.value,
    isAdd: isAdd.value
  }
  emits('serach', treeInfo)
}
/**
 * 顶层为集团 则显示对应系统图标
 * 不为集团 为监管则显示监管 业务显示业务
 */
function getIcon() {
  return ui.value.zhLogoUrl + ui.value.zhLogo + '.png'
}
// 获取目录树
function getTree() {
  return new Promise((resolve, reject) => {
    const params = {
      unitId: ui.value.unitId,
      needCheckJgdw: '1',
      type: '2'
    }
    $API
      .get({
        url: 'edu-inter-server/common/getOrgTree',
        params
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          console.log('res', res)
          if (res.data[0] && res.data[0].attributes.orgType === '1') isAdd.value = true
          let treeInfo = {
            orgName: orgName.value,
            orgCode: orgCode.value,
            isAdd: isAdd.value
          }
          emits('serach', treeInfo)
          data.value = res.data
          resolve(res.data)
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}
function renderContent(h, { data }) {
  let prefixIcon = data.attributes.orgType === '2' ? jianguan : data.attributes.orgType === '1' ? yewu : dept
  if (data.parentId === '-1') prefixIcon = getIcon()

  return h('div', { class: 'flex items-center pl-10px', title: data.text }, [
    h('img', {
      src: prefixIcon,
      class: 'w-[20px] h-[20px] inline-block mr-10px'
    }), // 自定义前缀图标
    h('span', { class: 'truncate' }, data.text) // 显示节点的标签
  ])
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

watch(
  () => props.type,
  (val) => {
    if (val === 'train') {
      treeRef.value!.filter('')
    }
  }
)
watch(
  () => orgCode.value,
  (val) => {
    localStorage.setItem('treeId', val)
  },
  { immediate: true }
)

onMounted(() => {
  getTree()
})

defineOptions({ name: 'trainTree' })
</script>

<style scoped lang="scss">
.org-tree {
  // min-width: 310px;
  // background-color: #eef6ff;
  // height: calc(100vh - 124px);
  background: #eef7ff !important;
  height: 100%;
  overflow: hidden;

  .topUnit {
    height: 37px;

    .unit-name {
      cursor: pointer;
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 400;
      font-size: 14px;
      // color: #222222;
    }
  }

  .tree-w {
    // height: calc(100vh - 260px);
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;

    .left-wrap {
      flex: 1;
      position: relative;

      span {
        padding-left: 25px;
      }
    }

    .right-wrap {
      margin-right: 15px;
      color: #527cff;

      a {
        margin-left: 20px;
        display: inline-block;
      }
    }
  }

  .empty-wrap {
    width: 100%;
    height: 200px;
  }

  :deep(.el-tree) {
    background: #eef7ff;
    // background-color: rgba(238, 247, 255, 1) !important;

    .el-tree-node__content {
      margin-top: 4px;
      border-radius: 6px;
      height: auto;
      padding: 5px 0;
      height: 44px;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        &:first-child {
          display: none;
        }

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }

    .el-tree-node__children .el-tree-node__expand-icon:first-child {
      display: block;
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
  background: #eef7ff;
  // border-right: 1px solid #d9d9d9;
}

:deep(.el-card__body) {
  @apply h-full;
  padding: 0;
}
</style>
