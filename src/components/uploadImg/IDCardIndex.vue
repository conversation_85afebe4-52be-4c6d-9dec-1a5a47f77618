<template>
  <div class="w_pic_box">
    <el-upload
      :limit="1"
      class="upload-demo"
      :file-list="fileList"
      name="uploadfile"
      :action="url"
      list-type="picture-card"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :on-remove="handleDelFile"
      :on-success="onSubmit"
      accept="image/jpeg,image/png"
    >
      <el-icon><Plus /></el-icon>
    </el-upload>

    <el-dialog v-model="dialogVisible">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'

// import $API from '~/common/api'
import { Plus } from '@element-plus/icons-vue'
import type { UploadProps, UploadFile, UploadFiles } from 'element-plus'
// import { useRoute } from 'vue-router'
import config from '@/config'
import { ElMessage } from 'element-plus'

const props = defineProps({
  imgList: {
    type: Array,
    default: () => []
  },
  auto: {
    type: Number,
    default: () => 0
  }
})

const $emits = defineEmits(['getImgList', 'delOneImgList', 'automatic'])

const url = ref(config.update_file + '/file/uploadfile')

const fileList = ref<any[]>([])
fileList.value = [...props.imgList]
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const preventRemove = ref(false)

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png']
  if (!allowedTypes.includes(rawFile.type)) {
    preventRemove.value = true
    ElMessage.error(' 请上传png或jpeg图片格式文件')
    return false
  } else if (rawFile.size / 1024 / 1024 > 10) {
    preventRemove.value = true
    ElMessage.error('图片大小不能大于 10MB!')
    return false
  }
  return true
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}

const handleExceed = () => {
  ElMessage.error('最多只能上传1张')
  return false
}

const handleDelFile = (file: any) => {
  // if (file?.raw?.size) {
  //   console.log(file.raw.size, 'file删除')
  // } else {
  //   console.log(file, 'file删除222')
  // }

  let ids = file.id
  fileList.value = fileList.value.filter((item: any) => item.id !== ids)
  $emits('getImgList', fileList.value)
  if (file.raw) {
  } else {
    ElMessage({
      message: '删除图片成功',
      type: 'success'
    })
  }
}

const onSubmit = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  console.log('上传成功', response, uploadFile, uploadFiles, '文件', fileList.value)
  let params = {
    id: response.data.id,
    name: response.data.fileName,
    url: config.downloadFileUrl + response.data.filePath
  }
  if (props.auto) {
    console.log(uploadFile, '文件-----', uploadFile.raw)
    getCherck(uploadFile.raw)
  }
  $emits('getImgList', fileList.value)

  fileList.value.push({ ...params })
}

async function getCherck(file: any) {
  const formData = new FormData() // 创建FormData对象
  const aBlob = new Blob([file], { type: file.type })
  formData.append('file', aBlob)
  formData.append('type_of_card', props.auto)
  // let url = 'https://agjp.tanzervas.com/aqsc/v1/third/ocr';
  let url = config.ocr_url
  // let url = '/third/ocr';
  try {
    const response = await fetch(url, {
      // 替换为你的上传API地址
      method: 'POST',
      body: formData
    })

    if (response.ok) {
      // 处理上传成功的结果
      // console.log('File uploaded successfully');
      // console.log(response);
      response.json().then((res) => {
        $emits('automatic', {
          data: res,
          auto: props.auto
        })
      })
    } else {
      // 处理上传失败的结果
      console.error('Upload failed')
    }
  } catch (error) {
    console.error('Error:', error)
  }
}

watch(
  () => props.imgList,
  (newV: any) => {
    fileList.value = [...newV]
  },
  {
    immediate: true,
    deep: true
  }
)
// 创建计划
defineOptions({ name: 'testPicIndex' })
</script>

<style scoped lang="scss">
.w_pic_box {
  display: grid;
  width: 100%;
  height: 100%;
  padding: 24px;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 32px;
  font-size: 14px;
  background-color: white;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }
  :deep(.el-upload-list--picture-card) {
    //   flex-direction: row-reverse;
    --el-upload-list-picture-card-size: 80px;
  }
  :deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 80px;
    margin: 0 8px 8px 0;
  }

  :deep(.el-upload-list__item.is-success:focus:not(:hover)) {
    display: none !important;
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
.upload-demo .el-upload-list__item.is-success:focus:not(:hover) {
  display: none !important;
}

.left-side {
  float: left;
}
</style>
