<!--折线图-->
<template>
  <div v-if="!isEmpty" class="line-part w-full h-full" ref="lineChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'

defineComponent({ name: 'LineChart' })

const props = defineProps({
  echartsData: {
    type: Object,
    default: () => {
      return { label: [], data: [] }
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['rgba(0, 244, 230, 1)', 'rgba(0, 146, 249, 1)']
    },
  },
  graphicColor: {
    type: Array,
    default: () => {
      return []
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { smooth: false, yAxis: {}, maxLabel: 10, legend: {}, tooltip: {} }
    },
  },
})

const isEmpty = ref(false)
const lineChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(echartsData: any) {
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(lineChart.value))
  const color = props.color
  const graphicColor: any[] = props.graphicColor || [[]]
  let series: object[] = []
  const data = echartsData.data || []
  const label = echartsData.label || []

  data.forEach((item: any, index: number) => {
    const part: any = {
      name: item.name,
      type: 'line',
      smooth: props.extra.smooth,
      data: item.value,
      symbol: 'circle',
      symbolSize: 5,
      showSymbol: false,
      lineStyle: {
        width: 2,
      },
      itemStyle: {
        color: color[index],
      },
    }
    if (graphicColor.length) {
      part.areaStyle = {
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          0,
          1,
          [
            {
              offset: 0,
              color: graphicColor[index][0],
            },
            {
              offset: 0.8,
              color: graphicColor[index][1],
            },
          ],
          false
        ),
      }
    }
    series.push(part)
  })
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      backgroundColor: 'rgba(0,150,236,0.6)',
      textStyle: {
        color: '#333',
      },
      ...props.extra?.tooltip,
    },
    legend: {
      icon: 'circle',
      itemWidth: 6,
      itemHeight: 6,
      itemGap: 26,
      data: data.map((item: any) => item.name),
      top: 16,
      right: 12,
      textStyle: {
        fontSize: 14,
        color: '#333',
      },
      ...props.extra.legend,
    },
    grid: {
      top: '40',
      left: '20',
      right: '20',
      bottom: '20',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: '0%',
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 12,
          color: '#6E7079',
          interval: function (index: number) {
            // 控制最多显示x个标签
            const dataLength = option.xAxis[0].data.length
            const interval = Math.ceil(dataLength / props.extra.maxLabel)
            return index % interval === 0
          },
        },
        axisLine: {
          lineStyle: {
            fontSize: 12,
            color: '#E0E0E6',
          },
        },
        data: label,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        minInterval: 1,
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 10,
          color: '#6E7079',
        },
        axisLine: {
          show: false,
          lineStyle: {
            fontSize: 12,
            color: '#526D85',
          },
        },
        splitLine: {
          show: true,
        },
        ...props.extra.yAxis,
      },
    ],
    series,
  }
  myChart.value.setOption(option)

  observer.value = useEchartsResizeObserver(myChart, lineChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

function getImgUrl() {
  return myChart.value?.getDataURL({ type: 'png' })
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any) => {
      destroyEcharts()
      isEmpty.value = !val.data.length
      await sleep(500)
      if (!isEmpty.value && lineChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})

defineExpose({ getImgUrl })
</script>

<style scoped></style>
