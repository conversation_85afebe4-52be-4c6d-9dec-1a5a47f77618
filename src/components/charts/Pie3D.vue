<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="chartRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import Empty from '@/components/public/noData.vue'
import { sleep } from '@/common/utils'
import * as echarts from 'echarts'
import 'echarts-gl'
import { markRaw, onMounted, ref, watch } from 'vue'

const props = defineProps({
  chartData: {
    type: Object,
    default: () => {
      return []
    },
  },
  extra: {
    type: Object,
    default: () => {
      return {
        grid: {},
        color: ['#549BF1', '#32498E', '#5A70BC', '#AA8A6E', '#3182EA', '#0DAEE3', '#BCBF5C', '#67D470', '#D0C500'],
        legend: {},
        labelLine: true,
        grid3D: {},
      }
    },
  },
})

const chartRef = ref()
const myChart = ref<any>(null)

function getParametricEquation(startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, height: any) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2

  let startRadian = startRatio * Math.PI * 2
  let endRadian = endRatio * Math.PI * 2
  let midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },

    v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },

    x: (u: any, v: any) => {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: (u: any, v: any) => {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: (u: any, v: any) => {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u)
      }
      return Math.sin(v) > 0 ? 1 * height : -1
    },
  }
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData: any, internalDiameterRatio?: any) {
  let series: any = []
  let sumValue = 0
  let startValue = 0
  let endValue = 0
  let legendData = []
  let k =
    typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value

    let seriesItem: Record<string, any> = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    }

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {
        color: undefined,
        opacity: undefined,
      }

      typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null

      seriesItem.itemStyle = itemStyle
    }
    series.push(seriesItem)
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value
    series[i].pieData.startRatio = startValue / sumValue
    series[i].pieData.endRatio = endValue / sumValue
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    )

    startValue = endValue

    legendData.push(series[i].name + series[i].value)
  }

  // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },
    parametricEquation: {
      u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      x: (u: any, v: any) => {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: (u: any, v: any) => {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: (u: any, v: any) => {
        // return Math.cos(v) > 0 ? -0.5 : -5;
        return Math.cos(v) > 0 ? -1 : -1.1
      },
    },
  })

  // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },
    parametricEquation: {
      u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      x: (u: any, v: any) => {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
      },
      y: (u: any, v: any) => {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
      },
      z: (u: any, v: any) => {
        // return Math.cos(v) > 0 ? -5 : -7;
        return Math.cos(v) > 0 ? -1.1 : -1.2
      },
    },
  })
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    itemStyle: {
      opacity: 0.1,
      color: '#E1E8EC',
    },

    parametricEquation: {
      u: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
      v: { min: 0, max: Math.PI, step: Math.PI / 20 },
      x: (u: any, v: any) => {
        return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.2
      },
      y: (u: any, v: any) => {
        return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.2
      },
      z: (u: any, v: any) => {
        // return Math.cos(v) > 0 ? -7 : -7;
        return Math.cos(v) > 0 ? -1.2 : -1.1
      },
    },
  })
  return series
}

function getMaxValue(chartData: any) {
  return Math.max(...chartData.map((item: any) => +item.value))
}

function initCharts() {
  const maxV = getMaxValue(props.chartData)
  const boxHeight = +(100 / maxV).toFixed(2)

  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(chartRef.value))

  const optionsData = props.chartData.map((item: any) => {
    return {
      ...item,
      itemStyle: {
        opacity: props.extra?.opacity || 0.8,
      },
    }
  })

  const legendData = props.chartData.map((e: any) => {
    return {
      icon: 'circle',
      name: e.name,
      value: e.value,
    }
  })

  let series = getPie3D(optionsData, 0.7) as any

  // 传入数据生成 option
  const option = {
    color: props.extra?.color,
    legend: {
      tooltip: {
        show: false,
      },
      right: '10%',
      top: 'center',
      data: legendData,
      orient: 'vertical',
      type: 'scroll',
      formatter: function (name: any) {
        let target = 0
        for (let i = 0; i < legendData.length; i++) {
          if (legendData[i].name === name) {
            target = legendData[i].value
          }
        }
        return ['{a|' + name + '}', '{b|' + target + '%}']
      },
      textStyle: {
        // 为了图例与第二行文字对齐，需要设置两个样式的padding，把文字顶到合适的位置，然后为了上下行的间隔，设置了第2行文字的行高
        rich: {
          a: {
            fontSize: 14,
            color: '#ffffff',
            align: 'center',
          },
          b: {
            fontSize: 12,
            color: '#2BD9ED',
            align: 'center',
            padding: [1, 0, 0, 10],
          },
        },
      },
      ...props.extra?.legend,
    },
    animation: true,
    tooltip: {
      backgroundColor: 'rgba(0,150,236,0.6)',
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          return `<span style="color: #ffffff">${params.seriesName}</span><br/>
                   <span style="color: #ffffff;display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
                    <span style="color: #ffffff">${option.series[params.seriesIndex].pieData.value + props.extra?.unit}</span>`
        }
      },
      textStyle: {
        fontSize: 14,
      },
      ...props.extra?.tooltip,
    },
    title: {
      x: '0',
      top: '20',
      textStyle: {
        color: '#fff',
        fontSize: 22,
      },
      ...props.extra?.title,
    },
    labelLine: {
      show: props.extra?.labelLine || false,
    },
    label: {
      show: props.extra?.labelLine || false,
      position: 'outside',
      formatter: '{b} \n{c} {d}%',
    },
    xAxis3D: { min: -1, max: 1 },
    yAxis3D: { min: -1, max: 1 },
    zAxis3D: { min: -1, max: 1 },
    grid3D: {
      show: false,
      boxHeight: boxHeight,
      left: '-20%',
      top: '5%',
      viewControl: {
        alpha: 30, //角度(这个很重要 调节角度的)
        distance: 200, //调整视角到主体的距离，类似调整zoom(这是整体大小)
        rotateSensitivity: 1, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 0, //设置为0无法平移
        autoRotate: false, //自动旋转
      },
      ...props.extra?.grid3D,
    },
    series: series,
  }

  myChart.value.setOption(option, true)
  window.addEventListener('resize', () => {
    if (myChart.value) myChart.value.resize()
  })
}

const isEmpty = ref(false)

onMounted(() => {
  watch(
    () => props.chartData,
    async (val: any) => {
      const _data = val?.filter((e: any) => e.value > 0)
      isEmpty.value = !val || !_data.length
      await sleep(500)
      if (!isEmpty.value && chartRef.value) initCharts()
    },
    { immediate: true, deep: true }
  )
})

defineOptions({ name: 'Pie3D' })
</script>

<style scoped lang="scss"></style>
