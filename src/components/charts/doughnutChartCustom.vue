<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="doughnutChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { sleep } from '@/common/utils'
import Empty from '@/components/empty/index.vue'
import { useEchartsResizeObserver } from '@/common/utils/useEchartsResizeObserver'
import _sum from 'lodash-es/sum'

defineComponent({ name: 'doughnutChartCustom' })

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return []
    },
  },
  color: {
    type: Array,
    default: () => {
      return ['#f55555', '#00BAFF', '#FF7F57', '#12C487', '#FFBE4C', '#18DDBF', '#429BFF', '#625EFF', '#D256F1']
    },
  },
  legendType: {
    type: String,
    default: 'scroll',
  },
  legendOrient: {
    type: String,
    default: '',
  },
  legendBottom: {
    type: String,
    default: '10%',
  },
  legendTop: {
    type: Number,
    default: 0,
  },
  legendRight: {
    type: Number,
    default: 0,
  },
  seriesCenter: {
    type: Array,
    default: () => {
      return ['50%', '38%']
    },
  },
  seriesRadius: {
    type: Array,
    default: () => {
      return ['30%', '50%']
    },
  },
  labelAndLabelLine: {
    type: Object,
    default: () => {
      return {
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
      }
    },
  },
  seriesMinAngle: {
    type: Number,
    default: 0,
  },
  seriesItemStyleNormalBorderWidth: {
    type: Number,
    default: 0,
  },
  formatter: {
    type: Function,
    default: (name: string, data: any[]) => {
      const count = _sum(data.map((item: any) => item.value))
      const val = data.filter((item: any) => {
        return item.name === name
      })
      const num = count ? ((Number(val[0].value) / count) * 100).toFixed(2) : 0
      return `${name}  ${num}%`
    },
  },
  formatterTitle: {
    type: String,
    default: '{b} : {c} ({d}%)',
  },
})

const isEmpty = ref(false)
const doughnutChart = ref()
const myChart = ref<any>(null)
const observer = ref<ResizeObserver>()

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts()
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(doughnutChart.value))
  const color: any[] = props.color.map((item) => {
    if (item instanceof Array) {
      return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: item[0] }, // 渐变色起始颜色
        { offset: 1, color: item[1] }, // 渐变色结束颜色
      ])
    } else {
      return item
    }
  })
  const legendType = props.legendType
  const legendOrient = props.legendOrient
  const legendBottom = props.legendBottom
  const legendTop = props.legendTop
  const legendRight = props.legendRight
  const seriesCenter = props.seriesCenter
  const seriesMinAngle = props.seriesMinAngle
  const seriesRadius = props.seriesRadius
  const seriesItemStyleNormalBorderWidth = props.seriesItemStyleNormalBorderWidth
  const labelAndLabelLine = props.labelAndLabelLine
  const formatter = props.formatter
  const formatterTitle = props.formatterTitle
  const option: any = {
    color: color,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,150,236,0.6)',
      formatter: formatterTitle,
      textStyle: {
        color: '#333',
      },
    },
    legend: {
      icon: 'circle',
      bottom: legendBottom,
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: '14',
      },
      backgroundColor: '#E7EDF6', // 设置图例背景色
      borderRadius: 10, // 设置图例的圆角
      padding: [20, 20, 20, 20], // 设置图例的内边距
      formatter(name: string) {
        return formatter(name, data)
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        minAngle: seriesMinAngle, //最小的扇区角度（0 ~ 360）
        radius: seriesRadius,
        center: seriesCenter,
        avoidLabelOverlap: false,
        itemStyle: {
          //图形样式
          normal: {
            borderColor: '#EAF5FF',
            borderWidth: seriesItemStyleNormalBorderWidth,
          },
        },
        data: data,
        ...labelAndLabelLine,
      },
    ],
  }

  if (legendType) {
    option.legend.type = legendType === 'null' ? '' : legendType
  }
  if (legendOrient) {
    option.legend.orient = legendOrient
  }
  if (legendRight) {
    option.legend.right = legendRight
  }
  if (legendTop) {
    option.legend.top = legendTop
  }

  myChart.value.setOption(option)
  observer.value = useEchartsResizeObserver(myChart, doughnutChart).observer
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      // 空数组或数组内的value全为0时展示缺省
      isEmpty.value = !val.length || val.every((item) => item.value === 0)
      await sleep(500)
      if (!isEmpty.value && doughnutChart.value) initEcharts(val)
    },
    { immediate: true, deep: true }
  )
})

onBeforeUnmount(() => {
  destroyEcharts()
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect()
})
</script>

<style scoped></style>
