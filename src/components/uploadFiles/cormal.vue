<template>
  <div>
    <el-upload
      name="uploadfile"
      class="upload__cormal"
      :disabled="disabled"
      :action="url"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :on-remove="handleDelFile"
      :on-success="onSubmit"
      style="width: 200px"
    >
      <el-button type="primary" :loading="disabled"
        ><el-icon><Plus /></el-icon>上传文件</el-button
      >
    </el-upload>

    <div class="upload__file__box">
      <!--图片展示区-->
      <div v-if="acceptType === '1'">
        <div v-for="item in fileList" :key="item.id" class="upload__image">
          <div class="upload__action">
            <el-icon class="upload__action__icon" @click="preview(item)"><ZoomIn /></el-icon>
            <el-icon class="upload__action__icon" @click="deleteFile(item.id)"><Delete /></el-icon>
          </div>
          <img :src="compileUrl(item.filePath)" />
        </div>
      </div>

      <!--文档展示区-->
      <div v-if="acceptType === '2'">
        <div class="upload__doc" v-for="item in fileList" :key="item.id">
          <el-icon class="upload__doc__close" @click="deleteFile(item.id)"><Close /></el-icon>
          <img v-if="item.suffix === '.pdf'" src="../../assets/image/pdf__icon.png" :title="item.name" />
          <img v-else src="../../assets/image/doc__icon.png" :title="item.name" />
          <span :title="item.name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!--图片预览放大-->
    <div class="upload__preview">
      <el-image
        style="width: 0; height: 0"
        :src="imageUrl"
        id="image-upload"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="srcList"
        :initial-index="100"
        fit="cover"
        preview-teleported
        lazy
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, watch, defineProps, onMounted, nextTick } from 'vue'
import config from '@/config'
import type { UploadProps, UploadFile, UploadFiles } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus, Close, ZoomIn, Delete } from '@element-plus/icons-vue'
const url = ref(config.update_file + '/file/uploadfile')
const fileList = ref<any[]>([])
const acceptType = ref<string>('') // 1: 图片 2: 文档
const emits = defineEmits(['eventFile'])
const disabled = ref<boolean>(false)
const srcList = ref<string[]>([])
const imageUrl = ref<string>('')
let elem: HTMLElement | null = null

const handleDelFile: UploadProps['onRemove'] = (file, uploadFiles) => {
  // 如果删除全部的文件后将扩展名置空
  const index = fileList.value.findIndex((item) => item.name === file.name)
  if (index !== -1) {
    fileList.value.splice(index, 1) // 删除文件
  }

  if (fileList.value.length === 0) {
    acceptType.value = ''
  }
  emits('eventFile', fileList.value)
}

const props = defineProps({
  limit: {
    type: Number,
    default: 10 // 最大可以上传10张
  },
  size: {
    type: Number,
    default: 10 // 最大文件上传大小10M
  },
  accept: {
    type: String,
    default: '' // 不限制
  },
  list: {
    type: Array,
    default: [] // 文件列表
  }
})

const setFileType = (list: []) => {
  const imageType = ['.png', '.jpeg', '.jpg', '.gif']
  const dcoType = ['.doc', 'docx', '.pdf']
  if (imageType.includes(list[0].suffix)) {
    acceptType.value = '1'
  } else if (dcoType.includes(list[0].suffix)) {
    acceptType.value = '2'
  }
}

const compileUrl = (url: string) => {
  return config.downloadFileUrl + url
}

fileList.value = props.list
onMounted(() => {
  elem = document.getElementById('image-upload')
  if (props.list.length > 0) {
    setFileType(props.list)
  }
})

const preview = (obj: any) => {
  imageUrl.value = compileUrl(obj.filePath)
  srcList.value = [compileUrl(obj.filePath)]
  nextTick(() => {
    elem.children && elem.children[0].click()
  })
}

const handleExceed = () => {
  disabled.value = false
  ElMessage.error(`超出最大上传文件数量`)
  return false
}

const deleteFile = (id: string) => {
  const index = fileList.value.findIndex((item) => item.id === id)
  if (index !== -1) {
    fileList.value.splice(index, 1) // 删除文件
  }

  if (fileList.value.length === 0) {
    acceptType.value = ''
  }
  emits('eventFile', fileList.value)
}

const onSubmit = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  let params = {
    id: response.data?.id,
    name: response.data?.fileName,
    fileName: response.data?.fileName,
    url: config.downloadFileUrl + response.data?.filePath,
    filePath: response.data?.filePath,
    fileSize: response.data?.fileSize,
    suffix: response.data?.suffix,
    businessType: '1'
  }
  fileList.value.push({ ...params })
  emits('eventFile', fileList.value)
  disabled.value = false
}
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  disabled.value = true
  console.log(rawFile.type, '-----rawFile.type111----')
  const allowedImagesTypes = ['image/jpeg', 'image/png', 'image/gif']
  const allowedDocumentTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf',
    'application/msword'
  ]

  if (acceptType.value === '') {
    if (allowedImagesTypes.includes(rawFile.type)) {
      acceptType.value = '1'
    } else if (allowedDocumentTypes.includes(rawFile.type)) {
      acceptType.value = '2'
    }
  }

  if (acceptType.value === '1') {
    if (fileList.value.length >= 9) {
      disabled.value = false
      ElMessage.error('最大可以上传9张图片')
      return false
    }

    // 前面传的是图片
    if (!allowedImagesTypes.includes(rawFile.type)) {
      disabled.value = false
      ElMessage.error('仅支持上传图片')
      return false
    }
  } else if (acceptType.value === '2') {
    if (fileList.value.length >= 3) {
      disabled.value = false
      ElMessage.error('最大可以上传3份文件')
      return false
    }

    // 前面传的是文档
    if (!allowedDocumentTypes.includes(rawFile.type)) {
      disabled.value = false
      ElMessage.error('请上传doc或pdf格式文件')
      return false
    }
  }

  // 即不是文档也不是图片
  if (!allowedImagesTypes.includes(rawFile.type) && !allowedDocumentTypes.includes(rawFile.type)) {
    ElMessage.error('仅支持上传图片，pdf，doc格式文件')
    disabled.value = false
    return false
  }

  if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('文件大小不能大于10MB!')
    disabled.value = false
    return false
  }
}

const handleError = () => {
  disabled.value = false
}

watch(
  () => props.list,
  (newVal: []) => {
    if (newVal.length > 0) {
      setFileType(newVal)
    }
    fileList.value = newVal
  }
)
</script>

<style lang="scss">
.upload__cormal {
  .el-upload-list {
    display: none;
  }
}
</style>

<style lang="scss" scoped>
.upload__file__box {
  width: 330px;
  margin-top: 20px;
  & > div {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
  .upload__image {
    position: relative;
    width: 105px;
    height: 100px;
    display: flex;
    margin-right: 5px;
    margin-bottom: 5px;
    justify-content: center;
    align-items: center;
    border: 1px solid #eee;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
    img {
      max-width: 80px;
    }
    .upload__action {
      position: absolute;
      left: 0;
      top: 0;
      width: 105px;
      height: 100px;
      display: none;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
      .upload__action__icon {
        font-size: 20px;
        color: #fff;
        margin: 0 5px;
      }
    }
    &:hover {
      .upload__action {
        display: flex;
      }
    }
  }
  .upload__doc {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 105px;
    height: 100px;
    border: 1px solid #ededed;
    border-radius: 5px;
    margin-right: 5px;
    margin-bottom: 5px;
    img {
      width: 40px;
    }
    span {
      width: 100%;
      display: block;
      white-space: nowrap;
      margin-top: 10px;
      padding: 0 10px;
      font-size: 13px;
      box-sizing: border-box;
      height: 20px;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #1890ff;
      text-align: center;
    }
  }
  .upload__doc__close {
    position: absolute;
    right: 5px;
    top: 5px;
    color: #999;
    cursor: pointer;
    display: none;
  }
  .upload__doc:hover {
    .upload__doc__close {
      display: block;
    }
  }
}
.upload__preview {
  height: 0;
  width: 0;
  overflow: hidden;
}
</style>
