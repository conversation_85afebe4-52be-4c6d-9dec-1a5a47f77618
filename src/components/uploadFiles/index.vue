<template>
  <div>
    <el-upload
      :file-list="fileList"
      name="uploadfile"
      :disabled="disabled"
      :action="url"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :on-remove="handleDelFile"
      :on-success="onSubmit"
      style="width: 200px"
    >
      <el-button type="primary"
        ><el-icon><Plus /></el-icon>上传文件</el-button
      >
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, watch, defineProps } from 'vue'
import config from '@/config'
import type { UploadProps, UploadFile, UploadFiles } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
// const url = ref(config.update_file + '/file/uploadfile');
const url = ref(config.update_file)
const fileList = ref<any[]>([])
const extension = ref<string>('')
const acceptType = ref<string>('')
const emits = defineEmits(['eventFile'])
const disabled = ref<boolean>(false)

const handleDelFile: UploadProps['onRemove'] = (file, uploadFiles) => {
  // 如果删除全部的文件后将扩展名置空
  const index = fileList.value.findIndex((item) => item.name === file.name)
  if (index !== -1) {
    fileList.value.splice(index, 1) // 删除文件
  }
  emits('eventFile', fileList.value)
  if (uploadFiles.length === 0) {
    extension.value = ''
  }
}

const props = defineProps({
  list: {
    type: Array,
    default: []
  }
})

watch(
  () => props.list,
  (newVal) => {
    fileList.value = props.list
  }
)

const handleExceed = () => {
  disabled.value = false
  ElMessage.error(`超出最大上传文件数量`)
  return false
}

const onSubmit = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  let params = {
    id: response.data?.id,
    name: response.data?.fileName,
    fileName: response.data?.fileName,
    url: config.downloadFileUrl + response.data?.filePath,
    filePath: response.data?.filePath,
    fileSize: response.data?.fileSize,
    suffix: response.data?.suffix
  }
  if (response.data?.fileName) {
    const nameArr = response.data?.fileName.split('.')
    extension.value = nameArr[nameArr.length - 1]
  }
  emits('eventFile', fileList.value)

  fileList.value.push({ ...params })
  disabled.value = false
}
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  console.log(rawFile.type, '-----rawFile.type111----')
  const allowedImagesTypes = ['image/jpeg', 'image/png', 'image/gif']
  const allowedDocumentTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf',
    'application/msword'
  ]

  if (acceptType.value === '1') {
    if (fileList.value.length >= 9) {
      disabled.value = false
      ElMessage.error('最大可以上传9张图片')
      return false
    }

    // 前面传的是图片
    if (!allowedImagesTypes.includes(rawFile.type)) {
      disabled.value = false
      ElMessage.error('仅支持上传图片')
      return false
    }
  } else if (acceptType.value === '2') {
    if (fileList.value.length >= 3) {
      disabled.value = false
      ElMessage.error('最大可以上传3份文件')
      return false
    }

    // 前面传的是文档
    if (!allowedDocumentTypes.includes(rawFile.type)) {
      disabled.value = false
      ElMessage.error('请上传doc或pdf格式文件')
      return false
    }
  }

  if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('文件大小不能大于10MB!')
    disabled.value = false
    return false
  }

  disabled.value = true
}

const handleError = () => {
  disabled.value = false
}

watch(
  () => extension.value,
  (newVal) => {
    if (newVal === 'png' || newVal === 'gif' || newVal === 'jpeg' || newVal === 'jpg') {
      acceptType.value = '1' // 图片
    } else if (newVal === 'doc' || newVal === 'docx' || newVal === 'pdf') {
      acceptType.value = '2' // 文档
    }
  }
)
</script>
