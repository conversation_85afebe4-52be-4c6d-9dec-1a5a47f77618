<template>
  <div>
    <div class="flex flex-wrap">
      <div>
        <upload ref="uploadRef" :accept="'.pdf,.mp4'" :acceptText="'.pdf,.mp4'" v-model="uploadFiles"
          @change="handleFileChange" @uploading="updateUploadingStatus" @success="handleLocalUploadSuccess" />
      </div>
    </div>

    <div v-if="fileList.length > 0 && isShow" class="mb-3">
      <div v-for="(item, index) in fileList" :key="index" class="file-item">
        <div class="file-info" @click="perview(item)">
          <span class="file-name" :title="getFileName(item)">{{ getFileName(item) }}</span>
          <div v-if="item.progress !== undefined && item.progress < 100" class="file-progress">
            <el-progress :percentage="item.progress" :show-text="false" />
            <span class="progress-text">{{ item.progress }}%</span>
          </div>
        </div>
        <el-button plain class="delete-btn" :disabled="item.progress !== undefined && item.progress < 100"
          @click="handleRemove(item, index)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import upload from '../upload/upload.vue'
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 课程中心用
  isShow: {
    type: Boolean,
    default: true,
  },
  // 文件上传用
  isShowBtn: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['update:modelValue', 'change', 'uploading', 'remove'])

const fileList = ref<any[]>(props.modelValue || [])
const uploadFiles = ref([])
const isUploading = ref(false)

// 引用子组件
const selecFileListRef = ref()

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newVal) => {
    fileList.value = newVal
  },
  { immediate: true }
)

// 处理文件变更
const handleFileChange = (files: any[]) => {
  let newFileList = [...fileList.value]
  files.forEach((file) => {
    // 使用tempId来识别正在上传的同一文件
    if (file.tempId) {
      const existingFileIndex = newFileList.findIndex((existing) => existing.tempId === file.tempId)
      if (existingFileIndex >= 0) {
        newFileList[existingFileIndex].progress = file.progress
      } else {
        // 将临时文件添加到列表
        newFileList.push(file)
      }
    } else {
      const existingFileIndex = newFileList.findIndex(
        (existing) => existing.fileName === file.fileName && existing.fileSize === file.fileSize
      )

      if (existingFileIndex >= 0) {
        if (file.url && file.id) {
          newFileList[existingFileIndex] = file
        } else {
          newFileList[existingFileIndex].progress = file.progress
        }
      } else {
        newFileList.push(file)
      }
    }
  })

  fileList.value = newFileList
  emit('update:modelValue', fileList.value)
  emit('change', fileList.value)
}

// 添加处理本地上传成功的方法
const uploadRef = ref(null)
const handleLocalUploadSuccess = (response: any, fileData: any) => {
  if (response && response.code === 200) {
    // 查找临时文件
    const existingIndex = fileList.value.findIndex((file) => file.tempId === fileData.tempId)
    if (existingIndex >= 0) {
      // 替换临时文件为完成的文件，但保留在列表中的相同位置
      fileList.value.splice(existingIndex, 1, fileData)
    } else {
      // 如果没找到临时文件，直接添加
      fileList.value.push(fileData)
    }

    emit('update:modelValue', fileList.value)
    emit('change', fileList.value)
  }
}

// 更新上传状态
const updateUploadingStatus = (status: boolean) => {
  isUploading.value = status
  emit('uploading', status)
}

// 删除文件
const handleRemove = (item: any, index: number) => {
  fileList.value.splice(index, 1)
  if (!item.businessType) {
    uploadFiles.value = uploadFiles.value.filter((file: any) => file.id !== item.id)
  }
  emit('update:modelValue', fileList.value)
  emit('change', fileList.value)
}


// 根据业务类型获取文件名
const getFileName = (file: any) => {
  console.log(file, 'file')
  switch (file.businessType) {
    case 'law':
      return (
        file.lawsRegulationsName ||
        file.standardSpecificationName ||
        file.rulesRegulationsName ||
        file.operatingProceduresName
      )
    case 'emergency':
      return file.planName
    case 'courseware':
      return file.fileName
    default:
      return file.fileName || file.name
  }
}



// 预览文件
const perview = (item: any) => {
  console.log(item)
}

defineExpose({
  fileList,
  uploadRef: ref(null),
  handleRemove,
})
</script>

<style scoped lang="scss"></style>
