<!--
 * @Author: jingjf <EMAIL>
 * @Date: 2024-07-09 17:11:46
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 17:24:39
 * @FilePath: \angang-edu-web\src\components\public\tableList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="w-full h-full table-list flex flex-col">
    <header v-if="showHeader">
      <el-row :gutter="20">
        <slot name="header"></slot>
      </el-row>
      <div class="right-content">
        <slot name="right"></slot>
      </div>
    </header>
    <div class="table-list_wrap w-full flex-1 overflow-hidden">
      <el-table
        @row-click="rowClick"
        v-bind="$attrs"
        :data="data"
        v-if="!userDefined"
        stripe
        ref="tableRef"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
        highlight-current-row
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        :row-key="getRowKeys"
      >
        <el-table-column
          v-for="(item, index) in props.columns"
          :label="item.label"
          :prop="item.prop"
          :key="index"
          show-overflow-tooltip
        ></el-table-column>
        <slot></slot>
        <template v-slot:empty>
          <div class="h-200px">
            <no-data></no-data>
          </div>
        </template>
      </el-table>
      <slot v-else></slot>
    </div>
    <div class="flex justify-end pb-0px page-w" v-if="pageFlag">
      <el-pagination
        @current-change="currentChange"
        v-model:page-size="pageModel.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pageModel.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :total="pageModel.total"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface PageModel {
  pageNo: number
  pageSize: number
  total: number
}
interface Props {
  columns: any
  data: any[]
  pageModel: PageModel
  loading: boolean
  showHeader: boolean
  userDefined: boolean
  pageFlag: boolean
  pageNumber: number
}

const props = withDefaults(defineProps<Props>(), {
  pageModel: () => {
    return {
      pageNo: 1,
      pageSize: 20,
      total: 0
    }
  },
  columns: [],
  loading: false,
  showHeader: true,
  userDefined: false,
  pageFlag: true,
  pageNumber: 20
})

const emits = defineEmits(['current-change', 'row-click', 'sort-change', 'selection-change', 'size-change'])

const tableRef = ref()

function currentChange(val) {
  emits('current-change', val)
}

function handleSizeChange(val) {
  emits('size-change', val)
}
function handleSelectionChange(val) {
  emits('selection-change', val)
}
function rowClick(val) {
  emits('row-click', val)
}

const sortChange = (val) => {
  emits('sort-change', val)
}

function toggleSelectionChild(rows: any) {
  if (rows) {
    rows.forEach((row) => {
      tableRef.value!.toggleRowSelection(row, undefined)
    })
  } else {
    tableRef.value!.clearSelection()
  }
}

//指定key值,数据更新之后保留之前选中的数据
function getRowKeys(row) {
  return row.id
}

defineExpose({
  getTableInstance() {
    return tableRef.value
  },
  toggleSelectionChild
})
</script>

<script lang="ts">
export default {
  name: 'tableList'
}
</script>

<style lang="scss">
.table-list {
  table-layout: fixed;
  header {
    background: white;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    position: relative;

    .right-content {
      position: absolute;
      right: 20px;
      bottom: 20px;
      margin-top: 20px;
    }

    .el-row {
      margin-bottom: 0;
    }
  }

  .el-table__inner-wrapper {
    display: flex;
    flex-direction: column;

    .el-table__header-wrapper {
      .el-table__header {
        .el-table__cell {
          // background: #f5f6fa;
          background-color: rgba(245, 246, 249, 1); /* 表头背景色 */
          color: #606266; /* 表头字体颜色 */
          font-size: 14px; /* 表头字体大小 */
          height: 48px;

          .cell {
            color: #606266;
          }
        }
      }
    }

    .el-table__body-wrapper {
      flex: 1;
    }
  }

  .el-table__body {
    .el-table__row {
      // &:hover {

      //     // background: #e6f7ff !important;
      //     td {
      //         background: #e6f7ff !important;
      //     }
      // }

      .el-table__cell {
        color: #333333;
        border-bottom: none;
      }
    }

    // .el-table__row--striped {
    //     .el-table__cell {
    //         background: #f8faff !important;
    //     }
    // }
  }

  .table-list_wrap {
    background: white;
    padding: 0 20px;
    box-sizing: border-box;
    padding-bottom: 20px;

    .el-table {
      height: 100%;

      .el-table__inner-wrapper,
      .el-table__body-wrapper {
        height: 100%;
      }
    }
  }
  .pagination-wrap {
    display: flex;
    justify-content: end;
  }
  // .w_page_num {
  //   display: flex;
  //   justify-content: end;
  // }
  // .pagination-wrap {
  //   .el-pagination {
  //     .el-input {
  //       .el-input__inner {
  //         padding-left: 0 !important;
  //       }
  //     }

  //     .el-select {
  //       height: 32px;
  //     }
  //   }
  // }
}

.el-table__body tr.current-row > td {
  color: #28a458;
  background: #e6f7ff !important;
}
</style>

<style>
.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
  margin-bottom: 20px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
.page-w {
  margin: 0 20px;
}
</style>
