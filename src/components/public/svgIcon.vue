<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-11 16:10:29
 * @FilePath: /ehs-partner-mgr/src/components/public/svgIcon.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <svg :style="svgClass">
    <use :xlink:href="symbolId" :fill="color" />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '#527CFF',
  },
  size: {
    type: Number,
    default: 32,
  },
  height: {
    type: Number,
    defualt: undefined,
  },
  width: {
    type: Number,
    default: undefined,
  },
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)

const svgClass = computed(() => {
  const w = props.width,
    h = props.height,
    s = props.size

  if (w && h) {
    return {
      height: h + 'px',
      width: w + 'px',
    }
  }
  return {
    height: s + 'px',
    width: s + 'px',
  }
})
</script>

<script lang="ts">
export default {
  name: 'svgIcon',
}
</script>
