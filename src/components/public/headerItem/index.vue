<template>
  <el-col :span="props.span" :style="props.style" class="form_item">
    <div class="header-item items-center" :style="{ marginBottom: `${marginBottom}px`, padding: '0 10px' }">
      <div class="header-item_label" v-show="props.title !== ''">
        {{ props.title }}
      </div>
      <div class="header-item_control flex-1">
        <slot></slot>
      </div>
    </div>
  </el-col>
</template>

<script lang="ts" setup>
interface Props {
  span?: number
  style?: string
  title: string
  marginBottom?: number
}
const props = withDefaults(defineProps<Props>(), {
  span: 6,
  style: '',
  title: '',
  marginBottom: 20,
})
</script>

<script lang="ts">
export default {
  name: 'headerItem',
}
</script>

<style lang="scss">
.header-item {
  display: flex;
  margin-bottom: 20px;
  height: 32px;

  .header-item_label {
    text-align: right;
    position: relative;
    color: #333333;
    min-width: 5em;
    &::after {
      content: ':';
      color: #606266;
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
    }
  }

  .header-item_control {
    > * {
      width: 100% !important;
      height: 32px !important;
    }
  }

  // .el-input__inner {
  //   font-size: 16px;
  // }
}
</style>
