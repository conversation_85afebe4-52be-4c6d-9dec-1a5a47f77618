<template>
  <div>
    <el-upload ref="uploader" v-if="!fileInfo.url"
      accept="video/avi,video/dat,video/mkv,video/flv,video/vob,video/mp4,application/pdf" :action="uploadUrl"
      :on-success="handleAvatarSuccess" :on-error="handleAvatarError" :on-progress="handleAvatarProgress"
      :http-request="uploadFile" :before-upload="beforeAvatarUpload" :before-remove="beforeRemove" :limit="1" drag
      v-model:file-list="fileList" :show-file-list="false">
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip">上传文件大小不能超过 2G</div>
      <el-button v-if="uploading" type="text" @click="cancelUpload">取消上传</el-button>
    </el-upload>

    <div v-else class="video-box">
      <template v-if="fileInfo.isPdf">
        <div class="pdf-name mb-[20px]">{{ fileInfo.fileName }}</div>
        <div class="video-del" @click="onRemove">
          <el-icon>
            <Delete />
          </el-icon>
        </div>
      </template>
      <template v-else>
        <video style="width: 100%" ref="video" :src="fileInfo.url" controls class="mb-[20px]"></video>
        <div class="video-del" @click="onRemove">
          <el-icon>
            <Delete />
          </el-icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import Base from '@/plugins/lib/base64.js'
import aaabb from '@/plugins/lib/crypto1/crypto/crypto2.js'
import { Delete } from '@element-plus/icons-vue'
import OSS from 'ali-oss'
import { ElMessage } from 'element-plus'
import { defineEmits, defineProps, nextTick, ref, watch } from 'vue'

const Base64 = Base.Base64
const Crypto = aaabb.Crypto
const fileList = ref([])

const props = defineProps(['defaultUrl'])
const emit = defineEmits(['remove', 'getDuration', 'success', 'getImgUrl', 'fileTypeChange'])

// 响应式数据
const uploadUrl = ref('')
const uploader = ref(null)
const client = ref(null)
const uploading = ref(false)
const fileInfo = ref({
  url: props.defaultUrl || '',
  isPdf: false,
  fileName: '',
})

const state = {
  accessid: 'LTAI5tHyPTyyRqaTJvKxzdyB',
  accesssecret: '******************************',
  bucket: 'gs-edu001',
  relativePath: 'image/',
  region: 'oss-cn-hangzhou',
  size: 1024 * 1024 * 1024 * 2, //文件大小
}

// 监听props
watch(
  () => props.defaultUrl,
  (newVal) => {
    if (newVal) {
      const isPdfFile = newVal.toLowerCase().endsWith('.pdf')
      fileInfo.value = {
        url: newVal,
        isPdf: isPdfFile,
        fileName: newVal.split('/').pop() || '',
      }
    }
  }
)

const beforeRemove = () => {
  if (client.value) client.value.abortMultipartUpload()
  return false
}

const onRemove = () => {
  fileInfo.value = {
    url: '',
    isPdf: false,
    fileName: '',
  }
  emit('remove', {
    isPdf: fileInfo.value.isPdf,
    fileName: fileInfo.value.fileName,
  })
}
const handleAvatarSuccess = (res, file) => {
  if (res.res.statusCode == 200) {
    // const fileUrl = `https://${state.bucket}.${state.region}.aliyuncs.com/${res.name}`
    // cdn地址替换
    const fileUrl = `https://cdn-agjp.tanzervas.com/${res.name}`
    const isPdfFile = file.raw.type === 'application/pdf'

    fileInfo.value = {
      url: fileUrl,
      isPdf: isPdfFile,
      fileName: file.name,
    }

    // 发送文件类型变更事件
    emit('fileTypeChange', {
      isPdf: isPdfFile,
      fileName: file.name,
    })

    if (file.raw.type.startsWith('video/')) {
      nextTick(() => {
        const audio = new Audio(fileUrl)
        audio.addEventListener('loadedmetadata', () => {
          emit('getDuration', Math.round(audio.duration))
          emit('success', {
            url: fileUrl,
            size: file.size,
            isPdf: isPdfFile,
            fileName: file.name,
            duration: Math.round(audio.duration),
          })
        })
      })
    } else {
      emit('success', {
        url: fileUrl,
        size: file.size,
        isPdf: isPdfFile,
        fileName: file.name,
      })
    }
  } else {
    ElMessage.error(res.msg || '文件上传失败')
  }
}

const handleAvatarError = () => {
  uploading.value = false
  ElMessage.error('文件上传失败')
}

const handleAvatarProgress = (file) => {
  // 上传进度处理
  console.log('file', file)
}
const isValidType = ref(true)
const beforeAvatarUpload = (file) => {
  console.log('file', file)
  // 上传前处理
  isValidType.value = file.name.toLowerCase().endsWith('.pdf') || file.name.toLowerCase().endsWith('.mp4')
  if (!isValidType.value) {
    fileList.value = []
    console.log('fileList.value', fileList.value)
    ElMessage.error('请上传PDF文件或MP4文件')
    return false
  }
  return true
}

const cancelUpload = () => {
  if (client.value) {
    client.value.cancel()
    uploading.value = false
    // 清除文件信息
    fileInfo.value = {
      url: '',
      isPdf: false,
      fileName: '',
    }
    // 重置上传组件
    uploader.value?.clearFiles()
    ElMessage.info('已取消上传')
  }
}

const uploadFile = async (option) => {
  try {
    uploading.value = true
    const policyText = {
      expiration: '2022-01-01T12:00:00.000Z',
      conditions: [['content-length-range', 0, state.size]],
    }

    const message = Base64.encode(JSON.stringify(policyText))
    const bytes = Crypto.HMAC(Crypto.SHA1, message, state.accesssecret, {
      asBytes: true,
    })
    const signature = Crypto.util.bytesToBase64(bytes)

    const ossData = {
      relativePath: state.relativePath,
      policy: Base64.encode(JSON.stringify(policyText)),
      accessid: state.accessid,
      accesssecret: state.accesssecret,
      bucket: state.bucket,
      signature: signature,
    }

    client.value = OSS({
      accessKeyId: ossData.accessid,
      accessKeySecret: ossData.accesssecret,
      bucket: ossData.bucket,
      signature: ossData.signature,
      region: state.region,
    })

    const file = option.file
    const point = file.name.lastIndexOf('.')
    const suffix = file.name.substr(point)
    const fileNames = `${parseInt(Math.random() * 10000)}${new Date().getTime()}${parseInt(Math.random() * 10000)}${suffix}`

    const ret = await client.value.multipartUpload(ossData.relativePath + fileNames, file, {
      progress: async (p) => {
        option.onProgress({ percent: p * 100 })
      },
      cancelFlag: true,
    })

    if (ret.res.statusCode == 200) {
      uploading.value = false
      option.onSuccess(ret)
      return ret
    } else {
      uploading.value = false
      // option.onError('上传失败')
    }
  } catch (error) {
    uploading.value = false
    console.error(error)
    // option.onError('上传失败')
    // ElMessage.error(error.message)
  }
}

watch(
  () => isValidType.value,
  (newVal) => {
    console.log('newVal', newVal)
    if (!newVal) {
      uploader.value?.clearFiles()
    }
  },
  { immediate: true, deep: true }
)

defineExpose({
  uploadFile,
  handleAvatarSuccess,
})
</script>

<style lang="scss" scoped>
.video-box {
  position: relative;

  &:hover .video-del {
    display: block;
  }
}

.video-del {
  display: none;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 19px;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 0px 6px;

  &:hover {
    // background: rgba(0,0,0,0.7);
  }
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100% !important;
}

.pdf-name {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  word-break: break-all;
}

.file-item {
  margin-bottom: 10px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
