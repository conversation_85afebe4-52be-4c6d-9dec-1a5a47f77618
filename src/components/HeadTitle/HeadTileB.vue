<template>
  <div class="title">
    <div class="vertical">{{ props.title }}</div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})
defineOptions({ name: 'HeadTileB' })
</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  color: #242526;

  .vertical {
    padding-left: 51px;
    width: 100%;
    height: 42px;
    background: url('./assets/header.png') no-repeat;
    background-size: 100% 100%;
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    font-size: 24px;
    color: #ffffff;
    line-height: 30px;
    text-shadow: 1px 3px 2px rgba(0, 0, 0, 0.4);
    font-synthesis: style;
    font-style: italic;
  }
}
</style>
