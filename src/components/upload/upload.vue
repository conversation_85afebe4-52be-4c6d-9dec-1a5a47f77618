<template>
  <div class="file-uploader">
    <!-- OSS上传方式 -->
    <div v-if="isOssUpload">
      <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" :accept="accept" />
      <div
        class="w_file_btn"
        @click="triggerFileUpload"
        v-if="fileList.length < maxCount"
        :class="{ disabled: isUploading }"
      >
        {{ isUploading ? '上传中...' : '+ 添加培训资料' }}
      </div>

      <div style="display: none">
        <my-video
          ref="hiddenVideoRef"
          @success="handleHiddenVideoSuccess"
          @remove="handleHiddenVideoRemove"
          @getDuration="handleHiddenVideoGetDuration"
          @fileTypeChange="handleHiddenVideoFileTypeChange"
        />
      </div>
    </div>

    <!-- 本地上传方式 -->
    <div v-if="isLocalUpload">
      <el-upload
        :accept="accept"
        class="avatar-uploader"
        :limit="maxCount"
        v-model:file-list="localFileList"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :on-progress="handleProgress"
        :on-change="handleLocalFileChange"
        name="uploadfile"
        :action="url"
        :disabled="isUploading"
        :show-file-list="false"
        :auto-upload="true"
      >
        <div>
          <div class="w_file_btn" v-if="localFileList.length < maxCount" :class="{ disabled: isUploading }">
            {{ isUploading ? '上传中...' : '+ 添加培训资料' }}
          </div>
        </div>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'
import config from '~/config'
import MyVideo from '../video/myVideo.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 600
  },
  maxSize: {
    type: Number,
    default: 500
  },
  accept: {
    type: String,
    default: '.png,.jpg,.jpeg,.pdf,.doc,.docx,.mp4'
  },
  acceptText: {
    type: String,
    default: 'png，jpg，pdf，doc，docx，mp4'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'uploading', 'success'])

const fileList = ref(props.modelValue || [])
const localFileList = ref([])
const fileInputRef = ref(null)
const hiddenVideoRef = ref(null)
const url = ref(config.update_file + '/file/uploadfile') // 上传路径
const isUploading = ref(false)

// 上传类型
const uploadType = window.$SYS_CFG.uploadType
const isLocalUpload = computed(() => uploadType === 'wych')
const isOssUpload = computed(() => uploadType === 'oss')

// 同步外部和内部的文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      fileList.value = newVal
    }
  },
  { deep: true }
)

watch(
  fileList,
  (val) => {
    emit('update:modelValue', val)
    emit('change', val)
  },
  { immediate: true, deep: true }
)

// 触发文件选择
const triggerFileUpload = () => {
  if (fileList.value.length >= props.maxCount) {
    ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
    return
  }
  fileInputRef.value.click()
}

// 文件选择变化处理
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件类型和大小
  if (!validateFile(file)) {
    return
  }

  // 添加一个带进度的文件项
  const fileIndex = fileList.value.length
  fileList.value.push({
    fileName: file.name,
    fileSize: file.size,
    progress: 0
  })

  // 通知父组件上传状态变更
  isUploading.value = true
  emit('uploading', true)

  // 使用隐藏的my-video组件上传文件
  nextTick(() => {
    if (hiddenVideoRef.value) {
      const option = {
        file: file,
        onProgress: (event) => {
          // 更新上传进度
          if (event.percent) {
            fileList.value[fileIndex].progress = Math.floor(event.percent)
          }
        },
        onSuccess: (res) => {
          hiddenVideoRef.value.handleAvatarSuccess(res, {
            raw: file,
            name: file.name,
            size: file.size
          })
        },
        onError: () => {
          // 上传失败时移除该文件
          fileList.value.splice(fileIndex, 1)
          isUploading.value = false
          emit('uploading', false)
          ElMessage.error('文件上传失败')
        }
      }
      hiddenVideoRef.value.uploadFile(option)
    }
  })

  // 重置input以便再次选择同一文件
  event.target.value = ''
}

// 验证文件
const validateFile = (file) => {
  const acceptedTypes = props.accept
    .split(',')
    .map((type) => {
      const mimeTypes = {
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.mp4': 'video/mp4'
      }
      return mimeTypes[type]
    })
    .filter(Boolean)

  const isValidFormat = acceptedTypes.includes(file.type)
  if (!isValidFormat) {
    ElMessage.error(`仅支持${props.acceptText}格式文件!`)
    return false
  }
  return true
}
const durationList = ref()
// 处理隐藏视频组件获取时长
const handleHiddenVideoGetDuration = (duration) => {
  durationList.value = duration
}

// 处理隐藏视频组件上传成功
const handleHiddenVideoSuccess = (data) => {
  if (!data || !data.url) {
    ElMessage.error('文件上传失败')
    isUploading.value = false
    emit('uploading', false)
    return
  }

  // 查找正在上传的文件并更新信息
  const fileIndex = fileList.value.findIndex((file) => file.fileName === data.fileName && file.progress !== undefined)

  if (fileIndex !== -1) {
    // 更新已存在的文件项
    fileList.value[fileIndex] = {
      ...fileList.value[fileIndex],
      url: data.url,
      id: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf,
      progress: 100,
      duration: data.duration,
      fileId: data.url // 确保fileId字段存在
    }
  } else {
    // 添加新文件到列表
    fileList.value.push({
      url: data.url,
      id: data.url,
      fileId: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf,
      duration: data.duration
    })
  }

  isUploading.value = false
  emit('uploading', false)
  ElMessage({
    message: '上传文件成功',
    type: 'success'
  })
}

// 本地上传相关方法
const handleExceed = () => {
  ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
}

// 处理本地文件变更，添加到临时列表显示进度
const handleLocalFileChange = (file) => {
  if (file.status === 'ready') {
    // 添加一个带进度的文件项用于显示
    const tempFile = {
      fileName: file.name,
      name: file.name,
      fileSize: file.size,
      progress: 0,
      tempId: file.uid // 使用uid作为临时标识
    }
    emit('change', [tempFile])
  }
}

// 处理上传进度
const handleProgress = (event, file) => {
  // 构建进度文件对象
  const progressFile = {
    fileName: file.name,
    name: file.name,
    fileSize: file.size,
    // 限制进度最大为99%，只有在handleSuccess时才显示100%
    progress: Math.min(Math.floor(event.percent), 99),
    tempId: file.uid
  }
  // 发送进度更新
  emit('change', [progressFile])
}

const beforeUpload = (rawFile) => {
  if (validateFile(rawFile)) {
    isUploading.value = true
    emit('uploading', true)
    return true
  }
  return false
}

const handleSuccess = (response, file) => {
  isUploading.value = false
  emit('uploading', false)

  if (response && response.code === 200) {
    // 先发送进度100%的状态更新
    const progressFile = {
      fileName: file.name,
      name: file.name,
      fileSize: file.size,
      progress: 100,
      tempId: file.uid
    }
    emit('change', [progressFile])

    // 延迟小段时间后发送完整的文件数据，确保进度条动画完成
    let duration = 0
    if (response.data.suffix.includes('mp4')) {
      let url = URL.createObjectURL(file.raw)
      let audioElement = new Audio(url)

      audioElement.addEventListener('loadedmetadata', function () {
        duration = parseInt(audioElement.duration) //时长为秒，取整
      })
    }
    setTimeout(() => {
      // 构建完整的文件对象
      const fileData = {
        id: response.data.id,
        fileId: response.data.id,
        fileName: response.data.fileName,
        fileSize: parseInt(response.data.fileSize),
        url: response.data.filePath,
        businessType: '',
        progress: 100,
        tempId: file.uid,
        isPdf: response.data.suffix.includes('pdf'),
        duration: duration || 0
      }

      // 清空本地文件列表，避免重复
      localFileList.value = []

      // 发送成功事件，传递处理过的文件数据
      emit('success', response, fileData)
    }, 200)
  } else {
    ElMessage.error('文件上传失败')
  }

  ElMessage({
    message: '上传文件成功',
    type: 'success'
  })
}

// 处理隐藏视频组件的事件
const handleHiddenVideoRemove = () => {
  // 处理隐藏视频组件的移除事件
}

const handleHiddenVideoFileTypeChange = () => {
  // 处理文件类型变化
}

// 获取上传文件ID列表
const getFileIds = () => {
  if (isLocalUpload.value && localFileList.value.length) {
    return localFileList.value
      .map((item) => item.response?.data?.id)
      .filter(Boolean)
      .join(',')
  } else if (isOssUpload.value && fileList.value.length) {
    return fileList.value
      .map((item) => item.id)
      .filter(Boolean)
      .join(',')
  }
}

// 重置
const reset = () => {
  fileList.value = []
  localFileList.value = []
}

defineExpose({
  getFileIds,
  reset
})
</script>

<style lang="scss" scoped>
.file-uploader {
  .w_file_btn {
    width: 128px;
    padding: 0 10px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    color: #527cff;

    &.disabled {
      cursor: not-allowed;
    }
  }
}
</style>
