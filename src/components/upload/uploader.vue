<template>
  <div class="file-uploader">
    <!-- OSS上传方式 -->
    <!-- <div v-if="!isOssUpload">
      <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" :accept="accept" />
      <el-button @click="triggerFileUpload" v-if="fileList.length < maxCount" class="w_file_btn"> 选择文件 </el-button>
      <div class="w_file_t">支持上传{{ maxCount }}个文件，支持{{ acceptText }}等格式，大小不超过{{ maxSize }}MB</div>

      <div class="file-list" v-if="fileList.length > 0">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <div class="text-[#527cff] cursor-pointer" @click="previewFile(item)">
            {{ item.fileName || '未命名文件' }}
          </div>
          <div v-if="item.progress !== undefined && item.progress < 100" class="file-progress">
            <el-progress :percentage="item.progress" :show-text="false" />
            <span class="progress-text">{{ item.progress }}%</span>
          </div>
          <el-button type="text" @click="handleFileRemove(index)">删除</el-button>
        </div>
      </div>

      <div style="display: none">
        <my-video
          ref="hiddenVideoRef"
          @success="handleHiddenVideoSuccess"
          @remove="handleHiddenVideoRemove"
          @fileTypeChange="handleHiddenVideoFileTypeChange"
        />
      </div>
    </div> -->

    <!-- 本地上传方式 -->
    <div>
      <el-upload
        :accept="accept"
        class="avatar-uploader"
        :limit="maxCount"
        v-model:file-list="localFileList"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        name="uploadfile"
        :action="url"
        :disabled="isUploading"
        multiple
      >
        <div>
          <div v-if="showTip">
            <div class="w_file_btn" v-if="localFileList.length < maxCount" :class="{ disabled: isUploading }">
              {{ isUploading ? '上传中...' : '选择文件' }}
            </div>
          </div>
          <div v-else>
            <div class="w_file_btn" :class="{ disabled: isUploading }">
              {{ isUploading ? '上传中...' : '选择文件' }}
            </div>
          </div>
          <div class="w_file_t" v-if="showTip">
            支持同时上传{{ maxCount }}个文件，支持{{ acceptText }}等格式，大小不超过{{ maxSize }}MB
          </div>
        </div>
        <!-- <template #file="{ file }">
          <div class="el-upload-list__item-info">
            <div class="file-name-container" :title="file.name">
              <span class="truncate-filename">{{ file.name }}</span>
            </div>
          </div>
        </template> -->
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { nextTick, ref, watch } from 'vue'
import config from '~/config'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 6
  },
  maxSize: {
    type: Number,
    default: 10
  },
  accept: {
    type: String,
    default: '.png,.jpg,.jpeg,.pdf,.doc,.docx,.mp4'
  },
  acceptText: {
    type: String,
    default: 'png，jpg，jpeg，pdf，doc，docx，mp4'
  },
  showTip: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'uploading'])

const fileList = ref(props.modelValue || [])
const localFileList = ref([])
const fileInputRef = ref(null)
const hiddenVideoRef = ref(null)
const url = ref(config.update_file + '/file/uploadfile')
const isUploading = ref(false)

// 添加一个变量来跟踪验证失败的文件
const invalidFiles = ref(new Set())

// 同步外部和内部的文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      fileList.value = newVal
    }
  },
  { deep: true }
)

watch(
  fileList,
  (val) => {
    emit('update:modelValue', val)
    emit('change', val)
  },
  { immediate: true, deep: true }
)

watch(
  localFileList,
  (val) => {
    emit('change', val)
  },
  { immediate: true, deep: true }
)

// 监听上传状态变化并发出事件
watch(
  isUploading,
  (val) => {
    emit('uploading', val)
  },
  { immediate: true }
)

// 触发文件选择
const triggerFileUpload = () => {
  if (fileList.value.length >= props.maxCount) {
    ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
    return
  }
  fileInputRef.value.click()
}

// 文件选择变化处理
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件类型和大小
  if (!validateFile(file)) {
    return
  }

  // 添加一个带进度的文件项
  const fileIndex = fileList.value.length
  fileList.value.push({
    fileName: file.name,
    fileSize: file.size,
    progress: 0
  })

  // 使用隐藏的my-video组件上传文件
  nextTick(() => {
    if (hiddenVideoRef.value) {
      const option = {
        file: file,
        onProgress: (event) => {
          // 更新上传进度
          if (event.percent) {
            fileList.value[fileIndex].progress = Math.floor(event.percent)
          }
        },
        onSuccess: (res) => {
          hiddenVideoRef.value.handleAvatarSuccess(res, { raw: file, name: file.name, size: file.size })
        },
        onError: () => {
          // 上传失败时移除该文件
          fileList.value.splice(fileIndex, 1)
          ElMessage.error('文件上传失败')
        }
      }
      hiddenVideoRef.value.uploadFile(option)
    }
  })

  // 重置input以便再次选择同一文件
  event.target.value = ''
}

// 处理文件删除
const handleFileRemove = (index) => {
  fileList.value.splice(index, 1)
}

// 处理隐藏视频组件上传成功
const handleHiddenVideoSuccess = (data) => {
  if (!data || !data.url) {
    ElMessage.error('文件上传失败')
    return
  }

  // 查找正在上传的文件并更新信息
  const fileIndex = fileList.value.findIndex((file) => file.fileName === data.fileName && file.progress !== undefined)

  if (fileIndex !== -1) {
    // 更新已存在的文件项
    fileList.value[fileIndex] = {
      ...fileList.value[fileIndex],
      url: data.url,
      id: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf,
      progress: 100
    }
  } else {
    // 添加新文件到列表
    fileList.value.push({
      url: data.url,
      id: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf
    })
  }

  // ElMessage({
  //   message: '上传文件成功',
  //   type: 'success'
  // })
}

// 本地上传相关方法
// 验证文件
const validateFile = (file) => {
  const acceptedTypes = props.accept
    .split(',')
    .map((type) => {
      const mimeTypes = {
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.mp4': 'video/mp4',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
      return mimeTypes[type]
    })
    .filter(Boolean)

  const isValidFormat = acceptedTypes.includes(file.type)
  if (!isValidFormat) {
    ElMessage.error(`仅支持${props.acceptText}格式文件!`)
    return false
  } else if (file.size / 1024 / 1024 > props.maxSize) {
    ElMessage.error(`文件不能大于${props.maxSize}MB!`)
    return false
  }
  return true
}
const handleExceed = () => {
  ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
}

const beforeUpload = (rawFile) => {
  if (validateFile(rawFile)) {
    isUploading.value = true
    emit('uploading', true)
    return true
  }
  // 记录验证失败的文件
  invalidFiles.value.add(rawFile.uid || rawFile.name)
  return false
}

const handleSuccess = (response, file, fileList) => {
  // 检查是否所有文件都已上传完成
  const allUploaded = fileList.every((item) => item.status === 'success' || item.status === 'fail')

  if (allUploaded) {
    isUploading.value = false
    emit('uploading', false)
  }

  ElMessage({
    message: '上传文件成功',
    type: 'success'
  })
}

// 处理上传错误
const handleError = (error, file, fileList) => {
  // 检查是否所有文件都已处理完成
  const allProcessed = fileList.every((item) => item.status === 'success' || item.status === 'fail')

  if (allProcessed) {
    isUploading.value = false
    emit('uploading', false)
  }

  ElMessage.error('文件上传失败')
}

const handleRemove = (file, fileList) => {
  // 检查是否是验证失败的文件
  if (!invalidFiles.value.has(file.uid || file.name)) {
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
  } else {
    // 从集合中移除该文件
    invalidFiles.value.delete(file.uid || file.name)
  }
  localFileList.value = localFileList.value.filter((item) => item.uid !== file.uid)
}

// 预览文件
const previewFile = (item) => {
  if (item.url) {
    window.open(item.url, '_blank')
  }
}

// 处理隐藏视频组件的事件
const handleHiddenVideoRemove = () => {
  // 处理隐藏视频组件的移除事件
}

const handleHiddenVideoFileTypeChange = () => {
  // 处理文件类型变化
}

// 获取上传文件ID列表
const getFileIds = () => {
  // if (isLocalUpload.value && localFileList.value.length) {
  //   return localFileList.value
  //     .map((item) => item.response?.data?.id)
  //     .filter(Boolean)
  //     .join(',')
  // } else if (isOssUpload.value && fileList.value.length) {
  //   return fileList.value
  //     .map((item) => item.id)
  //     .filter(Boolean)
  //     .join(',')
  // }
  return localFileList.value
    .map((item) => item.response?.data?.id)
    .filter(Boolean)
    .join(',')
}

// 重置
const reset = () => {
  fileList.value = []
  localFileList.value = []
}

defineExpose({
  getFileIds,
  reset
})
</script>

<style lang="scss" scoped>
.file-uploader {
  // text-align: right;
  .file-list {
    margin-top: 10px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .file-progress {
        display: flex;
        align-items: center;
        width: 200px;
        margin: 0 10px;

        :deep(.el-progress) {
          width: 150px;
        }

        :deep(.el-progress-bar__inner) {
          background-color: #527cff;
        }

        .progress-text {
          margin-left: 5px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .w_file_btn {
    width: 96px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    background-color: #527cff;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;

    &.disabled {
      cursor: not-allowed;
    }
  }

  .w_file_t {
    color: #a8abb2;
    margin-top: 5px;
    font-size: 12px;
  }
}
:deep(.el-upload-list) {
  width: 500px;
}
:deep(.el-upload-list__item-name) {
  width: 300px;
}
</style>
