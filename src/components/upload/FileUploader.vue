<template>
  <div class="file-uploader">
    <!-- OSS上传方式 -->
    <!-- <div v-if="!isOssUpload">
      <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" :accept="accept" />
      <el-button @click="triggerFileUpload" v-if="fileList.length < maxCount" class="w_file_btn"> 选择文件 </el-button>
      <div class="w_file_t">支持上传{{ maxCount }}个文件，支持{{ acceptText }}等格式，大小不超过{{ maxSize }}MB</div>

      <div class="file-list" v-if="fileList.length > 0">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <div class="text-[#527cff] cursor-pointer" @click="previewFile(item)">
            {{ item.fileName || '未命名文件' }}
          </div>
          <div v-if="item.progress !== undefined && item.progress < 100" class="file-progress">
            <el-progress :percentage="item.progress" :show-text="false" />
            <span class="progress-text">{{ item.progress }}%</span>
          </div>
          <el-button  @click="handleFileRemove(index)">删除</el-button>
        </div>
      </div>

      <div style="display: none">
        <my-video
          ref="hiddenVideoRef"
          @success="handleHiddenVideoSuccess"
          @remove="handleHiddenVideoRemove"
          @fileTypeChange="handleHiddenVideoFileTypeChange"
        />
      </div>
    </div> -->

    <!-- 本地上传方式 -->
    <div>
      <el-upload
        :accept="accept"
        class="avatar-uploader"
        :limit="maxCount"
        v-model:file-list="localFileList"
        :on-exceed="handleExceed"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        name="uploadfile"
        :action="url"
        :disabled="isUploading"
        multiple
        :on-preview="handlePreview"
      >
        <div>
          <div class="w_file_btn" v-if="localFileList.length < maxCount" :class="{ disabled: isUploading }">
            {{ isUploading ? '上传中...' : '选择文件' }}
          </div>
          <div class="w_file_t">
            支持同时上传{{ maxCount }}个文件，支持{{ accept }}等格式，大小不超过{{ maxSize }}MB
          </div>
        </div>
        <!-- 自定义文件列表项 -->
        <template #file="{ file }">
          <div class="custom-file-item">
            <!-- 文件名 -->
            <span class="filename" :title="file.name">{{ file.name }}</span>

            <!-- 进度条 -->
            <div v-if="file.status === 'uploading'" class="progress-wrapper">
              <el-progress :percentage="file.percentage || 0" :stroke-width="4" :show-text="true" />
            </div>

            <!-- 操作按钮 -->
            <div class="actions">
              <el-button type="text" @click.stop="handlePreview(file)" :disabled="file.status === 'uploading'"
                >预览</el-button
              >
              <el-button type="text" @click.stop="handleRemove(file)" :disabled="file.status === 'uploading'"
                >删除</el-button
              >
            </div>
          </div>
        </template>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { computed, nextTick, ref, watch } from 'vue'
import config from '~/config'
import router from '~/router'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxCount: {
    type: Number,
    default: 6
  },
  maxSize: {
    type: Number,
    default: 10
  },
  accept: {
    type: String,
    default: '.png,.jpg,.jpeg,.pdf,.doc,.docx,.mp4'
  },
  acceptText: {
    type: String,
    default: 'png，jpg，jpeg，pdf，doc，docx，mp4'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'uploading'])

const fileList = ref(props.modelValue || [])
const localFileList = ref([])
const fileInputRef = ref(null)
const hiddenVideoRef = ref(null)
const url = ref(config.update_file + '/file/uploadfile')
const isUploading = ref(false)

// 添加一个变量来跟踪验证失败的文件
const invalidFiles = ref(new Set())

// 本地上传相关方法
// 验证文件
const validateFile = (file) => {
  const acceptedTypes = props.accept
    .split(',')
    .map((type) => {
      const mimeTypes = {
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.mp4': 'video/mp4'
      }
      return mimeTypes[type]
    })
    .filter(Boolean)

  const isValidFormat = acceptedTypes.includes(file.type)
  if (!isValidFormat) {
    ElMessage.error(`仅支持${props.accept}格式文件!`)
    return false
  } else if (file.size / 1024 / 1024 > props.maxSize) {
    ElMessage.error(`文件不能大于${props.maxSize}MB!`)
    return false
  }
  return true
}
const handleExceed = () => {
  ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
}

const beforeUpload = (rawFile) => {
  if (validateFile(rawFile)) {
    isUploading.value = true
    emit('uploading', true)
    return true
  }
  // 记录验证失败的文件
  invalidFiles.value.add(rawFile.uid || rawFile.name)
  return false
}

const handleSuccess = (response, file, fileList) => {
  // 检查是否所有文件都已上传完成
  const allUploaded = fileList.every((item) => item.status === 'success' || item.status === 'fail')

  if (allUploaded) {
    isUploading.value = false
    emit('uploading', false)
  }

  if (response.code === 200 && response.data) {
    // 确保文件有 url 属性用于预览
    const fileIndex = localFileList.value.findIndex((item) => item.uid === file.uid)
    if (fileIndex !== -1) {
      localFileList.value[fileIndex].url = config.downloadFileUrl + response.data.filePath
      localFileList.value[fileIndex].response = response
    }

    ElMessage({
      message: '上传文件成功',
      type: 'success'
    })
  }
}

const handlePreview = (file) => {
  console.log('预览文件:', file)

  // 获取文件URL
  let fileUrl = ''

  if (file.url) {
    fileUrl = file.url
  } else if (file.response?.data?.fileUrl) {
    fileUrl = file.response.data.fileUrl
  } else if (file.response?.data?.url) {
    fileUrl = file.response.data.url
  } else if (file.response?.data?.filePath) {
    fileUrl = config.downloadFileUrl + file.response.data.filePath
  }

  if (!fileUrl) {
    ElMessage.warning('无法预览该文件，URL不存在')
    return
  }

  const fileName = file.name || ''
  const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()

  if (['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(fileExt)) {
    if (!fileUrl.startsWith('http')) {
      fileUrl = config.downloadFileUrl + fileUrl
    }
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: fileUrl }
      }).href,
      '_blank'
    )
  } else if (['jpg', 'jpeg', 'png', 'gif', 'pdf', 'mp4'].includes(fileExt)) {
    if (!fileUrl.startsWith('http')) {
      fileUrl = config.downloadFileUrl + fileUrl
    }
    window.open(fileUrl, '_blank')
  }
  // 其他类型文件
  else {
    if (!fileUrl.startsWith('http')) {
      fileUrl = config.downloadFileUrl + fileUrl
    }
    window.open(fileUrl, '_blank')
  }
}

const handleRemove = (file, fileList) => {
  // 检查是否是验证失败的文件
  if (!invalidFiles.value.has(file.uid || file.name)) {
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
  } else {
    invalidFiles.value.delete(file.uid || file.name)
  }
  localFileList.value = localFileList.value.filter((item) => item.uid !== file.uid)
}

// 获取上传文件ID列表
const getFileIds = () => {
  // if (isLocalUpload.value && localFileList.value.length) {
  //   return localFileList.value
  //     .map((item) => item.response?.data?.id)
  //     .filter(Boolean)
  //     .join(',')
  // } else if (isOssUpload.value && fileList.value.length) {
  //   return fileList.value
  //     .map((item) => item.id)
  //     .filter(Boolean)
  //     .join(',')
  // }
  return localFileList.value
    .map((item) => item.response?.data?.id)
    .filter(Boolean)
    .join(',')
}

// 重置
const reset = () => {
  fileList.value = []
  localFileList.value = []
}
// 同步外部和内部的文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 处理接口返回的文件列表
      localFileList.value = newVal.map((file) => {
        // 保留原始标记
        const isOriginalFile = file.isOriginal || false

        return {
          name: file.fileName || file.name,
          url: file.fileUrl || file.filePath || file.url,
          uid: file.id || file.fileId || file.uid || Date.now(),
          status: 'success',
          fileId: file.id || file.fileId,
          response: file.response || { data: { id: file.id || file.fileId } },
          isOriginal: isOriginalFile,
          originalData: file.originalData || file
        }
      })
    }
  },
  { immediate: true, deep: true }
)

watch(
  fileList,
  (val) => {
    emit('update:modelValue', val)
    emit('change', val)
  },
  { immediate: true, deep: true }
)

watch(
  localFileList,
  (val) => {
    emit('change', val)
  },
  { immediate: true, deep: true }
)
watch(
  isUploading,
  (val) => {
    emit('uploading', val)
  },
  { immediate: true }
)
/**
 * oss上传方法
 */
// 触发文件选择
const triggerFileUpload = () => {
  if (fileList.value.length >= props.maxCount) {
    ElMessage.error(`超出最大上传文件数量(${props.maxCount})`)
    return
  }
  fileInputRef.value.click()
}

// 文件选择变化处理
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件类型和大小
  if (!validateFile(file)) {
    return
  }

  // 添加一个带进度的文件项
  const fileIndex = fileList.value.length
  fileList.value.push({
    fileName: file.name,
    fileSize: file.size,
    progress: 0
  })

  // 使用隐藏的my-video组件上传文件
  nextTick(() => {
    if (hiddenVideoRef.value) {
      const option = {
        file: file,
        onProgress: (event) => {
          // 更新上传进度
          if (event.percent) {
            fileList.value[fileIndex].progress = Math.floor(event.percent)
          }
        },
        onSuccess: (res) => {
          hiddenVideoRef.value.handleAvatarSuccess(res, { raw: file, name: file.name, size: file.size })
        },
        onError: () => {
          // 上传失败时移除该文件
          fileList.value.splice(fileIndex, 1)
          ElMessage.error('文件上传失败')
        }
      }
      hiddenVideoRef.value.uploadFile(option)
    }
  })

  // 重置input以便再次选择同一文件
  event.target.value = ''
}

// 处理文件删除
const handleFileRemove = (index) => {
  fileList.value.splice(index, 1)
}

// 处理隐藏视频组件上传成功
const handleHiddenVideoSuccess = (data) => {
  if (!data || !data.url) {
    ElMessage.error('文件上传失败')
    return
  }

  // 查找正在上传的文件并更新信息
  const fileIndex = fileList.value.findIndex((file) => file.fileName === data.fileName && file.progress !== undefined)

  if (fileIndex !== -1) {
    // 更新已存在的文件项
    fileList.value[fileIndex] = {
      ...fileList.value[fileIndex],
      url: data.url,
      id: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf,
      progress: 100
    }
  } else {
    // 添加新文件到列表
    fileList.value.push({
      url: data.url,
      id: data.url,
      fileName: data.fileName,
      fileSize: data.size,
      isPdf: data.isPdf
    })
  }

  // ElMessage({
  //   message: '上传文件成功',
  //   type: 'success'
  // })
}
// 预览文件
const previewFile = (item) => {
  if (item.url) {
    window.open(item.url, '_blank')
  }
}

// 处理隐藏视频组件的事件
const handleHiddenVideoRemove = () => {
  // 处理隐藏视频组件的移除事件
}

const handleHiddenVideoFileTypeChange = () => {
  // 处理文件类型变化
}
// 处理上传错误
const handleError = (error, file, fileList) => {
  // 检查是否所有文件都已处理完成
  const allProcessed = fileList.every((item) => item.status === 'success' || item.status === 'fail')

  if (allProcessed) {
    isUploading.value = false
    emit('uploading', false)
  }

  ElMessage.error('文件上传失败')
}

defineExpose({
  getFileIds,
  reset
})
</script>

<style lang="scss" scoped>
.file-uploader {
  // text-align: right;
  .file-list {
    margin-top: 10px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .file-progress {
        display: flex;
        align-items: center;
        width: 50px;
        margin: 0 10px;

        :deep(.el-progress-bar__inner) {
          background-color: #527cff;
        }

        .progress-text {
          margin-left: 5px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .w_file_btn {
    width: 96px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    background-color: #527cff;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;

    &.disabled {
      cursor: not-allowed;
    }
  }

  .w_file_t {
    color: #a8abb2;
    margin-top: 5px;
    font-size: 12px;
  }
}
:deep(.el-upload-list) {
  width: 500px;
}
:deep(.el-upload-list__item-name) {
  width: 300px;
}

.custom-file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  width: 100%;

  .filename {
    width: 200px; // 固定文件名宽度
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .progress-wrapper {
    width: 100px; // 固定进度条宽度
    margin: 0 40px;
    flex-shrink: 0;
  }

  .actions {
    margin-left: auto;
    white-space: nowrap;

    .el-button {
      padding: 0 8px;

      &:first-child {
        color: #409eff;
      }

      &:last-child {
        color: #f56c6c;
      }

      &[disabled] {
        color: #c0c4cc;
      }
    }
  }
}

:deep(.el-progress) {
  margin-top: 5px;
  width: 100px !important;
}

:deep(.el-progress__text) {
  font-size: 12px;
}
</style>
