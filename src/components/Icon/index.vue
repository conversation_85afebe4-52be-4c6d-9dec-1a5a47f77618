<template>
  <div></div>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue'
//修改Favicon的方法
const changeFavicon = (link) => {
  let $favicon: any = document.querySelector('link[rel="icon"]')
  if ($favicon !== null) {
    $favicon.href = link
  } else {
    $favicon = document.createElement('link')
    $favicon.rel = 'icon'
    $favicon.href = link
    document.head.appendChild($favicon)
  }
}
//根据传递的参数修改Favicon
function changeIco(type) {
  // 得到图标地址
  let iconUrl
  if (type == 'bd') {
    iconUrl = `./faviconbd.ico`
  } else {
    iconUrl = `./favicon.ico`
  }
  changeFavicon(iconUrl)
}
defineComponent({ name: 'Icon' })
</script>
