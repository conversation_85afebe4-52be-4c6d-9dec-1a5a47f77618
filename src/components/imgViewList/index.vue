vue 复制代码
<template>
  <div class="demo-image">
    <el-badge :value="imgsNumber" class="item" v-if="imgsNumber > 1" :offset="[-8, 8]" type="primary">
      <el-image
        :style="{ width, height }"
        :src="url"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="srcList"
        preview-teleported
        :initial-index="4"
        fit="cover"
      />
    </el-badge>
    <el-image
      v-else-if="imgsNumber != 0"
      :style="{ width, height }"
      :src="url"
      :zoom-rate="1.2"
      :max-scale="7"
      :min-scale="0.2"
      preview-teleported
      :preview-src-list="srcList"
      :initial-index="4"
      fit="cover"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import config from '~/config'

const props = defineProps({
  imgList: {
    type: Array,
    default: () => []
  },
  width: {
    type: String,
    default: 'auto'
  },
  height: {
    type: String,
    default: '50px'
  }
})
const srcList = ref<Record<string, any>[]>([])
const url = ref<any>('')
const imgsNumber = ref(0)

watch(
  () => props.imgList,
  (newVal: any) => {
    srcList.value = newVal.map((item: any) => {
      return config.downloadFileUrl + item.filePath
    })
    url.value = srcList.value[0] || ''
    imgsNumber.value = newVal.length
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<style scoped>
.demo-image__error .image-slot {
  font-size: 30px;
}
.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
.demo-image__error .el-image {
  width: 100%;
  height: 200px;
}
</style>
