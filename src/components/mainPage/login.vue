<template>
  <!-- <div class="">1111</div> -->
  <div class="login-view w-full h-full relative">
    <div class="side-img"></div>
    <div class="login-box absolute">
      <div class="corner left-0 top-0"></div>
      <div class="corner right-0 top-0 rotate-90"></div>
      <div class="corner left-0 bottom-0 -rotate-90"></div>
      <div class="corner right-0 bottom-0 rotate-180"></div>
      <div v-if="islogin">
        <p class="text-center text-34px mt-30px mb-60px font-hp-regular">用户登录</p>
        <el-input
          v-model="loginName"
          @keyup.enter="submit"
          :prefix-icon="UserFilled"
          placeholder="请输入账号"
        ></el-input>
        <el-input
          v-model="password"
          @keyup.enter="submit"
          type="password"
          show-password
          :prefix-icon="Lock"
          placeholder="请输入密码"
        ></el-input>
        <el-button :loading="loading" class="mb-30px mt-60px" type="primary" @click="submit">登录</el-button>
      </div>
      <div v-else>
        <div class="w-login-opt">
          <el-form :model="form" class="h-[100px]" :rules="rules" ref="formRef">
            <el-form-item label="请选择单位" class="w-full" prop="unitId">
              <el-select
                v-model="form.unitId"
                placeholder="请选择单位"
                size="large"
                style="width: 100%"
                clearable
                class="input-border-style"
              >
                <el-option v-for="item in options" :key="item.unitId" :label="item.unitName" :value="item.unitId" />
              </el-select>
            </el-form-item>
          </el-form>
          <div class="flex justify-center items-center">
            <el-button type="primary" class="w-150px" @click="unitSubmit">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UserFilled, Lock } from '@element-plus/icons-vue'
import { hex_md5 } from '@/common/md5'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import router from '~/router'
import type { FormRules } from 'element-plus'

const islogin = ref<boolean>(true)
const loginName = ref('')
const password = ref('')
const loading = ref(false)
const ui: any = useUserInfo()

let formRef = ref()
const saveToken = ref('')
const rules = reactive<FormRules<any>>({
  unitId: [{ required: true, message: '请选择单位', trigger: ['change', 'blur'] }]
})
const form = ref({
  unitId: ''
})

function submit() {
  let l = loginName.value,
    p = password.value

  if (l === '') {
    return ElMessage.error('账号不能为空')
  }

  if (p === '') {
    return ElMessage.error('密码不能为空')
  }

  p = hex_md5(p + 'true')

  loading.value = true
  // ElMessage.success('登录成功');
  //  setTimeout(() => {

  //     loading.value = false;
  //     router.push('/staging');
  // }, 800);

  $API
    .post({
      url: 'train-server/login/login',
      params: {
        client: 'WEB',
        loginName: l,
        password: p,
        sysCode: 'ag_edu'
      }
    })
    .then(async (res: any) => {
      loading.value = false
      if (res && res.code == '200') {
        islogin.value = false
        const d = res?.data
        ui.value = d
        ui.value.systemName = d?.systemName || ui.value.zhLogo === 'yanchang' ? '承包商管理系统' : '相关方管理系统'
        ui.value.resourceList = d?.coopResourceList || []
        sessionStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
        // router.push('/staging')
        console.log('+++++++++', d)
        saveToken.value = d.token
        // return
        getAuthList()
      }
    })
}

let options = ref<any[]>([])

function getAuthList() {
  $API
    .post({
      url: `train-server/login/getUserAuthUit`
    })
    .then((res: any) => {
      if (res && res.code == 'success') {
        options.value = res.data
      }
    })
    .finally(() => {})
}

async function unitSubmit() {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (!valid) return
    $API
      .post({
        url: 'train-server/login/getUnitDetailInfoWithToken',
        params: {
          orgCode: form.value.unitId,
          client: 'ag_inter_web'
        }
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          const d = res.data
          d.resourceVoList = res?.data?.resourceVoList.map((item) => {
            return {
              ...item,
              children: item.childrens
            }
          })
          ui.value = d
          ui.value.systemName = d?.systemName || ui.value.zhLogo === 'yanchang' ? '承包商管理系统' : '相关方管理系统'
          ui.value.coopResourceList = ui.value.resourceVoList || []
          ui.value.token = saveToken.value
          sessionStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
          // return
          goRouter(ui.value.resourceVoList)
          // router.push('/staging')
          // setTimeout(() => {
          //   window.location.reload()
          // }, 300)
          // sessionStorage.setItem('isreload', '1')
        }
      })
    // console.log('发送请求',formInline.value)
  })
}

function goRouter(routerList) {
  if (routerList.length == 0) {
    // return  router.push('/login')
    return router.push('/staging')
  }
  let goRouterObj = routerList[0]
  let goRouterUrlName = ''
  if (goRouterObj.childrens.length > 0) {
    goRouterUrlName = goRouterObj.childrens[0].resUrl
  } else {
    goRouterUrlName = goRouterObj.resUrl
  }
  router.push(goRouterUrlName)
  setTimeout(() => {
    window.location.reload()
  }, 400)
}

onMounted(() => {})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
.login-view {
  // background: url('@/assets/image/login_bg.png') no-repeat;
  background-size: 100% 100%;

  .side-img {
    width: 884px;
    height: 170px;
    // background: url('@/assets/image/login_main.png') no-repeat left center;
    background-size: 100% 100%;
    position: absolute;
    left: 5%;
    top: 50%;
    transform: translateY(-50%);
  }

  .logo-info {
    color: white;
    left: 50px;
    top: 30px;
  }

  .login-box {
    padding: 44px;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    color: #333;
    right: 10%;
    top: 50%;
    width: 480px;
    transform: translateY(-50%);

    .el-input {
      margin-bottom: 30px;

      .el-input__inner {
        height: 50px;
        line-height: 50px;
        font-size: 18px;
        // background: rgb(64 158 255 / 10%);
        // box-shadow: 0 0 0 2px rgb(0 83 149 / 50%);
        color: #999;

        &:focus {
          // box-shadow: 0 0 0 2px rgb(64 158 255 / 50%);
        }
      }
    }

    .el-button {
      width: 100%;
      height: 50px;
      line-height: 50px;

      > span {
        font-size: 20px;
      }
    }

    .corner {
      width: 40px;
      height: 40px;

      background-size: 100%;
      position: absolute;
    }
  }

  .to-record {
    position: absolute;
    left: 20px;
    bottom: 20px;
    color: white;
  }
}
</style>
