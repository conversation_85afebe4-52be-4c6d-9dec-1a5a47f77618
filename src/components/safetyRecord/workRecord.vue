<template>
  <div class="work-record">
    <!-- 统计数据骨架屏 -->
    <div v-if="loading" class="statistics">
      <el-skeleton-item v-for="i in 3" :key="i" class="stat-box skeleton-stat" variant="rect" />
    </div>
    <!-- 原统计数据 -->
    <div v-else class="statistics">
      <div class="stat-box" v-for="(stat, index) in statistics" :key="stat.label" :class="`stat-box-${index + 1}`">
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-value">{{ stat.value }}</div>
      </div>
    </div>

    <!-- 列表骨架屏 -->
    <div v-if="loading" class="record-list">
      <div v-for="i in 3" :key="i" class="record-item skeleton-item">
        <el-skeleton :rows="5" animated />
        <div class="action-row">
          <el-skeleton-item variant="rect" style="width: 100px; height: 32px" />
        </div>
      </div>
    </div>
    <!-- 原列表内容 -->
    <div v-else class="record-list">
      <el-scrollbar>
        <no-data v-if="!records.length"></no-data>
        <div class="record-item" v-else v-for="item in records" :key="item.operationNo">
          <div class="info-row">
            <span>作业时间：{{ item.timeRange }}</span>
          </div>
          <div class="info-row">
            <span>作业单位：{{ item.unit }}</span>
          </div>
          <div class="info-row">
            <span>作业位置：{{ item.location }}</span>
          </div>
          <div class="info-row">
            <span>作业编号：{{ item.number }}</span>
          </div>
          <div class="info-row">
            <span>作业等级：{{ item.level }}</span>
          </div>
          <div class="action-row">
            <div class="info-item">
              <div class="flex gap-2">
                <div v-if="item.operationStatus">
                  <div class="w_tag_b w-tag" v-if="item.operationStatus === '2'">申请中</div>
                  <div class="w_tag_l w-tag" v-if="item.operationStatus === '3'">作业中</div>
                  <div class="w_tag_s w-tag" v-if="item.operationStatus === '4'">已完成</div>
                </div>
                <div class="w_tag_l w-tag" v-if="item.operationTypeName">
                  {{ item.operationTypeName }}
                </div>
                <div class="w_tag_o w-tag">合规</div>
              </div>
            </div>
            <el-button type="primary" @click="viewWorkTicket(item.operationBaseId)"> 查看作业票 </el-button>
          </div>
        </div>
      </el-scrollbar>

      <!-- 添加分页组件 -->
      <!-- <div class="pagination-container" v-if="records.length">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          layout="total, sizes, prev, pager, next"
        />
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import $API from '@/common/api'
import config from '@/config'

const props = defineProps<{
  recordData: {
    stats: StatData
    list: WorkRecord[]
  }
  loading?: boolean
}>()

const emit = defineEmits(['update:page', 'update:size'])

// 添加分页相关的响应式变量
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => props.recordData?.list.length || 0)

// 处理分页事件
const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('update:size', currentPage.value, size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('update:page', page, pageSize.value)
}

interface WorkRecord {
  id?: string
  planId?: string
  planStartTime: string
  planEndTime: string
  unitName: string
  operationLocationPart: string
  operationNo: string
  operationTypeName: string
  operationStatus: string
  deviceCount?: string
  operationLevelName: string
}

interface StatData {
  operTotal: number // 作业总数
  operTodayTotal: number // 今日作业数
  operApplyTotal: number // 申请中数量
  operWorkTotal: number // 作业中数量
  operOverTotal: number // 已完成数量
  operOk: number // 合规数量
  hazardNum: number // 违规数量
}

// 添加默认值处理
const defaultStats: StatData = {
  operTotal: 0,
  operTodayTotal: 0,
  operApplyTotal: 0,
  operWorkTotal: 0,
  operOverTotal: 0,
  operOk: 0,
  hazardNum: 0
}

// 修改统计数据计算逻辑，添加默认值处理
const statistics = computed(() => {
  const stats = props.recordData?.stats || defaultStats
  return [
    {
      label: '作业总次数（次）',
      value: stats.operTotal
    },
    {
      label: '合规次数（次）',
      value: stats.operOk
    },
    {
      label: '隐患次数（次）',
      value: stats.hazardNum
    }
  ]
})

// 添加错误处理和数据验证
const records = computed(() => {
  const list = props.recordData?.list || []
  return list.map((item: WorkRecord) => {
    // 确保所有必要字段都有值，使用空字符串作为默认值
    return {
      ...item,
      timeRange: item.planStartTime && item.planEndTime ? `${item.planStartTime} ~ ${item.planEndTime}` : '--',
      unit: item.unitName || '--',
      location: item.operationLocationPart || '--',
      number: item.operationNo || '--',
      level: item.operationLevelName || '--'
    }
  })
})

// 查看作业票
const viewWorkTicket = async (id: string) => {
  // let res: any = await $API.post({
  //   url: 'edu-inter-server/workManage/getPdfFile',
  //   params: {
  //     operationBaseId: id
  //   }
  // })
  // if (res.data && res.code == '200') {
  // }
  let url = config.workPreview + id
  window.open(url, '_target')
}
</script>

<style lang="scss" scoped>
.work-record {
  .statistics {
    display: flex;
    gap: 20px;
    margin-bottom: 35px;

    .stat-box {
      flex: 1;
      height: 130px;
      padding: 25px 40px;

      &.stat-box-1 {
        background: url('@/assets/image/work-1.png') no-repeat;
        background-size: 100% 100%;
      }

      &.stat-box-2 {
        background: url('@/assets/image/work-2.png') no-repeat;
        background-size: 100% 100%;
      }

      &.stat-box-3 {
        background: url('@/assets/image/work-3.png') no-repeat;
        background-size: 100% 100%;
      }

      .stat-label {
        color: #ffffff;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .stat-value {
        color: #ffffff;
        font-size: 28px;
        font-weight: bold;
        margin-top: 20px;
      }
    }
  }

  .record-list {
    height: calc(100vh - 300px);
    position: relative;

    .record-item {
      border: 1px solid #ccc;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 4px;
      background: #f5f8ff;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }

      .info-row {
        margin-bottom: 18px;
        color: #606266;

        span {
          color: #606266;
        }
      }

      .action-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;

        .status-tags {
          .tag {
            background: #ecf5ff;
            color: #409eff;
            padding: 2px 8px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
          }
        }
      }
    }
    :deep(.el-scrollbar__wrap) {
      height: 100%;
    }
  }

  .empty-data {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    font-size: 14px;
  }

  // 添加骨架屏样式
  .skeleton-stat {
    flex: 1;
    height: 130px !important;
    background: #e4e7ed !important;
  }

  .skeleton-item {
    .el-skeleton {
      padding: 10px 0;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
.w-tag {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  border-radius: 4px;
}
.w_tag_l {
  background: #527cff;
}

.w_tag_b {
  background: rgba(80, 178, 125, 1);
}

.w_tag_s {
  background: rgba(145, 148, 151, 1);
}

.w_tag_o {
  background: rgba(233, 157, 66, 1);
}
</style>
