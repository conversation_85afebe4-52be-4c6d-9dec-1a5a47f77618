<template>
  <div class="basic-info h-full">
    <div class="content-box">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <div class="info-header">
            <el-skeleton-item variant="image" style="width: 120px; height: 120px; border-radius: 8px" />
            <div class="info-content" style="margin-left: 24px">
              <div class="basic-info-section">
                <el-skeleton-item v-for="i in 6" :key="i" variant="text" style="width: 80%; margin-bottom: 16px" />
              </div>
            </div>
          </div>
        </template>

        <template #default>
          <div class="section-title">个人基本信息</div>
          <div class="info-header">
            <div class="special-worker-badge" v-if="recordData.specialOperation === '1'">特种作业人员</div>
            <div class="avatar">
              <el-image
                :src="avatar[0] || defaultAvatar"
                alt="头像"
                :preview-src-list="avatar.length ? avatar : [defaultAvatar]"
                :preview-teleported="true"
                hide-on-click-modal
                class="w-full h-full"
              />
            </div>
            <div class="info-content">
              <div class="basic-info-section">
                <div class="info-column">
                  <div class="info-item">
                    <span class="label">姓名:</span>
                    <span>{{ recordData.name || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">当前单位:</span>
                    <span>{{ recordData.depart || '--' }}</span>
                  </div>
                  <div class="insurance-docs" v-if="recordData.insureFiles && recordData.insureFiles.length > 0">
                    <div class="sub-title">投保信息</div>
                    <div class="docs-container">
                      <el-icon class="arrow-left" @click="prevImage" v-show="currentIndex > 0">
                        <ArrowLeftBold />
                      </el-icon>

                      <div class="docs-wrapper">
                        <div class="docs-group" :style="{ transform: `translateX(-${currentIndex * (150 + 16)}px)` }">
                          <div v-for="(doc, index) in recordData.insureFiles" :key="index" class="doc-item">
                            <el-image
                              :src="config.downloadFileUrl + doc.filePath"
                              fit="cover"
                              :preview-src-list="insuranceUrls"
                              :initial-index="index"
                              :preview-teleported="true"
                              hide-on-click-modal
                            />
                          </div>
                        </div>
                      </div>

                      <el-icon
                        class="arrow-right"
                        @click="nextImage"
                        v-show="currentIndex < recordData.insureFiles.length - 3"
                      >
                        <ArrowRightBold />
                      </el-icon>
                    </div>
                  </div>
                </div>

                <div class="info-column">
                  <div class="info-item">
                    <span class="label">年龄:</span>
                    <span>{{ recordData.age ? recordData.age + '岁' : '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">部门:</span>
                    <span>{{ recordData.depart || '--' }}</span>
                  </div>
                </div>

                <div class="info-column">
                  <div class="info-item">
                    <span class="label">身份证号:</span>
                    <span>{{ recordData.idNumber || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">岗位:</span>
                    <span>{{ recordData.post || '--' }}</span>
                  </div>
                  <div class="id-photos" v-if="recordData.idFiles && recordData.idFiles.length > 0">
                    <div class="sub-title">身份证照片</div>
                    <div class="photos-container">
                      <div v-for="(photo, index) in recordData.idFiles" :key="photo.id" class="photo-item">
                        <el-image
                          :src="config.downloadFileUrl + photo.filePath"
                          fit="cover"
                          :preview-src-list="idPhotoUrls"
                          :initial-index="index"
                          :preview-teleported="true"
                          hide-on-click-modal
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <div class="work-history">
      <div class="section-title">工作履历</div>
      <el-skeleton :loading="loading" animated>
        <template #template>
          <div style="padding: 20px">
            <el-skeleton-item v-for="i in 5" :key="i" variant="text" style="width: 100%; margin-bottom: 16px" />
          </div>
        </template>

        <template #default>
          <el-table :data="recordData.workHistory" style="width: 100%">
            <el-table-column type="index" label="序号" align="left" width="70px" />
            <el-table-column
              prop="dserverUnitName"
              :label="ui.zhLogo === 'yanchang' ? '承包商单位' : '相关方单位'"
              align="center"
            />
            <el-table-column prop="dunitName" label="服务单位" align="center" />
            <el-table-column prop="dworkCount" label="作业次数" align="center" />
            <el-table-column prop="dhazardWorkCount" label="隐患作业次数" align="center" />
          </el-table>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, computed, watch } from 'vue'
import defaultAvatar from '@/assets/image/default_avatar.png'
import config from '@/config'
import { ArrowLeftBold, ArrowRightBold } from '@element-plus/icons-vue'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const props = defineProps({
  recordData: {
    type: Object,
    default: () => ({
      insureFiles: [],
      idFiles: [],
      workHistory: []
    })
  }
})

// 获取身份证照片URL列表用于预览
const idPhotoUrls = computed(() => {
  return props.recordData.idFiles?.map((photo) => config.downloadFileUrl + photo.filePath) || []
})

// 同样为保险文件添加预览功能
const insuranceUrls = computed(() => {
  return props.recordData.insureFiles?.map((doc) => config.downloadFileUrl + doc.filePath) || []
})

const avatar = computed(() => {
  return props.recordData.documentFiles?.map((doc) => config.downloadFileUrl + doc.filePath) || []
})

const loading = ref(true)

// 监听 recordData 的变化
watch(
  () => props.recordData,
  (newVal) => {
    if (Object.keys(newVal).length > 0) {
      setTimeout(() => {
        loading.value = false
      }, 1000)
    }
  },
  { immediate: true }
)

const currentIndex = ref(0)

const nextImage = () => {
  if (currentIndex.value < props.recordData.insureFiles.length - 3) {
    currentIndex.value++
  }
}

const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}
</script>

<style lang="scss" scoped>
.basic-info {
  background: #ffffff;
  color: #303133;

  .content-box {
    border-radius: 8px;
    border: 1px solid #ccc;
    padding: 24px;
    margin-bottom: 24px;
    background: #f5f8ff;
    position: relative;

    .special-worker-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: #409eff;
      color: white;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 14px;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 24px;
    color: #303133;
  }

  .sub-title {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .info-header {
    display: flex;

    .avatar {
      width: 160px;
      height: 180px;
      margin-right: 24px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        object-fit: cover;
      }
    }

    .info-content {
      flex: 1;
      width: 100%;
      overflow: hidden;
      overflow-x: auto;

      .basic-info-section {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
        margin-bottom: 24px;

        .info-column {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .info-item {
          min-width: unset;
        }
      }

      .insurance-docs {
        .docs-container {
          position: relative;
          height: 150px;
          display: flex;
          align-items: center;
          padding: 0 20px;
          overflow: hidden;

          .docs-wrapper {
            width: calc(150px * 3 + 16px * 2);
            overflow: hidden;
          }

          .docs-group {
            display: flex;
            gap: 16px;
            transition: transform 0.3s ease;
            will-change: transform;
            flex-wrap: nowrap;
          }

          .doc-item {
            width: 150px;
            height: 150px;
            flex-shrink: 0;
          }

          .arrow-left,
          .arrow-right {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            cursor: pointer;
            color: #909399;
            transition: color 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 24px;
            z-index: 1;

            &:hover {
              color: #409eff;
            }
          }

          .arrow-left {
            left: -5px;
          }

          .arrow-right {
            right: 0px;
          }
        }
      }
    }
  }
}

.work-history {
  background: #fff;
  border-radius: 8px;
  margin-top: 24px;
  border: 1px solid #ccc;
  padding: 24px;
}

.docs-group,
.photos-container {
  display: flex;
  gap: 16px;
}

.doc-item,
.photo-item {
  width: 12.5rem;
  height: 9.375rem;
  cursor: pointer;
  transition: all 0.3s;
  flex: 0 0 auto;

  .el-image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    object-fit: cover;
  }
}

:deep(.el-carousel__item) {
  border-radius: 4px;
}

:deep(.el-carousel__item--card) {
  &.is-active {
    .docs-group {
      .doc-item {
        transform: none;
      }
    }
  }
}

:deep(.el-image-viewer__wrapper) {
  position: fixed;
  z-index: 2000;
}

:deep(.el-image-viewer__img) {
  max-width: 100%;
  max-height: 100%;
}

.empty-data {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 16px 0;
}
</style>
