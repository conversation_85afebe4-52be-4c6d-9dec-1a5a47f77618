<template>
  <div class="certificate-hold">
    <div class="certificate-list" v-if="recordData.length">
      <div v-for="(item, index) in recordData" :key="index" class="certificate-item">
        <div class="cert-image">
          <imgViewList :img-list="item.certificateFiles || []" height="" />
        </div>
        <div class="cert-info">
          <div class="info-row">
            <span class="label">证书名称：</span>
            <span>{{ item.certificateName || '--' }}</span>
            <span class="label right">证书编号：</span>
            <span>{{ item.certificateNo || '--' }}</span>
          </div>
          <div class="info-row">
            <span class="label">证书类型：</span>
            <span>{{ item.certificateTypeName || '--' }}</span>
            <span class="label right">发证日期：</span>
            <span>{{ item.issueTime || '--' }}</span>
          </div>
          <div class="info-row">
            <span class="label">证书有效期：</span>
            <span>{{
              item.issueStartTime && item.issueEndTime ? `${item.issueStartTime} - ${item.issueEndTime}` : '--'
            }}</span>
            <span class="label right">证书状态：</span>
            <span
              :class="{
                'status-text-normal': item.status === '正常',
                'status-text-expired': item.status === '即将逾期' || item.status === '已逾期'
              }"
              >{{ item.status || '--' }}</span
            >
          </div>
          <div class="info-row">
            <span class="label">下次复审日期：</span>
            <span>{{ item.nextReviewTimeStr || '--' }}</span>
          </div>
        </div>
        <div
          class="status-tag"
          :class="{
            normal: item.status === '正常',
            expired: item.status === '已逾期',
            warning: item.status === '即将逾期'
          }"
        >
          {{ item.status }}
        </div>
      </div>
    </div>
    <div v-else-if="loading" class="skeleton-list">
      <div v-for="i in 3" :key="i" class="certificate-item skeleton">
        <div class="cert-image skeleton-block"></div>
        <div class="cert-info">
          <div v-for="j in 4" :key="j" class="info-row">
            <div class="skeleton-text"></div>
            <div class="skeleton-text"></div>
          </div>
        </div>
      </div>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<script lang="ts" setup>
import imgViewList from '@/components/imgViewList/index.vue'

const props = defineProps({
  recordData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped lang="scss">
.certificate-hold {
  .certificate-item {
    display: flex;
    padding: 16px 24px;
    border: 1px solid #ccc;
    border-radius: 8px;
    position: relative;
    margin-bottom: 16px;
    background: #f5f8ff;
  }

  .cert-image {
    width: 160px;
    height: 160px;
    margin-right: 62px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  .cert-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .info-row {
      display: flex;
      margin-bottom: 12px;
      align-items: center;

      .label {
        color: #666;
        min-width: 84px;

        &.right {
          margin-left: auto;
          padding-left: 32px;
          min-width: 84px;
        }
      }

      span:not(.label) {
        flex: 1;
      }
    }
  }

  .status-tag {
    width: 80px;
    text-align: center;
    position: absolute;
    top: 8px;
    right: 10px;
    padding: 4px;
    border-radius: 4px;
    font-size: 14px;
  }
  .normal {
    border: 1px solid #4a91cd;
    background: #dce3fb;
    color: #4a91cd;
  }
  .expired {
    border: 1px solid #f44336;
    background: #ffebee;
    color: #f44336;
  }
  .warning {
    border: 1px solid #f44336;
    background: #ffebee;
    color: #f44336;
  }
}

.skeleton-list {
  .skeleton {
    background: #fff !important;
  }

  .skeleton-block {
    background: #f2f2f2;
    animation: skeleton-loading 1.5s infinite;
  }

  .skeleton-text {
    height: 16px;
    background: #f2f2f2;
    margin: 8px 0;
    border-radius: 4px;
    animation: skeleton-loading 1.5s infinite;

    &:nth-child(1) {
      width: 60%;
    }
    &:nth-child(2) {
      width: 40%;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.status-text-normal {
  color: #4a91cd;
}
.status-text-expired {
  color: #f44336;
}

.no-data-center {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
</style>
