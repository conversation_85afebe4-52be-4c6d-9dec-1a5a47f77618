<template>
  <div class="train-record">
    <!-- 用户信息区域 -->
    <div class="user-info">
      <div class="info-left">
        <div class="user-name">{{ userInfo.userName }}</div>
      </div>
      <div class="info-right">
        <div class="info-column">
          <span>企业：{{ userInfo.unitName || '--' }}</span>
          <span>共培训 {{ userInfo.trainTimes >= 0 ? userInfo.trainTimes + '次' : '--' }}</span>
        </div>
        <div class="info-column">
          <span>部门：{{ userInfo.deptName || '--' }}</span>
          <span>签到次数 {{ userInfo.signInTimes >= 0 ? userInfo.signInTimes + '次' : '--' }}</span>
        </div>
        <div class="info-column">
          <span>岗位：{{ userInfo.postName || '--' }}</span>
          <span>培训通过 {{ userInfo.passTimes >= 0 ? userInfo.passTimes + '次' : '--' }}</span>
        </div>
      </div>
    </div>

    <!-- 培训记录表格 -->
    <div class="record-table">
      <el-scrollbar height="620px">
        <el-table :data="tableData" height="100%">
          <template #empty>
            <no-data />
          </template>
          <el-table-column type="index" label="序号" width="70" />
          <el-table-column prop="taskName" label="培训任务名" align="center" show-overflow-tooltip />
          <el-table-column prop="planStartTime" label="培训时间" align="center" />
          <el-table-column prop="examTime" label="培训时长" align="center" width="80" />
          <el-table-column prop="trainUserName" label="培训实施人" align="center" />
          <el-table-column prop="signTime" label="签到时间点" align="center" />
          <el-table-column prop="isExamName" label="是否考试" align="center" />
          <el-table-column prop="score" label="考试分数" align="center" width="80">
            <template #default="scope">
              <span>{{ scope.row.score ? scope.row.score + '分' : '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isPassName" label="培训通过情况" align="center">
            <template #default="scope">
              <span>{{ scope.row.isPassName ? scope.row.isPassName : '--' }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>

      <!-- 分页器 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
const props = defineProps({
  recordData: {
    type: Object,
    default: () => ({
      stats: {},
      list: []
    })
  }
})

// 用户信息和统计数据直接引用 recordData.stats
const userInfo = ref(props.recordData.stats || {})
const statistics = ref(props.recordData.stats || {})

// 表格数据
const tableData = ref([])

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 监听数据变化
watch(
  () => props.recordData,
  (newData) => {
    console.log('newData', newData)
    if (newData?.stats) {
      userInfo.value = newData.stats
      statistics.value = newData.stats
      tableData.value = newData.list || []
      // 更新总数
      pagination.total = newData.total || 0
    }
  },
  { immediate: true }
)

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1 // 重置到第一页
  emit('update:size', pagination.currentPage, size) // 添加发送size变化事件
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  emit('update:page', page, pagination.pageSize)
}

// 修改 emit 定义，添加分页大小变化事件
const emit = defineEmits(['update:page', 'update:size'])
</script>

<style lang="scss" scoped>
.train-record {
  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .user-info {
    height: 120px;
    background-color: #4d7cfe;
    color: white;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .info-left {
      margin-left: 30px;
      flex: 0 0 200px;

      .user-name {
        font-size: 28px;
      }
    }

    .info-right {
      flex: 1;
      display: flex;
      gap: 40px;

      .info-column {
        display: flex;
        flex-direction: column;
        gap: 25px;
      }
    }
  }

  .record-table {
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
