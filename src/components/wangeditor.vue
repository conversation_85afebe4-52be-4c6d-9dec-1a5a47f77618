<!--
 * @Author: jingjf <EMAIL>
 * @Date: 2024-07-09 20:35:44
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-15 10:41:18
 * @FilePath: \ehs-partner-mgr\src\components\wangeditor.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="w-full h-full">
    <div ref="toolbarContainer" class="toolbar-container"></div>
    <div ref="editorContainer" class="editor-container"></div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { createEditor, createToolbar } from '@wangeditor/editor'
import config from '~/config'
import $API from '~/common/api'

export default {
  name: 'WangEditor',
  props: {
    modelValue: {
      type: String,
      default: '',
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const editorContainer = ref(null)
    const toolbarContainer = ref(null)
    let editor, toolbar, imgUrl
    const uploadImg = async (file, insertFn) => {
      let uploadfile = new FormData()
      uploadfile.append('uploadfile', file)
      try {
        $API
          .formDataFile(
            {
              serverName: 'editorFile',
              url: '/file/uploadfile',
            },
            uploadfile
          )
          .then((res) => {
            if (res) {
              imgUrl =
                window.location.host !== 'agjp.tanzervas.com'
                  ? config.base_host + '/ahzd-beta/file/' + res.data.filePath
                  : config.base_host + '/file/' + res.data.filePath
              // 调用insertFn插入图片
              insertFn(imgUrl)
            }
          })
      } catch (error) {
        console.error('上传图片失败', error)
      }
    }

    onMounted(() => {
      editor = createEditor({
        selector: editorContainer.value,
        config: {
          placeholder: '请输入内容...',
          onChange(editor) {
            const html = editor.getHtml()
            emit('update:modelValue', html)
          },

          MENU_CONF: {
            // 配置上传图片
            uploadImage: {
              customUpload: (file, insertFn) => uploadImg(file, insertFn),
            },
          },
        },
      })

      toolbar = createToolbar({
        editor,
        selector: toolbarContainer.value,
        config: {
          excludeKeys: ['group-video'],
        },
      })
      // console.log(toolbar.getConfig().toolbarKeys)
      // 设置初始内容
      editor.setHtml(props.modelValue)
      // 设置样式以确保内容自动换行
      const editorElement = editorContainer.value.querySelector('.w-e-text-container')
      if (editorElement) {
        editorElement.style.whiteSpace = 'pre-wrap'
        editorElement.style.wordWrap = 'break-word'
        editorElement.style.overflowWrap = 'break-word'
      }
    })

    onBeforeUnmount(() => {
      editor.destroy()
      toolbar.destroy()
    })

    return {
      editorContainer,
      toolbarContainer,
    }
  },
}
</script>

<style scoped>
.editor-container {
  height: 200px;
  border: 1px solid #ccc;
  overflow-y: auto;
  max-width: 100% !important;
}

.toolbar-container {
  border: 1px solid #ccc;
  border-bottom: none;
}
:deep(.w-e-modal button) {
  /* line-height: 3px; */
  padding: 0 15px;
}

:deep(.w-e-text-container) {
  width: 728px;
}
</style>
