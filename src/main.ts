/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:09:04
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-15 10:59:45
 * @FilePath: /ehs-partner-mgr/src/main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import $API from '~/common/api'
import App from './App.vue'
import '@wangeditor/editor/dist/css/style.css'
import elementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'virtual:svg-icons-register'
import 'virtual:windi.css'
import registerComponent from './common/registerComponent'
import { BridgeService } from './common/utils/BridgeService'
import { registerDirectives } from './directives'
import { setFavicon } from './hooks/updIcon'
import router from './router'
import piniaInstance, { useUserInfo } from './store'
import './style.scss'
import '@tanzerfe/ifm-child'

const app = createApp(App)
registerComponent(app)

const handleTokenValidation = async (to: any, next: any) => {
  const ui = useUserInfo()
  const res: any = await $API.post({
    url: 'edu-inter-server/login/queryToken',
    params: {
      sysCode: to.query.sysCode,
      token: to.query.token
    }
  })
  if (res && res.code === 'success') {
    const d = res.data
    const transformTree = (resourceVoList: any) => {
      return resourceVoList.map((node) => {
        // 创建新对象或修改原对象
        node.children = node.childrens || []

        // 递归处理 children（即原 childrens）
        if (node.children.length > 0) {
          node.children = transformTree(node.children)
        }

        return node
      })
    }
    d.resourceVoList = transformTree(res?.data?.resourceVoList)
    // d.resourceVoList = res?.data?.resourceVoList.map((item) => {
    //   return {
    //     ...item,
    //     children: item.childrens
    //   }
    // })
    ui.value = d
    ui.value.coopResourceList = ui.value.resourceVoList || []
    ui.value.systemName = d?.systemName || '相关方管理系统'
    ui.value.resourceList = d?.coopResourceList || []
    // 获取安全积分按钮权限
    ui.value.buttonPermission = []
    ui.value.resourceList.forEach((item: any) => {
      item.childrens.forEach((sItem: any) => {
        const arr = sItem.childrens || []
        ui.value.buttonPermission = ui.value.buttonPermission.concat(arr.map((i: any) => i.resAlias))
      })
    })

    sessionStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
    localStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
    next()
  } else {
    next({ path: '/login' })
    return false
  }
}

router.beforeEach(async (to: any, from: any, next: any) => {
  const ui = useUserInfo()
  console.log(from)
  setFavicon(ui.value)
  //判断该用户有没有登录过
  if (to.path === '/login' || to.path === '/index') {
    next()
  } else {
    if (ui.value.id) {
      if (to.query.token) {
        sessionStorage.removeItem('@@web_userInfo_partner')
        localStorage.removeItem('@@web_userInfo_partner')
        await handleTokenValidation(to, next)
      } else {
        next()
      }
    } else {
      await handleTokenValidation(to, next)
    }
  }
})

app.use(router)
app.use(piniaInstance)
app.use(elementPlus, { size: '', locale: zhCn })

// register directives
registerDirectives(app)

// 注册代理服务(开发)
import.meta.env.DEV && BridgeService.registerProxy()
app.mount('#app')
