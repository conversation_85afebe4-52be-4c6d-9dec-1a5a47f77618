/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:26:50
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-13 16:26:20
 * @FilePath: /ehs-partner-mgr/src/router/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory } from 'vue-router'
import defaultLayout from '@/layout/default.vue'

const routes = [
  {
    path: '/index',
    component: () => import('@/view/index.vue')
  },
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    component: () => import('@/view/login.vue')
  },
  {
    path: '/digitalSpace',
    name: 'digitalSpace',
    component: () => import('@/view//digitalSpace.vue')
  },
  {
    path: '/enterprise',
    name: 'enterprise',
    component: () => import('@/view/enterprise/index.vue')
  },
  {
    path: '/preview',
    name: 'preview',
    component: () => import('@/view/preview.vue')
  },

  {
    path: '/interestedParty',
    name: 'interestedParty',
    component: defaultLayout,
    children: [
      {
        path: '/resumeManagement',
        name: 'resumeManagement',
        meta: {
          pageTitle: '相关方履历管理'
        },
        component: () => import('@/view/resumeManagement/index.vue')
      }
    ]
  },
  {
    path: '/stagingMain',
    name: 'stagingMain',
    component: defaultLayout,
    children: [
      {
        path: '/staging',
        name: 'staging',
        meta: {
          pageTitle: '工作台'
        },
        component: () => import('@/view/staging/index.vue')
      }
    ]
  },
  {
    path: '/trainCourseManagementMain',
    name: 'trainCourseManagementMain',
    component: defaultLayout,
    children: [
      {
        path: '/trainCourseManagement',
        name: 'trainCourseManagement',
        meta: {
          pageTitle: '培训课程管理'
        },
        component: () => import('@/view/trainCourseManagement/index.vue')
      }
    ]
  },
  {
    path: '/relatedTypeSettingMain',
    name: 'relatedTypeSettingMain',
    component: defaultLayout,
    children: [
      {
        path: '/relatedTypeSetting',
        name: 'relatedTypeSetting',
        meta: {
          pageTitle: '基础设置'
        },
        component: () => import('@/view/basicSettings/index.vue')
      }
    ]
  },
  {
    path: '/personnelResumeManagementMain',
    name: 'personnelResumeManagementMain',
    component: defaultLayout,
    children: [
      {
        path: '/personnelResumeManagement',
        name: 'personnelResumeManagement',
        meta: {
          pageTitle: '人员履历管理'
        },
        component: () => import('../view/personnelResumeManagement/index.vue')
      }
    ]
  },
  {
    path: '/homeWorkMain',
    name: 'homeWorkMain',
    component: defaultLayout,
    children: [
      {
        path: '/homeWork',
        name: 'homeWork',
        meta: {
          pageTitle: '作业过程管理'
        },
        component: () => import('../view/homework/index.vue')
      }
    ]
  },
  {
    path: '/partnerPictureMain',
    name: 'partnerPictureMain',
    component: defaultLayout,
    children: [
      {
        path: '/partnerPicture',
        name: 'partnerPicture',
        meta: {
          pageTitle: '相关方一张图'
        },
        component: () => import('../view/partnerPicture/index.vue')
      }
    ]
  },
  {
    path: '/workFlowMain',
    name: 'workFlowMain',
    component: defaultLayout,
    children: [
      {
        path: '/workFlow',
        name: 'workFlow',
        meta: {
          pageTitle: '流程指引'
        },
        component: () => import('@/view/workFlow/index.vue')
      }
    ]
  },
  {
    path: '/mustSystemMain',
    name: 'mustSystemMain',
    component: defaultLayout,
    children: [
      {
        path: '/mustSystem',
        name: 'mustSystem',
        meta: {
          pageTitle: '相关方必遵制度'
        },
        component: () => import('@/view/mustSystem/index.vue')
      }
    ]
  },
  {
    path: '/projectMgrMain',
    name: 'projectMgrMain',
    component: defaultLayout,
    children: [
      {
        path: '/projectMgr',
        name: 'projectMgr',
        meta: {
          pageTitle: '项目管理'
        },
        component: () => import('@/view/projectMgr/index.vue')
      }
    ]
  }
]
const router = createRouter({
  routes,
  history: createWebHashHistory()
})
export default router
