/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-10 15:23:00
 * @FilePath: /ehs-partner-mgr/src/store/useUserInfo.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getUserInfo } from '@/common/utils';
import { defineStore } from 'pinia';
import { computed } from 'vue';
// import { UserInfo } from '@/types'

const userInfoStore = defineStore('@@web_userInfo_partner', {
  state: () => {
    return {
      userInfo: getUserInfo(),
    }
  },

  actions: {
    setUserInfo(payload) {
      this.userInfo = payload
    }
  },

  persist: true
})

export default function useUserInfo() {
  const uis = userInfoStore()

  return computed({
    get: () => uis.userInfo || {},

    set(val) {
      uis.setUserInfo(val)
    }
  })
}
