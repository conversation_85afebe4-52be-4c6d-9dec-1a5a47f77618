<template>
  <div class="w-full h-full flex">
    <el-card
      v-if="ui.unitOrgType !== '1'"
      class="mr-[20px] org-tree"
      :class="!collapsed ? '' : 'p-20px'"
      :style="{ width: collapsed ? '323px' : '0' }"
    >
      <train-tree :collapsed="collapsed" @serach="searchData" />
    </el-card>
    <div class="homework relative flex-1 w-0">
      <ul class="head">
        <li>
          <div>
            <p>总作业数</p>
            <p>{{ countObj.workCount }}</p>
          </div>
        </li>
        <li>
          <div>
            <p>今日作业数</p>
            <p>{{ countObj.todayWorkNum }}</p>
          </div>
        </li>
        <li>
          <div>
            <p>申请中作业数</p>
            <p>{{ countObj.applyWorkCount }}</p>
          </div>
        </li>
        <li>
          <div>
            <p>作业中作业数</p>
            <p>{{ countObj.workingCount }}</p>
          </div>
        </li>
        <li>
          <div>
            <p>今日已完成作业数</p>
            <p>{{ countObj.todayFinishNum }}</p>
          </div>
        </li>
        <li>
          <div>
            <p>出现隐患作业数</p>
            <p>{{ countObj.violationCount }}</p>
          </div>
        </li>
      </ul>
      <div class="expand" @click="collapsedTree" v-if="ui.unitOrgType !== '1'"></div>
      <stopCom @addDialog="addDialog" :id="id" />
      <!-- 监测和详情抽屉-->
      <EditCom :nameee="namee" ref="dialogRef" @action="handleFun"></EditCom>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import EditCom from './comp/edit.vue'
import stopCom from './comp/stopCom.vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import TrainTree from '@/components/tree/index.vue'

const ui: any = useUserInfo()
const collapsed = ref(true)

const dialogRef = ref()
const tableRef = ref()
const namee = ref('')
const id = ref<string>(ui.value.unitId) // 选中树形结构id
const countObj = ref({
  workCount: 0,
  todayWorkNum: 0,
  applyWorkCount: 0,
  workingCount: 0,
  todayFinishNum: 0,
  violationCount: 0
})
const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

// 接受树结构选中数据
const searchData = (obj) => {
  id.value = obj.orgCode
}
const addDialog = (obj: any, str: any) => {
  namee.value = str
  dialogRef.value.showDialog(obj)
}

const getCountData = async () => {
  let res: any = await $API.get({
    url: 'edu-inter-server/workManage/queryStatistic',
    params: { unitId: id.value }
  })
  if (res.data && res.code == 'success') {
    countObj.value = res.data
  }
}
getCountData()
function handleFun() {
  tableRef.value.refreshFetch()
}
watch(
  () => id.value,
  (val) => {
    if (val) getCountData()
  },
  { immediate: true }
)

watch(
  () => ui.value.unitOrgType,
  (val) => {
    if (val == '1') collapsed.value = false
  },
  { immediate: true }
)

onMounted(() => {})
</script>

<style scoped lang="scss">
.org-tree {
  // min-width: 310px;
  background: #eef7ff;
  height: calc(100vh - 115px);
  overflow: hidden;

  :deep(.el-tree) {
    .el-tree-node__content {
      //   height: 40px;
      margin-top: 4px;
      border-radius: 6px;
      /* height: auto; */
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.homework {
  // width: 100%;
  // width: calc(100vw - 625px);
  height: 100%;
  background-color: #fff;
  overflow: visible;

  .expand {
    background: url('@/assets/expand.png') no-repeat;
    width: 34px;
    height: 36px;
    background-size: 100% 100%;
    position: absolute;
    top: 45%;
    left: -17px;
    cursor: pointer;
  }

  .head {
    width: 100%;
    margin-top: 20px;
    padding: 0 5px;
    color: #fff;
    list-style: none;
    height: 80px;
    display: flex;
    justify-content: space-around;

    li:nth-child(1) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/one.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #527cff;
        }
      }
    }

    li:nth-child(2) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/two.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #0097a9;
        }
      }
    }

    li:nth-child(3) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/tree.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #00cfe8;
        }
      }
    }

    li:nth-child(4) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/four.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #527cff;
        }
      }
    }

    li:nth-child(5) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/five.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #409d13;
        }
      }
    }

    li:nth-child(6) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('../../assets/img/six.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #fd8410;
        }
      }
    }
  }
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

.ifm-child {
  .homework {
    @apply flex flex-col;
    .w_server_content {
      @apply flex-1 h-0 flex flex-col;
      :deep(.w_server_table) {
        @apply flex flex-col overflow-hidden;
        .el-table {
          height: 100% !important;
        }
      }
    }
  }
}
</style>
