<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" width="848px">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>{{ props.nameee == '1' ? '详情' : '监测' }}</div>
        </div>
      </template>
      <el-tabs type="card" v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="作业详情" name="1"> </el-tab-pane>
        <el-tab-pane label="智能监测" name="2"> </el-tab-pane>
        <!-- <el-tab-pane label="过程隐患" name="3"> </el-tab-pane> -->
      </el-tabs>
      <Detail :detailObj="detailObj" v-show="activeName === '1'" ref="workRef" @showgis="handleGis" />
      <JianCe v-show="activeName === '2'" ref="checkRef" />
      <YinHuan v-show="activeName === '3'" ref="recordRef" />
    </el-drawer>
    <el-dialog v-model="isShowGis" title="位置" class="h-[640px] w-[600px]">
      <div v-if="isShowGis" class="w-[860px] h-[500px]">
        <floorMap :floor-info="floorData" :isAddMark="true" :pointer="pointer" @add-mark="addMark"></floorMap>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick } from 'vue'
// import { GISOBJ } from '@/view/indoorMap/gisGlobal'
import floorMap from '@/view/indoorMap/index.vue'

import Detail from './detail.vue'
import JianCe from './jiance.vue'
import YinHuan from './yinhuan.vue'
// import { ElMessage } from 'element-plus'
// import SelectUsers from '@/view/components/selectUsers/index.vue'
// import config from '@/config'
// import UploadImg from '@/components/uploadImg/index.vue'

const isShowGis = ref(false)
// const pointer = ref({
//   x: 3028718.761922908,
//   y: 909491.4322155592,
// })
const pointer = ref({
  x: '',
  y: ''
})

const floorData = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: ''
})

const addMark = (val: { x: number; y: number; text: string }) => {
  console.log('🚀 ~ addMark ~ val:', val)
}

const handleGis = (obj) => {
  isShowGis.value = true
  pointer.value.x = obj.x
  pointer.value.y = obj.y
  floorData.value.unitId = obj.unitId
  floorData.value.buildingId = obj.buildingId
  floorData.value.floorId = obj.floorId
  console.log(obj)
}

const props = defineProps({
  nameee: {
    type: String,
    default: '1'
  }
})
const activeName = ref('1')
// 作业详情
const workRef = ref()
// 智能检测
const checkRef = ref()
// 异常记录
const recordRef = ref()

const detail = ref('')

const handleClick = (tab: any) => {
  if (tab.props.name == '1') {
    nextTick(() => {
      workRef.value.initData(detail.value)
    })
  } else if (tab.props.name == '2') {
    checkRef.value.initData(detail.value)
  } else {
    recordRef.value.initData(detail.value)
  }
}
// const emits = defineEmits(['action', 'submit'])

const drawer = ref(false)
const detailObj = ref({})
const showDialog = (row: any) => {
  detail.value = row.id
  if (row.id) {
    detailObj.value = row
    console.log('detailObj:', detailObj.value)
  }
  drawer.value = true
  nextTick(() => {
    activeName.value = props.nameee
    if (activeName.value == '1') {
      workRef.value.initData(detail.value)
    } else if (activeName.value == '2') {
      checkRef.value.initData(detail.value)
    }
  })
}

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 848px !important;
    overflow: auto !important;
  }

  :deep(.el-tab-pane) {
    height: 100% !important;
    //overflow: auto !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  :deep(.el-drawer__close-btn) {
    position: fixed !important;
    top: 1% !important;
    right: 2% !important;
  }

  .w_add_plan_header {
    font-weight: 700;

    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      border-radius: 2px 2px 2px 2px;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_plan_con_box {
    font-size: 0.875rem;
    //background: yellowgreen;
    display: grid;
    height: 790px;
    grid-template-rows: 790px;
    grid-template-columns: 1fr;
    color: rgba(72, 74, 77, 1);

    .bg_text {
      color: rgba(48, 49, 51, 1);
      font-size: 16px;
    }

    .w_content_box {
      //background: red;
      width: 100%;
      height: 790px;

      .w_plan_con_one {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .div_con20 {
          height: 20px;
          line-height: 20px;
          display: flex;

          .w_flex-1 {
            flex: 1;
          }

          .w_flex-2 {
            flex: 2;
            overflow: auto;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .w_dialog_from_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .w_file_btn {
          width: 136px;
          height: 32px;
          border: 1px solid #527cff;
          text-align: center;
          color: #527cff;
          cursor: pointer;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
        }

        //background: red;
        .w_file_t {
          color: #a8abb2;
        }
      }

      .w_table_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 10px 0 16px;
        margin-bottom: 22px;

        .w_dialog_from_title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
        }

        .w_add_bor {
          width: 100px;
          border: 1px solid #527cff;
          color: #527cff;
          text-align: center;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
          cursor: pointer;
        }

        .w_span_cursor {
          color: #527cff;
          cursor: pointer;
        }

        :deep(.el-table__header .el-table__cell) {
          background-color: rgba(245, 246, 249, 1);
          /* 表头背景色 */
          color: #606266;
          /* 表头字体颜色 */
          font-size: 14px;
          /* 表头字体大小 */
          height: 48px;
        }
      }

      .w_page_box {
        margin-top: 16px;
        height: 32px;
        display: flex;
        justify-content: end;
        padding-right: 24px;
      }
    }
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;

    //background-color: red;
    //padding-right: 20px;
    .w_btn {
      height: 32px;
    }

    .w_btn_bg {
      background-color: rgba(82, 124, 255, 1) !important;
    }
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-tabs--card > .el-tabs__header) {
  border: none;
}

:deep(.el-tabs__nav-scroll) {
  margin-left: 35%;
}

:deep(.el-tabs--top) {
  flex-direction: column !important;
}

:deep(.el-tabs__item) {
  border-bottom: 1px solid rgb(237, 231, 231) !important;
}

:deep(.el-tabs__item.is-active) {
  background: #527cff;
  color: #ffffff;
}
</style>
