<template>
  <el-table v-bind="$attrs" :data="data">
    <el-table-column v-for="item in columns" :key="item.prop || item.type" v-bind="item" align="center">
      <template #default="scope">
        <template v-if="item.slot">
          <div v-if="scope.row.qualificationImage">
            <el-image
              :src="config.downloadFileUrl + scope.row.qualificationImage"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[config.downloadFileUrl + scope.row.qualificationImage]"
              :initial-index="4"
              fit="cover"
            />
          </div>
          <div v-else>--</div>
        </template>
        <template v-else-if="item.prop && !item.type">
          {{ scope.row[item.prop] || '--' }}
        </template>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import type { TableColumnCtx } from 'element-plus'
import config from '@/config'

// 定义表格列的接口
interface TableColumn extends Partial<TableColumnCtx<any>> {
  prop?: string
  type?: string
  slot?: any
}

// 定义表格数据的接口
interface TableData {
  [key: string]: any
}

// 定义 props 的类型
interface Props {
  data: TableData[]
  columns: TableColumn[]
}

// 使用 withDefaults 定义带默认值的 props
withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => []
})
</script>
