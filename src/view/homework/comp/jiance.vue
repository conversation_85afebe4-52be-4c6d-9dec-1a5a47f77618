<template>
  <div class="detail">
    <div class="table">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="deviceName" label="监测设备" width="100" align="center" />
        <el-table-column prop="eventDesc" label="内容描述" width="130" align="center" />
        <el-table-column prop="deviceTime" label="监测时间点" width="180" align="center" />
        <el-table-column prop="picPath" label="图片" width="100" align="center">
          <template #default="scope">
            <el-image
              v-if="scope.row.picPath"
              :src="config.appServer + scope.row.picPath"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[config.appServer + scope.row.picPath]"
              :initial-index="4"
              fit="cover"
            />
            <div v-else>--</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '@/common/api'
import config from '~/config'

const tableData = ref([])
const total = ref<number>(0)

const pageNo = ref<number>(1)

const pageSize = ref<number>(20)

const getListData = async (id: string) => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/pageDeviceAbnormalRecord',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      operationBaseId: id
      // operationBaseId: 'bf9786f036e643b183e4afae9dcaf3f0',
    }
  })
  if (res.data) {
    tableData.value = res.data.rows
    total.value = res.data.total
  }
}

const initData = (id: string) => {
  console.log('检测')
  getListData(id)
}

defineExpose({
  initData
})

defineOptions({ name: 'homeWorkJianCeCom' })
</script>

<style scoped lang="scss">
.detail {
  color: black;

  .text {
    margin-left: 5px;

    font-size: 12px;

    p {
      margin-top: 20px;
    }
  }

  .table {
    width: 100%;
    margin-top: 20px;
  }
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}

:deep(.el-table__header) {
  width: 100% !important;
}
:deep(.el-table__body) {
  width: 100% !important;
}
</style>
