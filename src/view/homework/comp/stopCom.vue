<template>
  <div class="w_server_content">
    <div class="w_server_title_box">
      <el-form ref="formRef" inline :label-width="80" :model="formValue">
        <el-form-item label="作业编号" path="deptName">
          <el-input v-model="formValue.workNumber" placeholder="请输入作业编号" clearable />
        </el-form-item>
        <el-form-item label="作业类型" path="type">
          <el-select v-model="formValue.workType" placeholder="全部" clearable>
            <el-option label="常规作业" value="ZY" />
            <el-option label="动火作业" value="DH" />
            <el-option label="受限空间作业" value="SX" />
            <el-option label="盲板抽堵作业" value="MB" />
            <el-option label="高空作业" value="GC" />
            <el-option label="吊装作业" value="DZ" />
            <el-option label="临时用电作业" value="YD" />
            <el-option label="动土作业" value="DT" />
            <el-option label="断路作业" value="DL" />
          </el-select>
        </el-form-item>
        <el-form-item label="作业状态" path="status">
          <el-select v-model="formValue.workStatus" placeholder="全部" clearable>
            <el-option label="草稿" value="1" />
            <el-option label="申请中" value="2" />
            <el-option label="作业中" value="3" />
            <el-option label="已完成" value="4" />
            <el-option label="作废" value="5" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input v-model="formValue.unitName" placeholder="请输入企业名称查询" clearable />
        </el-form-item>
      </el-form>
    </div>
    <div class="w_server_table">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%" fit>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="workNumber" label="作业编号" align="center" show-overflow-tooltip width="150" />
        <el-table-column
          prop="relatedUnitName"
          :label="ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业'"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column prop="workTypeName" label="作业类型" align="center" show-overflow-tooltip />
        <el-table-column prop="workAddress" label="作业地点" align="center" show-overflow-tooltip />
        <el-table-column prop="workDuration" label="作业时间" align="center" show-overflow-tooltip />
        <el-table-column prop="violationFlag" label="是否存在违规" align="center" width="110" show-overflow-tooltip />
        <el-table-column prop="deviceNum" label="监测设备数" align="center" width="110" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.deviceNum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="workStatus" label="作业状态" align="center" width="110" show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.workStatus == '2'" type="warning">{{ scope.row.workStatusName }}</el-tag>
            <el-tag v-else-if="scope.row.workStatus == '3'" type="success">{{ scope.row.workStatusName }}</el-tag>
            <el-tag v-else-if="scope.row.workStatus == '4'" type="info">{{ scope.row.workStatusName }}</el-tag>
            <el-tag v-else type="info">{{ scope.row.workStatusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="jiance(scope.row)">监测</el-button>
            <el-button type="primary" plain size="small" @click="toDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_server_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
// import { ElMessage } from 'element-plus'
import mixTableHeight from '~/common/tableHeight.js'
// import { Search } from '@element-plus/icons-vue'
import { useUserInfo } from '~/store'

const ui: any = useUserInfo()
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const { tableHeight } = mixTableHeight({ subtractHeight: 365 })

const formValue = ref({
  workNumber: '',
  workType: '',
  workStatus: '',
  unitName: ''
})

const emits = defineEmits(['addDialog'])
const tableData = ref<any>([])
const total = ref<number>(0)

const pageNo = ref<number>(1)

const pageSize = ref<number>(20)

const getListData = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/pageList',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      ...formValue.value,
      unitId: props.id ? props.id : ui.value.unitId
    }
  })
  if (res.data && res.code == 'success') {
    // if(res.data.rows.length>0){
    tableData.value = res.data.rows
    // }

    total.value = res.data.total
  }
}

const handleSizeChange = () => {
  pageNo.value = 1
  getListData()
}
const handleCurrentChange = () => {
  getListData()
}
function jiance(row: any) {
  emits('addDialog', row, '2')
}
function toDetail(row: any) {
  emits('addDialog', row, '1')
}

// async function submit() {
//   // ruleDialogRef.value?.validate(async (valid: boolean) => {
//   //   if (valid) {
//   const params = {
//     userId: currentReasonRow.value.userId,
//     blockType: '0'
//   }
//   const res: any = await $API.get({
//     url: 'ehs-inter-server/perManager/operateBlock',
//     params
//   })
//   if (res.code == 'success') {
//     ElMessage({
//       message: res.msg || '此用户已取消 黑名单 列表中',
//       type: 'success'
//     })
//     cancel()
//     await getData()
//   } else {
//     ElMessage.error(res.msg || '取消黑名单失败')
//   }
//   //   }
//   // })
// }

// 获取列表
// async function getData() {
//   let res: any = await $API.post({
//     url: 'ehs-inter-server/perManager/pageList',
//     method: 'get',
//     data: {
//       pageNo: pageNo.value,
//       pageSize: pageSize.value,
//       serverStatus: 1,
//       ...filterForm.value
//     }
//   })

//   tableData.value = res.data.rows
//   total.value = res.data.total
// }
// function refreshFetch() {
//   getData()
// }

// getData()

watch(
  () => formValue.value,
  () => {
    getListData()
  },
  {
    deep: true
  }
)

watch(
  () => props.id,
  () => {
    getListData()
  },
  { immediate: true }
)

// getListData()

defineOptions({ name: 'PnlReMtStopCom' })
</script>

<style scoped lang="scss">
.w_server_content {
  // display: grid;
  width: 100%;
  // height: 100%;
  padding: 20px 24px 20px 24px;
  background-color: rgba(255, 255, 255, 1);
  // grid-template-columns: 1fr;
  // grid-template-rows: 50px 1fr;
  // grid-row-gap: 20px;

  .w_server_title_box {
    width: 100%;
    height: 54px;
    background-size: 100% 100%;
    line-height: 54px;
  }

  .w_server_table {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_server_page_num {
    display: flex;
    justify-content: end;
    margin-top: 20px;
  }
}

.demo-form-inline .el-input {
  --el-input-width: 280px;
}

.demo-form-inline .el-select {
  --el-select-width: 280px;
}

.el-select {
  width: 150px !important;
}
</style>
