<template>
  <el-dialog v-model="outerVisible" width="1064" higtht="892" v-if="outerVisible">
    <template v-slot:header>
      <HeadTitle title="选择用户"></HeadTitle>
    </template>
    <div class="underline"></div>
    <div class="selected">
      <div class="left">已选：</div>
      <div class="right">
        <el-scrollbar :height="68 + 'px'">
          <div class="item-all">
            <div class="item" v-for="user in unique(userList)" :key="user.id">
              <el-button @click="delUser(user)" style="height: 26px" class="mt-8px" type="primary" plain
                >{{ user.userName }}<el-icon> <Close /> </el-icon
              ></el-button>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="operate">
      <div class="head">
        <div class="left">选择人员</div>
        <div class="right">
          <el-form style="margin-left: 10px" :inline="true" :model="filterForm">
            <el-form-item label="关键字搜索：">
              <el-input
                style="width: 290px"
                v-model="filterForm.keyWords"
                placeholder="请输入员工姓名或岗位或手机号搜索"
                :clearable="true"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="body">
        <div class="tissue-tree w-298px h-full">
          <!--          <TreeMenu v-if="outerVisible" ref="treesRef" :superviseUnitId="deptId || userInfo.orgCode" />-->
          <TreeMenu v-if="outerVisible" ref="treesRef" />
        </div>
        <!--        <div class="user-list h-full">-->
        <!--          <UserTableList-->
        <!--            v-if="outerVisible"-->
        <!--            ref="userTableListRef"-->
        <!--            :deptId="deptId || userInfo.orgCode"-->
        <!--            @selectUser="selectUser"-->
        <!--          />-->
        <!--        </div>-->

        <div class="user-list h-full">
          <UserTableList v-if="outerVisible" ref="userTableListRef" @selectUser="selectUser" />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="outerVisible = false">取消</el-button>
        <el-button class="w_btn_bg" type="primary" @click="save">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import HeadTitle from '@/components/HeadTitle/index.vue'
import { Close } from '@element-plus/icons-vue'
import { nextTick, ref, useAttrs, watch } from 'vue'
import TreeMenu from './comp/treeMenu.vue'
import { useUserInfo } from '~/store'
import { ElMessage } from 'element-plus'
import store from './comp/store'
import UserTableList from './comp/tableList.vue'

const userInfo = useUserInfo().value
const attrs = useAttrs()
const emits = defineEmits(['selectedUser'])
const outerVisible = ref(false)
const innerVisible = ref(false)
const userList = ref<any>([])
// const userInfo = useUserInfo().value
const useStore = store()
const treesRef: any = ref()
const deptName: any = ref('')
const deptId: any = ref('')
const filterForm = ref<any>({
  keyWords: ''
})
const userTableListRef = ref()

const delUserId = ref()

function delUser(user: any) {
  const arr = userList.value.filter((item) => item.id === user.id)
  toggleSelection(arr)
  nextTick(() => {
    delUserId.value = user.id
  })
}

function selectUser(users: any) {
  console.log('++++++++++++user----', users)
  userList.value = users
}

function toggleSelection(rows: any) {
  if (rows) {
    userTableListRef.value!.toggleSelectionChild(rows)
  }
}

function save() {
  // 限制选择选中个数
  if (attrs.limit && attrs.limit < userList.value.length) {
    let mes = attrs.limitTotal || `选择人员不能大于${attrs.limit}`
    return ElMessage({
      message: mes,
      type: 'warning'
    })
  }
  if (userList.value.length <= 0) {
    innerVisible.value = true
    return ElMessage({
      message: '请选择人员',
      type: 'warning'
    })
  } else {
    outerVisible.value = false
    useStore.orgCode = userInfo.orgCode
    useStore.treeTabs = 1
    emits('selectedUser', userList.value)
  }
}

function unique(arr) {
  let obj = {} // 用来存放已经出现过的id
  return arr.reduce((pre, cur) => {
    if (!obj[cur.id]) {
      // obj没有cur的id则放入数组
      obj[cur.id] = true
      return [...pre, cur]
    } else {
      return pre
    }
  }, [])
}

watch(
  () => useStore.deptId,
  (val) => {
    deptId.value = val
    deptName.value = useStore.deptName
  }
)
watch(
  () => useStore.refreshBumen,
  () => {}
)
watch(
  () => useStore.refreshFlag,
  () => {}
)
watch(
  () => useStore.currentCode,
  () => {}
)

watch(
  () => filterForm.value,
  () => {
    console.log('搜索条件变化')
    userTableListRef.value!.getListDataWatch2(filterForm.value)
  },
  { deep: true }
)

watch(
  () => delUserId.value,
  (val) => {
    if (val) {
      const arr = userList.value.filter((item) => item.id === val)
      toggleSelection(arr)
    }
  },
  { deep: true }
)

watch(
  () => outerVisible.value,
  (val) => {
    if (!val) {
      toggleSelection(userList.value)
      userList.value = []
      filterForm.value.keyWords = ''
      useStore.cleanAll()
    }
  }
)

defineExpose({
  outerVisible,
  toggleSelection
})

defineOptions({ name: 'SelectUsers' })
</script>

<style scoped lang="scss">
.underline {
  height: 1px;
  background: #ebeef5;
  margin-bottom: 23px;
}

.selected {
  height: 78px;

  .left {
    width: 50px;
    float: left;
    font-weight: 400;
    font-size: 16px;
    color: #262626;
    line-height: 17px;
    text-align: left;
  }

  .right {
    height: 100%;
    width: calc(100% - 50px);
    float: right;
    background: #ffffff;
    border: 1px solid #ebeef5;
  }

  .item-all {
    padding: -3px 5px 5px 5px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
  }

  .item {
    margin-left: 5px;
    margin-right: 6px;
  }
}

.operate {
  margin-top: 24px;
  margin-bottom: 16px;

  .head {
    height: 34px;
    margin-bottom: 23px;

    .left {
      float: left;
      font-weight: 400;
      font-size: 16px;
      color: #262626;
      line-height: 32px;
      text-align: left;
    }

    .right {
      float: right;
    }
  }

  .body {
    display: flex;
    width: 100%;

    .tissue-tree {
      height: 407px;
      background: #ffffff;
      border: 1px solid #ebeef5;
      border-radius: 4px 4px 4px 4px;
    }

    .user-list {
      height: 407px;
      margin-left: 16px;
      width: calc(100% - 298px - 16px);
      border: 1px solid #ebeef5;
      border-radius: 4px 4px 4px 4px;
    }
  }
}

.w_btn_bg {
  background-color: rgba(82, 124, 255, 1) !important;
}
</style>
