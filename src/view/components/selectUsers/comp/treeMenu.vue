<template>
  <div class="tree_bgc">
    <div class="title">组织架构</div>
    <div class="h-[calc(100%-54px)] overflow-hidden">
      <el-scrollbar max-height="100%">
        <el-tree :default-expand-all="true" ref="tree" :default-expanded-keys="selectedKeys" :data="treeData"
          :highlight-current="true" :props="defaultProps" :current-node-key="currentCode" node-key="id"
          :expand-on-click-node="false" @node-click="handleSelect" :key="currentCode" :render-content="renderContent">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="left-wrap">
                <span :class="node.isLeaf ? 'leaf-node-icon' : 'parent-node-icon'">{{ data.text }}</span>
              </div>
            </div>
          </template>
          <template #empty>
            <div class="empty-wrap relative">
              <span class="empty-text">暂无数据</span>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, nextTick } from 'vue'
import $API from '~/common/api'
import store from './store'
import jianguan from '@/assets/image/examManagement/menu-father.png'
import yewu from '@/assets/image/examManagement/menu-child.png'
import dept from '@/assets/image/examManagement/dept.png'
import { useUserInfo } from '@/store'

const ui = useUserInfo()
const props = defineProps({
  superviseUnitId: {
    default: '',
    type: String
  },
  tabOptionFlag: {
    default: () => false,
    type: Boolean
  }
})
const defaultProps = {
  children: 'children',
  label: 'text'
}

const useStore = store()
const tree: any = ref()
const treeData: any = ref([])
// const tabActive: any = ref('1')
const selectedKeys = ref(['-1'])

const currentCode = ref('')

// 点击树节点
function handleSelect(_: any, e: any) {
  console.log(e, 'dianjile -----------------------')
  const node = e.data
  console.log(e.data, '*********----------nodecur-------')
  useStore.updateSelectedNode(node)
}
/**
 * 顶层为集团 则显示对应系统图标
 * 不为集团 为监管则显示监管 业务显示业务
 */
function getIcon() {
  return ui.value.zhLogoUrl + ui.value.zhLogo + '.png'
}

function renderContent(h, { data }) {
  let prefixIcon = data.attributes.orgType === '2' ? jianguan : data.attributes.orgType === '1' ? yewu : dept
  if (data.parentId === '-1') prefixIcon = getIcon()

  return h('div', { class: 'flex items-center pl-10px', title: data.text }, [
    h('img', {
      src: prefixIcon,
      class: 'w-[20px] h-[20px] inline-block mr-10px'
    }), // 自定义前缀图标
    h('span', { class: 'truncate' }, data.text) // 显示节点的标签
  ])
}
watch(
  () => useStore.refreshFlag,
  () => {
    findTreeData()
  }
)

// 树的列表
async function findTreeData() {
  const result: any = await $API.post({
    url: 'train-server/org/queryOrgTreeByTanzer'
  })
  console.log(result, '------------***************--------------------')
  if (result && result.code === 'success') {
    const _data = result.data
    console.log(_data, '////////////////', _data[0].id)
    currentCode.value = _data[0].id
    useStore.upadateCurrentCode(currentCode.value)
    // treeData.value = setTreeData(_tableData, '', 'org', '')
    treeData.value = _data
    console.log('treeData.value =======', treeData.value)
    // useStore.updateSelectedNode(searchNodeByKey(selectedKeys.value.length === 0 ? '0' : selectedKeys.value[0]))
  }
}

function fetchTreeData() {
  console.log(props.superviseUnitId, '+++***----props----------------------')
  // nextTick(() => tree.value.setCurrentKey(props.superviseUnitId))
  nextTick(() => tree.value.setCurrentKey(-1))
}

onMounted(async () => {
  await findTreeData()
  fetchTreeData()
})
</script>

<style lang="scss" scoped>
.tabs-wrap {
  text-align: center;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

.tree_bgc {
  height: 100%;
  background: white;
  border-radius: 6px;
  box-sizing: border-box;

  .title {
    padding-left: 24px;
    display: flex;
    width: 100%;
    height: 54px;
    align-items: center;
    /* 垂直居中 */
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    border-bottom: 1px solid #ebeef5ff;
  }

  .custom-tree-node {
    flex: 1;
    padding: 5px 0 5px 10px;
    user-select: none;
  }

  :deep(.el-input) {
    height: 40px;
    line-height: 40px;
    margin-bottom: 15px;
  }

  :deep(.el-tree) {
    .el-tree-node__content {
      margin-top: 4px;
      border-radius: 6px;
      height: auto;
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        &:first-child {
          display: none;
        }

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }

    .el-tree-node__children .el-tree-node__expand-icon:first-child {
      display: block;
    }
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;

  .left-wrap {
    flex: 1;
    position: relative;

    span {
      padding-left: 25px;
    }
  }

  .right-wrap {
    margin-right: 15px;
    color: #527cff;

    a {
      margin-left: 20px;
      display: inline-block;
    }
  }
}
</style>
