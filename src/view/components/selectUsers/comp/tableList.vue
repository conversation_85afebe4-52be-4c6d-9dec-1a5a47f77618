<template>
  <table-list
    ref="tableListRef"
    @current-change="currentChange"
    @size-change="handleSizeChange"
    :data="tableDataList"
    :pageModel="pagination"
    v-loading="loading"
    :loading="loading"
    @selection-change="handleSelectionChange"
    :pageNumber="20"
    :pageFlag="true"
    :showHeader="false"
    :userDefined="false"
    :columns="[]"
    stripe
  >
    <!--  @select="handleSelect"   @selection-change="handleSelectionChange"-->
    <el-table-column type="selection" width="40" :reserve-selection="true" align="center"></el-table-column>
    <el-table-column
      v-for="item in tableColumns"
      :label="item.label"
      :key="item.id"
      :type="item.type"
      :width="item.width"
      :property="item.property"
      show-overflow-tooltip
    >
      <template v-if="item.type !== 'index'" #default="scope">
        <template v-if="item.key === 'userType'">
          <template v-if="scope.row[item.key] === '02'">
            {{ ui.zhLogo === 'yanchang' ? '承包商员工' : '相关方员工' }}
          </template>
          <template v-else-if="scope.row[item.key] === '01'"> 鞍钢员工 </template>
          <template v-else>--</template>
        </template>
        <template v-else>
          {{ scope.row[item.key] !== 0 ? scope.row[item.key] || '--' : scope.row[item.key] }}
        </template>
      </template>
    </el-table-column>
  </table-list>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import { tableColumnsArr } from '../constant'
import store from './store'

// defineProps({
//   deptId: {
//     type: String,
//     default: '',
//   },
// })

const ui = useUserInfo()
const emits = defineEmits(['selectUser'])

const tableColumns = ref<any>(tableColumnsArr)
const useStore = store()
const tableListRef = ref(null)
const tableDataList = ref<any>([])
const loading = ref<boolean>(false)
const pagination = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 20
  // showSizeChanger: true,
  // showTotal: (total: any) => `共${total}条`,
})
const filterForm = ref<any>({
  keywords: ''
})

// 列表
async function getListData() {
  loading.value = true
  useStore.updateDisabled(true) // 结果没有返回不能操作
  const params: any = {
    // deptId: useStore.deptId || ui.unitId,
    orgCode: useStore.orgCode,
    pageNo: pagination.pageNo,
    pageSize: pagination.pageSize,
    ...filterForm.value,
    userType: useStore.treeTabs
  }
  try {
    const result: any = await $API.post({
      url: 'train-server/org/queryUserByDeptCode',
      params
    })
    if (result && result.code === 'success') {
      if (result.data.rows && result.data.rows.length > 0) {
        tableDataList.value = result.data.rows
      } else {
        tableDataList.value = []
      }

      pagination.total = result.data.total
      nextTick(() => {
        // tableListRef.value!.toggleSelectionChild(multipleSelection.value)
      })
    }
  } catch (error) {
  } finally {
    useStore.updateDisabled(false)
    loading.value = false
  }
}

// 分页切换
function currentChange(val: any) {
  pagination.pageNo = val
  getListData()
}

function handleSizeChange(val: any) {
  console.log(val)
  pagination.pageNo = 1
  pagination.pageSize = val
  getListData()
}

function getListDataWatch() {
  tableDataList.value = []
  pagination.pageNo = 1
  getListData()
}

function getListDataWatch2(filter: any) {
  filterForm.value = filter
  tableDataList.value = []
  pagination.pageNo = 1
  getListData()
}

function handleSelectionChange(val: any) {
  let newArr = goDuplicateArray(val)
  console.log('resArr----------', newArr)
  emits('selectUser', newArr)
}

// 去除选择重复的数组
function goDuplicateArray(val) {
  let obj = {}
  let arrNewSelection = val.reduce((a, b) => {
    obj[b.id] ? '' : (obj[b.id] = true && a.push(b))
    return a
  }, [])
  return arrNewSelection
}

function toggleSelectionChild(rows: any) {
  if (rows) {
    tableListRef.value!.toggleSelectionChild(rows)
  }
}

watch(
  () => useStore.orgCode,
  () => {
    getListDataWatch()
  }
)

// watch(
//   () => useStore.treeTabs,
//   () => {
//     tableDataList.value = []
//     pagination.total = 0
//     pagination.pageNo = 1
//     console.log('++++++++++默认cur变化了*tab************')
//     // getListDataWatch()
//     // getListDataWatch()
//   }
// )

watch(
  () => useStore.clearData,
  (newVal) => {
    if (newVal) {
      tableDataList.value = []
      pagination.total = 0
      pagination.pageNo = 1
    }
  },
  {
    deep: true
  }
)

getListData()

defineExpose({
  toggleSelectionChild,
  getListDataWatch2
})
</script>

<style lang="scss" scoped>
:deep(.el-tooltip__popper) {
  font-size: 12px;
}

.action-label {
  color: #2f54eb;
}

.tag {
  background: rgb(51 92 255);
  color: white;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  padding: 0 4px;
  margin-left: 10px;
}

.operate-item {
  font-size: 14px;
  color: #527cff;
  cursor: pointer;
}

:deep(.header-item_control) {
  width: 100% !important;
}

.pagination-wrap {
  display: flex;
  justify-content: center;
  padding-bottom: 0;

  .el-pagination {
    .el-input {
      .el-input__inner {
        padding-left: 0 !important;
      }
    }

    .el-select {
      height: 32px;
    }
  }
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.table-list_wrap) {
  padding-bottom: 0 !important;
}

:deep(.el-pagination) {
  padding-right: 20px;
  margin-top: 20px;
}

:deep(.el-table__header thead tr) {
  .el-table__cell {
    background-color: #fafafa !important;
  }
}

:deep(.el-table--striped .el-table__body tr.el-table__row td.el-table__cell) {
  background-color: #fff !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: #fafafa !important;
}
</style>
