<template>
  <div class="company-information">
    <div class="total-number-enterprises">
      <div class="sum">{{ totalNumber }}</div>
      <div class="description">企业总数</div>
    </div>
    <div class="item" v-for="item in correlationInfo" :key="item.id">
      <div class="item1">
        <div class="left">
          <el-image :src="dotIcon"></el-image>
          <span class="ml-8px">{{ item.title }}</span>
        </div>
        <!-- <div class="right" @click="jump(item)"><span>更多</span></div> -->
      </div>
      <div class="item2">
        <div class="pl-1px h-[30%]">
          <div class="item-left">{{ item.alreadyValue }}个</div>
          <div class="item-right">{{ item.notValue }}个</div>
        </div>
        <div class="h-[40%] middle">
          <el-progress :stroke-width="20" :show-text="false" :percentage="item.percentage"></el-progress>
        </div>
        <div class="pl-1px pt-5px h-[30%]">
          <div class="item-left">{{ item.alreadyDescription }}</div>
          <div class="item-right">{{ item.notDescription }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dotIcon from '../assets/dot2.png'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const totalNumber = ref(12) // 企业总数
const correlationInfo = ref() // 关联企业

const getDate = (orgCode: string) => {
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: '/edu-inter-server/resumeInfo/relatedPageList',
        data: { unitId: orgCode, serverStatus: '1', pageNo: 1, pageSize: -1 }
      })
      .then((res: any) => {
        totalNumber.value = res.data.serviceRelatedNum // 更新企业总数

        // 更新关联信息数组
        correlationInfo.value = [
          {
            id: '1',
            title: ui.value.zhLogo === 'yanchang' ? '承包商资质证书' : '相关方资质证书',
            alreadyDescription: '已上传数',
            notDescription: '待上传数',
            alreadyValue: res.data.uploadedCertificateNum,
            notValue: res.data.notUploadedCertificateNum,
            percentage:
              Math.round(
                (res.data.uploadedCertificateNum /
                  (res.data.uploadedCertificateNum + res.data.notUploadedCertificateNum)) *
                  100
              ) || 0
          },
          {
            id: '2',
            title: ui.value.zhLogo === 'yanchang' ? '承包商安全协议' : '相关方安全协议',
            alreadyDescription: '已上传数',
            notDescription: '待上传数',
            alreadyValue: res.data.uploadedProtocolNum,
            notValue: res.data.notUploadedProtocolNum,
            percentage:
              Math.round(
                (res.data.uploadedProtocolNum / (res.data.uploadedProtocolNum + res.data.notUploadedProtocolNum)) * 100
              ) || 0
          },
          {
            id: '3',
            title: ui.value.zhLogo === 'yanchang' ? '承包商员工信息' : '相关方员工信息',
            alreadyDescription: '已添加',
            notDescription: '待添加',
            alreadyValue: res.data.uploadedStaffInfoNum,
            notValue: res.data.notUploadedStaffInfoNum,
            percentage:
              Math.round(
                (res.data.uploadedStaffInfoNum / (res.data.uploadedStaffInfoNum + res.data.notUploadedStaffInfoNum)) *
                  100
              ) || 0
          },
          {
            id: '4',
            title: ui.value.zhLogo === 'yanchang' ? '承包商应急预案' : '相关方应急预案',
            alreadyDescription: '已上传数',
            notDescription: '待上传数',
            alreadyValue: res.data.uploadedEmergencyPlanNum,
            notValue: res.data.notUploadedEmergencyPlanNum,
            percentage:
              Math.round(
                (res.data.uploadedEmergencyPlanNum /
                  (res.data.uploadedEmergencyPlanNum + res.data.notUploadedEmergencyPlanNum)) *
                  100
              ) || 0
          }
        ]
        resolve(res.data.unitIds)
      })
      .catch((err: any) => {
        reject(err)
      })
  })
}

defineExpose({
  getDate
})
defineOptions({ name: 'CompanyInformation' })
</script>

<style scoped lang="scss">
.company-information {
  width: 100%;
  margin-bottom: 3.125rem;

  .total-number-enterprises {
    padding-top: 11px;
    width: 100%;
    height: 78px;
    background: url('../assets/total-number-enterprises.png') no-repeat;
    background-size: 100% 100%;

    .sum {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 600;
      font-size: 34px;
      color: #333333;
      line-height: 34px;
      text-transform: none;
    }

    .description {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 6px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 19px;
    }
  }

  .item {
    margin-top: 20px;
    padding: 3px;
    height: 150px;
    margin-bottom: 20px;
    background: linear-gradient(270deg, #99c2ff 0%, #c5dcff 98%);
    border-radius: 8px 8px 8px 8px;

    .item1 {
      height: calc(100% - 97px);

      .left {
        margin: 12px 0 0 11px;
        float: left;
        font-weight: 400;
        font-size: 16px;
        color: #2f62ff;
        line-height: 17px;
      }

      .right {
        cursor: pointer;
        width: 46px;
        height: 36px;
        padding-top: 10px;
        float: right;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 0 6px 0 16px;
        font-weight: 500;
        font-size: 16px;
        color: #2f62ff;
        line-height: 17px;
        text-align: center;
      }
    }

    .item2 {
      padding: 10px 11px 13px 13px;
      height: 97px;
      background: linear-gradient(226deg, #f0f6ff 0%, #f7fbff 100%);
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
      border-radius: 6px 6px 6px 6px;
      border-image: linear-gradient(223deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
      font-weight: 400;
      font-size: 14px;
    }
  }

  .item-left {
    float: left;
  }

  .item-right {
    float: right;
  }

  .middle {
    padding-top: 5px;
  }

  :deep(.el-progress-bar__inner) {
    background: linear-gradient(270deg, #8cb6ff 2%, #567fff 100%);
  }
}
</style>
