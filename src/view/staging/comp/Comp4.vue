<template>
  <div class="comp4">
    <HeadTitle title="作业排行榜分析"></HeadTitle>
    <div class="custom-body" :class="{ collapsed: props.collapsed }">
      <div>
        <el-table
          :data="table1"
          style="width: 100%; --el-table-border-color: none"
          height="325"
          :header-cell-style="headerCellStyle"
          :row-style="rowStyle"
          :cell-style="cellStyle"
        >
          <el-table-column prop="ranking" label="排名">
            <template #default="scope">
              <div class="no-all">
                <div :class="'no no' + scope.row.ranking">
                  {{ scope.row.ranking }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="企业名称" width="210" show-overflow-tooltip />
          <el-table-column prop="violationTotal" label="隐患作业总次数">
            <template #default="scope">
              <div class="table-cell-content">
                {{ scope.row.violationTotal ? scope.row.violationTotal + '次' : '0次' }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div>
        <el-table
          :data="table2"
          style="width: 100%; --el-table-border-color: none"
          height="325"
          :header-cell-style="headerCellStyle"
          :row-style="rowStyle"
          :cell-style="cellStyle"
        >
          <el-table-column prop="ranking" label="排名">
            <template #default="scope">
              <div class="no-all">
                <div :class="'no no' + scope.row.ranking">
                  {{ scope.row.ranking }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="企业名称" width="210" show-overflow-tooltip />
          <el-table-column prop="workTotal" label="作业总数">
            <template #default="scope">
              <div class="table-cell-content">
                {{ scope.row.workTotal ? scope.row.workTotal + '次' : '0次' }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HeadTitle from '@/components/HeadTitle/index.vue'
import { ref } from 'vue'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: true
  },
  table1: {
    type: Array,
    default: () => []
  },
  table2: {
    type: Array,
    default: () => []
  }
})

const headerCellStyle = ref({
  'text-align': 'center',
  'background-color': '#EBEEF5FF',
  'font-weight': '700',
  color: '#606266FF',
  'font-size': '14px',
  height: '48px'
})
const cellStyle = ref({
  'text-align': 'center',
  padding: '0',
  height: '48px',
  borderTop: '1px #F2F6FCFF solid'
})
const rowStyle = ref({ height: '48px' })

defineOptions({ name: 'Comp4' })
</script>

<style scoped lang="scss">
.comp4 {
  width: 100%;
  height: 100%;

  .no-all {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .no {
    padding-top: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 23px;
    background: url('../assets/no4.png') no-repeat;
    background-size: 100% 100%;
    font-weight: 700;
    font-size: 14px;
    line-height: 15px;
    color: #7a7e94ff;
  }

  .no1 {
    color: #d42121ff;
    background: url('../assets/no1.png') no-repeat;
    background-size: 100% 100%;
  }

  .no2 {
    color: #c24400ff;
    background: url('../assets/no2.png') no-repeat;
    background-size: 100% 100%;
  }

  .no3 {
    color: #cd6402ff;
    background: url('../assets/no3.png') no-repeat;
    background-size: 100% 100%;
  }

  .custom-body {
    margin-top: 22px;
    height: 100%;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: calc(100vw - 580px);
    transition: width 0.3s ease;

    > div {
      width: 49%;
    }

    &.collapsed {
      width: calc(100vw - 900px);
    }
  }

  .table-cell-content {
    cursor: pointer;
  }
}
</style>
