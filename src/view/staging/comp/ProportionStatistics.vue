<template>
  <div class="company-information">
    <div class="overview">
      <div v-for="item in overviewList" :key="item.id" class="overview-item">
        <div class="overview-item-left">
          <img :src="item.icon" alt="" />
        </div>
        <div class="overview-item-right">
          <div>
            <span class="count">{{ item.count }}{{ item.unit }}</span>
          </div>
          <div>
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { overviewArr } from '../constant'
import $API from '@/common/api'

const overviewList = ref<any>(overviewArr)

// 获取数据总览
async function getTrainData(orgCode: string, unitIds: []) {
  try {
    const res = await $API.post({
      url: '/edu-inter-server/resumeInfo/statistics',
      data: {
        pageNo: 1,
        pageSize: 1,
        unitId: orgCode,
        unitIds: unitIds && unitIds.length > 0 ? unitIds : null,
        type: 0
      }
    })

    if (res.code == '200') {
      overviewList.value = [
        {
          ...overviewArr[0],
          count: res.data.threeSafeRate || 0
        },
        {
          ...overviewArr[1],
          count: res.data.responsibilityRate || 0
        },
        {
          ...overviewArr[2],
          count: res.data.workViolationRate || 0
        }
      ]
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

defineExpose({
  getTrainData
})

defineOptions({ name: 'ProportionStatistics' })
</script>

<style scoped lang="scss">
.company-information {
  width: 100%;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  .title {
    display: flex;
    height: 50px;
    align-items: center; /* 垂直居中 */
    font-weight: 500;
    font-size: 16px;
    color: #242526;

    .vertical {
      margin-left: 24px;
      margin-right: 10px;
      width: 3px;
      height: 16px;
      background: #527cff;
      border-radius: 2px 2px 2px 2px;
    }
  }

  .underline {
    height: 1px;
    background: #ebeef5;
  }

  .overview {
    display: grid;
    width: 100%; /* 容器宽度 */
    grid-template-columns: repeat(3, 1fr); /* 创建3列，每列宽度相等 */
    gap: 30px; /* 网格项之间的间隙 */

    .overview-item {
      display: flex;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #d7dae0;

      .overview-item-left {
        width: 61px;
        display: flex;
        align-items: center; /* 垂直居中 */
        img {
          margin-left: 17px;
          width: 48px;
          height: 48px;
        }
      }

      .overview-item-right {
        height: 103px;
        width: calc(100% - 61px);
        padding-left: 15px;
        padding-top: 13px;
        span {
          font-weight: 600;
          font-size: 34px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}
</style>
