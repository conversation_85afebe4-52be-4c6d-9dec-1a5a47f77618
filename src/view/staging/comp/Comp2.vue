<template>
  <div class="comp2" v-loading="isLoading">
    <div style="width: 100%; height: 20%">
      <div class="left">
        <HeadTitle title="入场培训课程完成情况"></HeadTitle>
      </div>
      <div class="right item-all">
        <div class="item mr-20px">
          <div>
            <div class="title1 title">{{ obj.totalNumberParticipants }}</div>
            <div class="title2 title">参训总人次</div>
          </div>
        </div>
        <div class="item mr-20px">
          <div>
            <div class="title1 title">{{ obj.passedTotalNumberParticipants }}</div>
            <div class="title2 title">已通过参训总人次</div>
          </div>
        </div>
        <div class="item">
          <div>
            <div class="title1 title">{{ obj.notTotalNumberParticipants }}</div>
            <div class="title2 title">未参训总人次</div>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 100%; height: 80%">
      <barChart
        :echartsData="echartsData"
        :color="['rgba(56, 126, 255, 1)']"
        :graphicColor="[['rgba(96, 162, 255, 1)', 'rgba(56, 126, 255, 1)']]"
        :extra="extra"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import barChart from '@/components/charts/barChart.vue'
import HeadTitle from '@/components/HeadTitle/index.vue'
import { ref, watch } from 'vue'
import $API from '~/common/api'

const props = defineProps({
  unitIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  code: {
    type: String,
    default: ''
  }
})
const obj = ref<any>({
  totalNumberParticipants: '0',
  passedTotalNumberParticipants: '0',
  notTotalNumberParticipants: '0'
})
const echartsData = ref<Record<string, any>>({
  label: [],
  data: [
    {
      name: '参训通过率',
      value: [
        {
          id: '',
          value: 0,
          passed: 0,
          failed: 0
        }
      ]
    }
  ]
})
const isLoading = ref(false)

// 创建一个响应式对象，用于存储最近的请求参数
const lastRequestParams = ref({
  unitIds: null as any[] | null,
  code: '' as string
})

const extra = {
  grid: {
    bottom: '16.6%'
  },
  yAxis: {
    axisLabel: {
      formatter: (value: number) => {
        if (value !== 0) {
          return `${value} %`
        } else {
          return value
        }
      }
    }
  },
  legend: {
    show: false
  },
  tooltip: {
    formatter: (params: any) => {
      let dataStr = `<div><p >${params[0].name}</p></div>`
      params.forEach((item: any) => {
        dataStr += `<div style="min-width: 150px;">
          <div>
            <span>${item.seriesName}</span>
            <span style="color:#57617B; font-weight:bold; float: right">${item.data.value}%</span>
          </div>
          <div>
            <span>已通过</span>
            <span style="color:#57617B; font-weight:bold; float: right;">${item.data.passed}人次</span>
          </div>
          <div>
            <span>未通过</span>
            <span style="color:#57617B; font-weight:bold; float: right;">${item.data.failed}人次</span>
          </div>
        </div>`
      })
      return dataStr
    }
  },
  series: {
    dataZoom: [
      {
        type: 'slider',
        start: 0,
        end: 60,
        xAxisIndex: 0,
        height: 16,
        bottom: 10,
        borderColor: 'transparent',
        backgroundColor: '#F0F7FF',
        fillerColor: '#CCE4FF',
        handleColor: '#2F8FFF',
        showDataShadow: false,
        showDetail: false,
        startValue: 0,
        endValue: 5,
        filterMode: 'filter',
        width: '95%',
        left: 'center',
        zoomLoxk: true,
        handleSize: 30,
        handleStyle: {
          borderColor: '#2F8FFF',
          shadowBlur: 10,
          shadowOffsetX: 1,
          shadowOffsetY: 1,
          shadowColor: '#2F8FFF'
        },
        dataBackground: {
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            opacity: 0
          }
        },
        selectedDataBackground: {
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            opacity: 0
          },
          brushStyle: {
            borderColor: '#CCE4FF'
          }
        }
      }
    ]
  }
}

// 监听unitIds变化
watch(
  () => props.unitIds,
  (newUnitIds) => {
    // 比较新旧参数，只有参数真正变化时才发起请求
    if (JSON.stringify(newUnitIds) !== JSON.stringify(lastRequestParams.value.unitIds) && newUnitIds?.length) {
      // 更新最近的请求参数
      lastRequestParams.value.unitIds = newUnitIds

      isLoading.value = true
      $API
        .post({
          url: '/edu-inter-server/resumeInfo/trainerStatistics',
          data: { unitIds: newUnitIds }
        })
        .then((res) => {
          if (res.code === 'success') {
            Object.assign(obj.value, {
              totalNumberParticipants: res.data.trainerCount || '0',
              passedTotalNumberParticipants: res.data.passCount || '0',
              notTotalNumberParticipants: res.data.notTraninCount || '0'
            })
          }
        })
        .catch((error) => {
          console.error('获取培训统计数据失败:', error)
        })
        .finally(() => {
          isLoading.value = false
        })
    }
  },
  { immediate: true, deep: true }
)

// 监听code变化
watch(
  () => props.code,
  (newCode) => {
    // 比较新旧参数，只有参数真正变化时才发起请求
    if (newCode !== lastRequestParams.value.code && newCode) {
      // 更新最近的请求参数
      lastRequestParams.value.code = newCode

      isLoading.value = true
      $API
        .post({
          url: '/edu-inter-server/resumeInfo/trainer',
          data: {
            pageNo: 1,
            pageSize: -1,
            serverStatus: 1,
            unitId: newCode
          }
        })
        .then((res) => {
          if (res.code === 'success') {
            const data = res.data || []
            echartsData.value = {
              label: data.map((item: any) => item.deptName),
              data: [
                {
                  name: '参训通过率',
                  value: data.map((item: any) => ({
                    id: item.id,
                    value: item.passRate,
                    passed: item.passNum,
                    failed: item.notPassNum
                  }))
                }
              ]
            }
          }
        })
        .catch((error) => {
          console.error('获取培训详细数据失败:', error)
        })
        .finally(() => {
          isLoading.value = false
        })
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.comp2 {
  width: 100%;
  height: 100%;

  .right {
    float: right;
  }

  .left {
    float: left;
  }

  .item-all {
    margin-right: 4px;
    display: flex;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title1 {
    font-weight: 600;
    font-size: 20px;
    color: #1f2225;
    line-height: 22px;
  }

  .title2 {
    font-weight: 400;
    font-size: 14px;
    color: #484e5a;
    line-height: 22px;
  }

  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 60px;
    background: url('../assets/bj.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
