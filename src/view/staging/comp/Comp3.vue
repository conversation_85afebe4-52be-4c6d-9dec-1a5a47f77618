<template>
  <div class="comp3" v-loading="isLoading">
    <div style="width: 100%; height: 20%">
      <div class="left">
        <HeadTitle :title="ui.zhLogo === 'yanchang' ? '承包商规章制度查阅情况' : '相关方规章制度查阅情况'"></HeadTitle>
      </div>
      <div class="right item-all">
        <div class="item mr-20px">
          <div>
            <div class="title1 title">{{ obj.shouldTotalNumberRead }}</div>
            <div class="title2 title">应阅读总人次</div>
          </div>
        </div>
        <div class="item mr-20px">
          <div>
            <div class="title1 title">{{ obj.totalNumberConsulted }}</div>
            <div class="title2 title">已查阅总人次</div>
          </div>
        </div>
        <div class="item">
          <div>
            <div class="title1 title">{{ obj.notTotalNumberConsulted }}</div>
            <div class="title2 title">未查阅总人次</div>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 100%; height: 80%">
      <barChart
        :echartsData="echartsData"
        :color="['rgba(56, 126, 255, 1)']"
        :graphicColor="[['rgba(96, 162, 255, 1)', 'rgba(56, 126, 255, 1)']]"
        :extra="extra"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import barChart from '@/components/charts/barChart.vue'
import HeadTitle from '@/components/HeadTitle/index.vue'
import { ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const props = defineProps({
  unitIds: {
    type: Array,
    default: () => {
      return []
    }
  },
  code: {
    type: String,
    default: ''
  }
})
// 初始化数据结构
const obj = ref<any>({
  shouldTotalNumberRead: '0',
  totalNumberConsulted: '0',
  notTotalNumberConsulted: '0'
})

const echartsData = ref<Record<string, any>>({
  label: [],
  data: [
    {
      name: '查阅完成率',
      value: []
    }
  ]
})

const extra = {
  grid: {
    bottom: '16.6%'
  },
  yAxis: {
    axisLabel: {
      formatter: (value: number) => {
        if (value !== 0) {
          return `${value} %`
        } else {
          return value
        }
      }
    }
  },
  legend: {
    show: false
  },
  tooltip: {
    formatter: (params: any) => {
      let dataStr = `<div><p >${params[0].name}</p></div>`
      params.forEach((item: any) => {
        dataStr += `<div style="min-width: 150px;">
          <div>
            <span>${item.seriesName}</span>
            <span style="color:#57617B; font-weight:bold; float: right">${item.data.value}%</span>
          </div>
          <div>
            <span>已查阅</span>
            <span style="color:#57617B; font-weight:bold; float: right;">${item.data.passed}人次</span>
          </div>
          <div>
            <span>未查阅</span>
            <span style="color:#57617B; font-weight:bold; float: right;">${item.data.failed}人次</span>
          </div>
        </div>`
      })
      return dataStr
    }
  },
  series: {
    dataZoom: [
      {
        type: 'slider',
        start: 0,
        end: 60,
        xAxisIndex: 0,
        // 设置滚动条高度
        height: 16,
        bottom: 10,
        borderColor: 'transparent',
        // 设置背景颜色
        backgroundColor: '#F0F7FF',
        // 设置选中范围的填充颜色
        fillerColor: '#CCE4FF',
        // 设置边框颜色
        handleColor: '#2F8FFF',
        showDataShadow: false, //是否显示数据阴影 默认auto
        // 是否显示detail，即拖拽时候显示详细数值信息
        showDetail: false,
        // 数据窗口范围的起始数值
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 5,
        // empty：当前数据窗口外的数据，被设置为空。
        // 即不会影响其他轴的数据范围
        filterMode: 'filter',
        // 设置滚动条宽度，相对于盒子宽度
        width: '95%',
        // 设置滚动条显示位置
        left: 'center',
        // 是否锁定选择区域（或叫做数据窗口）的大小
        zoomLoxk: true,

        handleSize: 30,
        handleStyle: {
          borderColor: '#2F8FFF',
          shadowBlur: 10,
          shadowOffsetX: 1,
          shadowOffsetY: 1,
          shadowColor: '#2F8FFF'
        },
        dataBackground: {
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            opacity: 0
          }
        },
        selectedDataBackground: {
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            opacity: 0
          },
          brushStyle: {
            borderColor: '#CCE4FF'
          }
        }
      }
      // {
      //   // 没有下面这块的话，只能拖动滚动条，
      //   // 鼠标滚轮在区域内不能控制外部滚动条
      //   type: 'inside',
      //   // 滚轮是否触发缩放
      //   zoomOnMouseWheel: false,
      //   // 鼠标滚轮触发滚动
      //   moveOnMouseMove: true,
      //   moveOnMouseWheel: true,
      // },
    ]
  }
}
const isLoading = ref(false)
// 获取规章制度统计数据
const getRegulationsStatistics = async (unitIds: []) => {
  isLoading.value = true
  try {
    const res = await $API.post({
      url: '/edu-inter-server/resumeInfo/regulationsStatistics',
      data: { unitIds: unitIds && unitIds.length > 0 ? unitIds : null }
    })
    if (res.code === 'success') {
      obj.value = {
        shouldTotalNumberRead: res.data.readToal || '0',
        totalNumberConsulted: res.data.readCount || '0',
        notTotalNumberConsulted: res.data.notReadCount || '0'
      }
    }
  } catch (error) {
    console.error('获取规章制度统计数据失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 获取规章制度详细数据
const getRegulationsDetail = async (unitId: string) => {
  isLoading.value = true
  try {
    const res = await $API.post({
      url: '/edu-inter-server/resumeInfo/regulations',
      data: {
        pageNo: 1,
        pageSize: -1,
        unitId,
        serverStatus: 1
      }
    })
    if (res.code === 'success') {
      const data = res.data || []
      echartsData.value = {
        label: data.map((item: any) => item.deptName),
        data: [
          {
            name: '查阅完成率',
            value: data.map((item: any) => ({
              id: item.id,
              value: item.passRate,
              passed: item.passNum,
              failed: item.notPassNum
            }))
          }
        ]
      }
    }
  } catch (error) {
    console.error('获取规章制度详细数据失败:', error)
  } finally {
    isLoading.value = false
  }
}

watch(
  () => props.unitIds,
  (val = []) => {
    getRegulationsStatistics(val)
  },
  { immediate: true }
)
watch(
  () => props.code,
  (val = '') => {
    getRegulationsDetail(val)
  },
  { immediate: true }
)

// 暴露方法给父组件调用
defineExpose({
  getRegulationsStatistics,
  getRegulationsDetail
})
</script>

<style scoped lang="scss">
.comp3 {
  width: 100%;
  height: 100%;

  .right {
    float: right;
  }

  .left {
    float: left;
  }

  .item-all {
    margin-right: 4px;
    display: flex;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title1 {
    font-weight: 600;
    font-size: 20px;
    color: #1f2225;
    line-height: 22px;
  }

  .title2 {
    font-weight: 400;
    font-size: 14px;
    color: #484e5a;
    line-height: 22px;
  }

  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 60px;
    background: url('../assets/bj.png') no-repeat;
    background-size: 100% 100%;
  }
}
</style>
