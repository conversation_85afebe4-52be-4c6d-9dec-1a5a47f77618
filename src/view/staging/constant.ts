import defaultAvatar1 from './assets/1.png'
import defaultAvatar2 from './assets/2.png'
import defaultAvatar3 from './assets/3.png'

export const correlationInfoArr = [
  {
    id: '1',
    title: '相关方资质证书',
    alreadyDescription: '已上传数',
    notDescription: '待上传数',
    alreadyValue: 10,
    notValue: 33,
    percentage: 50,
    key: 'taskTotalNum',
    alreadyKey: 'numberUploads',
    notKey: 'numberToBeUploaded'
  },
  {
    id: '2',
    title: '相关方安全协议',
    alreadyDescription: '已上传数',
    notDescription: '待上传数',
    alreadyValue: 10,
    notValue: 33,
    percentage: 50,
    key: 'taskTotalNum',
    alreadyKey: 'numberUploads',
    notKey: 'numberToBeUploaded'
  },
  {
    id: '3',
    title: '相关方员工信息',
    alreadyDescription: '已上传数',
    notDescription: '待上传数',
    alreadyValue: 10,
    notValue: 33,
    percentage: 50,
    key: 'taskTotalNum',
    alreadyKey: 'numberUploads',
    notKey: 'numberToBeUploaded'
  },
  {
    id: '4',
    title: '相关方应急预案',
    alreadyDescription: '已上传数',
    notDescription: '待上传数',
    alreadyValue: 10,
    notValue: 33,
    percentage: 50,
    key: 'taskTotalNum',
    alreadyKey: 'numberUploads',
    notKey: 'numberToBeUploaded'
  }
]

export const overviewArr = [
  {
    id: '1',
    count: '0',
    icon: defaultAvatar1,
    unit: '%',
    description: '三级安全上传占比',
    key: 'planNum'
  },
  {
    id: '2',
    count: '0',
    icon: defaultAvatar2,
    unit: '%',
    description: '责任制签署情况',
    key: 'implementedTaskNum'
  },
  {
    id: '3',
    count: '0',
    icon: defaultAvatar3,
    unit: '%',
    description: '作业隐患占比',
    key: 'userNum'
  }
]

export const comp2 = {
  id: '1',
  totalNumberParticipants: '290',
  passedTotalNumberParticipants: '290',
  notTotalNumberParticipants: '5'
}

export const comp3 = {
  id: '1',
  shouldTotalNumberRead: '290',
  totalNumberConsulted: '290',
  notTotalNumberConsulted: '5'
}

export const tableData1 = [
  {
    id: '1',
    ranking: 1,
    unitName: '西安万威机械制造股份有限公司',
    totalNumberViolations: 1
  },
  {
    id: '2',
    ranking: 2,
    unitName: '陕西建工第一建设',
    totalNumberViolations: 2
  },
  {
    id: '3',
    ranking: 3,
    unitName: '延长石油榆林能化公司',
    totalNumberViolations: 3
  },
  {
    id: '4',
    ranking: 4,
    unitName: '石油管道运输公司',
    totalNumberViolations: 4
  },
  {
    id: '5',
    ranking: 5,
    unitName: '西安石油炼化公司',
    totalNumberViolations: 5
  },
  {
    id: '6',
    ranking: 6,
    unitName: '陕西工程服务科技有限公司',
    totalNumberViolations: 6
  },
  {
    id: '7',
    ranking: 7,
    unitName: '陕西石油建设有限公司',
    totalNumberViolations: 6
  },
  {
    id: '8',
    ranking: 8,
    unitName: '西安建设工程有限公司',
    totalNumberViolations: 6
  },
  {
    id: '9',
    ranking: 9,
    unitName: '榆林建筑工程服务有限公司',
    totalNumberViolations: 6
  },
  {
    id: '10',
    ranking: 10,
    unitName: '榆林气田管理有限公司',
    totalNumberViolations: 10
  },
  {
    id: '11',
    ranking: 11,
    unitName: '海安橡胶集团股份公司榆林分公司',
    totalNumberViolations: 11
  },
  {
    id: '12',
    ranking: 12,
    unitName: '阜新环宇橡胶（集团）有限公司',
    totalNumberViolations: 12
  }
]

export const tableData2 = [
  {
    id: '1',
    ranking: 1,
    userName: '李忠华',
    totalNumberViolations: 1
  },
  {
    id: '2',
    ranking: 2,
    userName: '赵洋',
    totalNumberViolations: 2
  },
  {
    id: '3',
    ranking: 3,
    userName: '雷雨',
    totalNumberViolations: 3
  },
  {
    id: '4',
    ranking: 4,
    userName: '郑悦',
    totalNumberViolations: 4
  },
  {
    id: '5',
    ranking: 5,
    userName: '徐文龙',
    totalNumberViolations: 5
  },
  {
    id: '6',
    ranking: 6,
    userName: '张冠平',
    totalNumberViolations: 6
  },
  {
    id: '7',
    ranking: 7,
    userName: '李科，卢玉明',
    totalNumberViolations: 7
  },
  {
    id: '8',
    ranking: 8,
    userName: '周恩哲、雷明',
    totalNumberViolations: 8
  },
  {
    id: '9',
    ranking: 9,
    userName: '胡宝兵',
    totalNumberViolations: 9
  },
  {
    id: '10',
    ranking: 10,
    userName: '张耿城',
    totalNumberViolations: 10
  },
  {
    id: '11',
    ranking: 11,
    userName: '朱文辉',
    totalNumberViolations: 11
  },
  {
    id: '12',
    ranking: 12,
    userName: '郭晓峰',
    totalNumberViolations: 12
  }
]

export const echartsList = {
  label: [
    '西安万威机械制造股份有限公司',
    '陕西建工第一建设',
    '延长石油榆林能化公司',
    '石油管道运输公司',
    '西安石油炼化公司',
    '陕西工程服务科技有限公司',
    '陕西矿山建设有限公司',
    '西安建设剥岩工程有限公司',
    '辽宁程鞍建筑工程服务有限公司',
    '矿业爆破有限公司',
    '海安橡胶集团股份公司榆林分公司',
    '阜新环宇橡胶（集团）有限公司'
  ],
  data: [
    {
      name: '参训通过率',
      value: [
        {
          id: '1',
          value: 30,
          passed: 10,
          failed: 20
        },
        {
          id: '2',
          value: 60,
          passed: 30,
          failed: 30
        },
        {
          id: '3',
          value: 80,
          passed: 20,
          failed: 70
        },
        {
          id: '4',
          value: 52,
          passed: 21,
          failed: 31
        },
        {
          id: '5',
          value: 73,
          passed: 20,
          failed: 53
        },
        {
          id: '6',
          value: 64,
          passed: 11,
          failed: 53
        },
        {
          id: '7',
          value: 30,
          passed: 10,
          failed: 20
        },
        {
          id: '8',
          value: 60,
          passed: 30,
          failed: 30
        },
        {
          id: '9',
          value: 80,
          passed: 20,
          failed: 70
        },
        {
          id: '10',
          value: 52,
          passed: 21,
          failed: 31
        },
        {
          id: '11',
          value: 73,
          passed: 20,
          failed: 53
        }
      ]
    }
  ]
}

export const echartsList3 = {
  label: [
    '西安万威机械制造股份有限公司',
    '陕西建工第一建设',
    '延长石油榆林能化公司',
    '石油管道运输公司',
    '西安石油炼化公司',
    '西安建设剥岩工程有限公司',
    '陕西矿山建设有限公司',
    '西安建设剥岩工程有限公司',
    '辽宁程鞍建筑工程服务有限公司',
    '矿业爆破有限公司',
    '海安橡胶集团股份公司榆林分公司',
    '阜新环宇橡胶（集团）有限公司'
  ],
  data: [
    {
      name: '查阅完成率',
      value: [
        {
          id: '1',
          value: 30,
          passed: 10,
          failed: 20
        },
        {
          id: '2',
          value: 60,
          passed: 30,
          failed: 30
        },
        {
          id: '3',
          value: 80,
          passed: 20,
          failed: 70
        },
        {
          id: '4',
          value: 52,
          passed: 21,
          failed: 31
        },
        {
          id: '5',
          value: 73,
          passed: 20,
          failed: 53
        },
        {
          id: '6',
          value: 64,
          passed: 11,
          failed: 53
        },
        {
          id: '7',
          value: 30,
          passed: 10,
          failed: 20
        },
        {
          id: '8',
          value: 60,
          passed: 30,
          failed: 30
        },
        {
          id: '9',
          value: 80,
          passed: 20,
          failed: 70
        },
        {
          id: '10',
          value: 52,
          passed: 21,
          failed: 31
        },
        {
          id: '11',
          value: 73,
          passed: 20,
          failed: 53
        }
      ]
    }
  ]
}
