<template>
  <!-- <el-scrollbar :height="windowSize.height.value - 50 + 'px'"> </el-scrollbar> -->
  <div class="all w-full h-full flex" :style="{ paddingLeft: collapsed ? '0' : '15px' }">
    <div class="h-full ml-[10px]" v-show="collapsed && ui.unitOrgType !== '1'">
      <el-card class="mr-[10px] org-tree">
        <train-tree :collapsed="collapsed" @serach="searchData" class="p-20px" />
      </el-card>
    </div>
    <div class="w_main_box relative flex">
      <!--   企业信息跟进   -->
      <div class="left h-full overflow-hidden" :class="{ expanded: !collapsed }">
        <div style="margin-bottom: 19px">
          <HeadTitle title="企业信息跟进"></HeadTitle>
        </div>
        <el-scrollbar>
          <CompanyInformation ref="companyInformationRef"></CompanyInformation>
        </el-scrollbar>
        <div class="expand" @click="collapsedTree" v-if="ui.unitOrgType !== '1'"></div>
      </div>
      <div class="right" :style="{ width: collapsed ? 'calc(100% - 323px)' : '100%' }">
        <el-scrollbar>
          <div class="percentage1">
            <!-- 相关方占比统计 -->
            <div class="percentage-top">
              <ProportionStatistics ref="proportionStatisticsRef"></ProportionStatistics>
            </div>
            <div class="percentage-bottom"></div>
          </div>
          <!-- 入场培训课程完成情况 -->
          <div class="percentage3">
            <div class="percentage-top" :key="Date.now()">
              <Comp2 ref="comp2Ref" :unitIds="unitIds" :code="orgCode"></Comp2>
            </div>
            <div class="percentage-bottom"></div>
          </div>
          <!-- 相关方规章制度查阅情况 -->
          <div class="percentage3">
            <div class="percentage-top" :key="Date.now()">
              <Comp3 ref="comp3Ref" :unitIds="unitIds" :code="orgCode"></Comp3>
            </div>
            <div class="percentage-bottom"></div>
          </div>
          <!-- 作业违规排行榜分析 -->
          <div class="percentage4">
            <div class="percentage-all">
              <Comp4 :collapsed="collapsed" :table1="tableData1" :table2="tableData2" />
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HeadTitle from '@/components/HeadTitle/index.vue'
import TrainTree from '@/components/tree/index.vue'
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import Comp2 from './comp/Comp2.vue'
import Comp3 from './comp/Comp3.vue'
import Comp4 from './comp/Comp4.vue'
import CompanyInformation from './comp/CompanyInformation.vue'
import ProportionStatistics from './comp/ProportionStatistics.vue'

const ui: any = useUserInfo()
const orgCode = ref<string>(ui.value.unitId)
const collapsed = ref(true)
// 企业违规排行榜
const tableData1 = ref<any>([])
// 员工违规排行榜
const tableData2 = ref<any>([])
const companyInformationRef = ref<any>(null)
const proportionStatisticsRef = ref<any>(null)
const comp2Ref = ref<any>(null)
const comp3Ref = ref<any>(null)
// 接受树结构选中数据
const searchData = (obj) => {
  if (orgCode.value !== obj.orgCode) {
    orgCode.value = obj.orgCode
    initData()
  }
}
const unitIds = ref<string[]>([])
// 初始化数据
const initData = async () => {
  // 这地方后端接口写的垃圾，有的需要传orgCode（在子组件里叫unitid），有的需要传unitIds
  // 获取企业信息跟进
  unitIds.value = await companyInformationRef.value.getDate(orgCode.value)
  console.log(unitIds, 'unitIds')
  // 获取作业违规排行榜企业违规排行榜
  getStagingWorkViolationRankingAnalysisData1()
  // 获取作业违规排行榜员工违规排行榜
  getStagingWorkViolationRankingAnalysisData2(unitIds.value)
  // 获取相关方占比统计
  proportionStatisticsRef.value.getTrainData(orgCode.value, unitIds.value)
  // 获取培训相关数据
  // comp2Ref.value?.getTrainerStatistics(unitIds.value)
  // comp2Ref.value?.getTrainerDetail(orgCode.value)
  // 获取规章制度相关数据
  // comp3Ref.value?.getRegulationsStatistics(unitIds)
  // comp3Ref.value?.getRegulationsDetail(orgCode.value)
}

// 获取作业违规排行榜企业违规排行榜
const getStagingWorkViolationRankingAnalysisData1 = async () => {
  const res: any = await $API.post({
    url: '/edu-inter-server/resumeInfo/relatedWorkList',
    data: {
      unitId: orgCode.value, // 传orgCode
      serverStatus: 1
    }
  })
  if (res.code === 'success') {
    if (res.data.rows) {
      res.data.rows.forEach((item: any, index: number) => {
        item.ranking = index + 1
      })
    }
    tableData1.value = res.data.rows || []
  }
}

// 获取作业违规排行榜员工违规排行榜
const getStagingWorkViolationRankingAnalysisData2 = async (unitIds: []) => {
  const res: any = await $API.post({
    url: '/edu-inter-server/resumeInfo/queryXgfOperStatistic1',
    params: {
      orgCode: orgCode.value
    }
  })
  if (res.code == 200) {
    if (res.data) {
      res.data.forEach((item: any, index: number) => {
        item.ranking = index + 1
      })
      // 只取前10条数据
      tableData2.value = res.data.slice(0, 10) || []
    } else {
      tableData2.value = []
    }
  }
}
const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

watch(
  () => ui.value.unitOrgType,
  (val) => {
    if (val == '1') collapsed.value = false
  },
  { immediate: true }
)
onMounted(() => {
  initData()
})
defineOptions({ name: 'StagingPage' })
</script>

<style lang="scss" scoped>
.w_main_box {
  width: 100%;
  height: 100%;
}

.all {
  padding-bottom: 4.375rem;

  .org-tree {
    width: 323px;
    background-color: #ffffff;
    height: 100%;
    overflow: hidden;

    .top-unit-active {
      border-radius: 6px;
      padding: 5px 0;
      white-space: pre-line;
      background-color: rgba(82, 124, 255, 0.1);
      color: #527cff;
    }

    :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
      background-color: rgba(82, 124, 255, 0.1);
      color: #527cff;
    }
  }

  .left {
    padding: 13px 12px 0 13px;
    width: 300px;
    height: 100%;
    background-color: #ffffff;
    border-radius: 4px;
    margin-right: 10px;
    :deep(.el-scrollbar) {
      height: calc(100vh - 133px);
    }

    .expand {
      background: url('@/assets/expand.png') no-repeat;
      width: 34px;
      height: 36px;
      background-size: 100% 100%;
      position: absolute;
      top: 50%;
      left: -17px;
      transform: translateY(-50%);
      cursor: pointer;
    }

    &.expanded {
      grid-column: 1 / 3;
      width: 100%;
    }
  }

  .right {
    // height: calc(100vh - 105px);
    // flex: 1;

    div {
      width: 100%;
    }

    .percentage1 {
      height: 150px;
    }

    .percentage3 {
      width: 100%;
      height: 22.875rem;
    }

    .percentage-top {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 16px;
      width: 100%;
      height: calc(100% - 20px);
      background-color: #ffffff;
      border-radius: 4px;
    }

    .percentage-bottom {
      height: 20px;
    }

    .percentage-all {
      padding: 16px;
      height: 100%;
      background-color: #ffffff;
      border-radius: 4px;
    }
  }
}

// 左边‘企业信息跟进’高度
:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}
</style>
