<template>
  <div class="w-full h-full relative">
    <!-- <div class="h-[15px] absolute top-[0] z-20" @click="loadFloorData">6666666666</div> -->

    <div class="w-full h-full relative" ref="ElGIs"></div>
    <div v-if="isAddMark" class="flex justify-end w-full pb-5 mt-10 pr-5">
<!--      <n-button @click="addMarkbtn" type="primary" style="width: 92px"> 确 定 </n-button>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { ACTION } from '@/views/hidden-dangers-photo/constant';
import { GISOBJ } from './gisGlobal';
import gisPopup from './gisPopup.vue';
const ElGIs = ref();
interface DeviceItem {
  [key: string]: string | number;
}
interface Props {
  deviceList?: DeviceItem[];
  floorInfo?: {
    unitId: string;
    buildingId: string;
    floorId: string;
    floorAreaImg?: string;
  };
  pointer?: {
    x: number;
    y: number;
  };
  isAddMark?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  deviceList: () => [],
  isAddMark: false,
});
const emits = defineEmits(['addMark']);
const floorData = computed(() => {
  return props?.floorInfo;
});
const pointer = computed(() => {
  return props?.deviceList || [];
});
const defPonter = computed(() => {
  return props?.pointer || {};
});

watch(
  () => floorData.value,
  (val) => {
    if (val?.unitId && val.buildingId && val.floorId) {
      loadFloorData();
    }
  },
  {
    deep: true,
  }
);

watch(
  () => pointer.value,
  () => {
    loadFloorData();
  },
  {
    deep: true,
  }
);
watch(
  () => defPonter.value,
  () => {
    if (props.isAddMark) {
      addDeftPointer();
    }
  },
  {
    deep: true,
  }
);
const addDeftPointer = () => {
  gisMap3DM.addNewMark({ ...defPonter.value, z: 0 });
};
let popup: any;

let gisMap3DM: any;
const initGis = () => {
  ElGIs.value.appendChild(GISOBJ.getDom());
  gisMap3DM = GISOBJ.getIndoorMap();
  gisMap3DM.clearAll();

  (window as any).gisMap3DM = gisMap3DM;
  GISOBJ.render();
  gisMap3DM.clearPopup();
};

const loadFloorData = () => {
  //渲染平面图

  gisMap3DM.showFloorData(
    // props.isAddMark ? window.IndoorMap.ViewType.IndoorAreaVector2D : window.IndoorMap.ViewType.IndoorAreaVector,
    window.IndoorMap.ViewType.IndoorAreaVector2D,
    floorData.value?.unitId ?? undefined, //单位id
    floorData.value?.buildingId ?? undefined, //楼栋id
    floorData.value?.floorId, //楼层id
    // undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省）
    floorData.value?.floorAreaImg ? gisMap3DM.base_url + '/img1/floorImage/' + floorData.value.floorAreaImg : undefined,
    function (mapType: string, success: any, objArgs: any, indoor: any) {
      GISOBJ.render();
      indoor.showFloorDataDevice(pointer.value ?? [], 'mapX', 'mapY', 'mapZ', function filter() {
        // 过滤条件，可对数据做修改，返回true表示去掉此条数据
        // item, markFieldNameX, markFieldNameY
        return false;
      });
    }
  );
};
let addMarkList: any = null;
const pointerData = ref<{
  x?: number;
  y?: number;
  text?: string;
}>({
  x: 0,
  y: 0,
  text: '',
});
const bandEvent = () => {
  gisMap3DM.onMouseClick = function (e: any) {
    if (!props.isAddMark) return;
    gisMap3DM.clearNewMark();
    GISOBJ.render();
    const pointer = gisMap3DM.getMap().getView().ClientToScene(new window.THREE.Vector2(e.getX(), e.getY()), 0);
    console.log('🚀 ~ bandEvent ~ pointer:', pointer);
    const { x, y } = pointer;
    gisMap3DM.addNewMark({ x, y, z: 0 });
    pointerData.value.x = x;
    pointerData.value.y = y;
  };
  //楼层网格点击事件
  gisMap3DM.onGridSelected = function (data: any, e: any, obj: any, target: any) {
    console.log('🚀 ~ bandEvent ~ target:楼层网格点击事件', target);
    console.log('🚀 ~ bandEvent ~ obj:楼层网格点击事件', obj);
    console.log('🚀 ~ bandEvent ~  e:楼层网格点击事件', e);
    console.log('🚀 ~ bandEvent ~ data:楼层网格点击事件', data);
  };
  //楼层图斑点击事件
  gisMap3DM.onAreaSelected = function (data: any, e: any, obj: any, target: any) {
    console.log('🚀 ~ bandEvent ~ data:', data);
    pointerData.value.text = data.text;
    console.log('🚀 ~ bandEvent ~ 楼层图斑点击事件.point:', target.point);
  };
  gisMap3DM.onNullSelected = function (data: any) {};
  //设备点位图标 点击事件
  gisMap3DM.onDeviceSelected = function (data: any, e: any, obj: any, target: any) {};
};
const addMarkbtn = () => {
  if (!addMarkList) addMarkList = JSON.parse(JSON.stringify(defPonter.value));
  emits('addMark', pointerData.value);
};
onMounted(() => {
  initGis();
  bandEvent();

  setTimeout(() => {
    loadFloorData();
    if (props.isAddMark) {
      addDeftPointer();
    }
  }, 500);
});

defineOptions({ name: 'floorGis' });
</script>

<style module lang="scss"></style>
