import { createApp, createVNode, render as vRender } from 'vue'
import gisPopup from './gisPopup.vue'

// const base_url = '';
// const base_url = '/aqsc/v1';
const base_url = window.location.host !== 'agjp.tanzervas.com' ? '' : '/aqsc/v1'
const gisConfig = {
  tileURL: base_url + '/api/v3/gissetter-service/TileSetter/tileSetter?tsName=GS-Map&x={x}&y={y}&z={z}',
  center: [13055414.38723415, 3710265.1970611503]
}
const { tileURL, center } = gisConfig
const dom = document.createElement('div')
// dom.className = 'h-full w-full';
dom.style.height = '100%'
dom.style.width = '100%'

window.IndoorThree.init()
const mapOnLoad = () => {
  console.log('🚀 ~ mapOnLoad:')
}

const CONST_GSOptions = {
  deviceFieldNameX: 'mapx', //设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
  deviceFieldNameY: 'mapy', //设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
  deviceFieldNameX0: 'longitude', //设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
  deviceFieldNameY0: 'latitude', //设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）
  deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
  videoBufferQueryVideoTypeCode: '25030000'
}
//
const CONST_GSOptions2 = {
  dbService: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/indoorMap'), //室内GIS服务地址
  dbService_Record: window.newIndoorService(base_url + '/api/v3/bw-svc-indoor-gis-service/record'), //室内GIS服务地址（电子档案相关）
  deviceIconAlarmGifUrl: base_url + '/api/v3/v3-img1-online/img1/alarm.gif', //【因存在跨域问题，故单独设置跳转】'http://112.27.198.15:9862/image/deviceIcons/gif/alarm.gif', //报警设备图标
  // wmsURL: 'http://112.27.198.15:9862/geoserver/GS/wms', //后端渲染地址
  unitUrlHeader: base_url + '/img1/floorImage',
  deviceIconUrlHeader: base_url + '/img1/deviceIcons/_v3.0',
  gridTypeIds: IndoorMap.GridAreaType.GridAreaLayerFX,
  //【3.0 使用该配置】
  // videoBufferQueryVideoTypeCode: '25030000',//【3.0 使用该配置】
  // deviceFieldNameState: 'priorityEventType', //【3.0 使用该配置】 eventType priorityEventType
  // deviceFieldNameOnlineState: undefined,//【3.0 使用该配置】
  // deviceStateValueConvertFun: CONST_Function_DeviceStateValueConvertFun_Default_3,//【3.0 使用该配置】
  deviceFieldNameX: 'mapx', //设备室内GIS模式X轴坐标字段，默认值：mapX（按实际情况填写）
  deviceFieldNameY: 'mapy', //设备室内GIS模式Y轴坐标字段，默认值：mapY（按实际情况填写）
  deviceFieldNameX0: 'longitude', //设备室内图纸模式X轴坐标字段，默认值：longitude（按实际情况填写）
  deviceFieldNameY0: 'latitude', //设备室内图纸模式Y轴坐标字段，默认值：latitude（按实际情况填写）
  deviceStateValueConvertFun: window.CONST_Function_DeviceStateValueConvertFun_Default_3
}

const option = window.IndoorMap.Merge([{}, CONST_GSOptions2, CONST_GSOptions], {
  tile: false, //底图是否可见
  sky: true, //开启天空
  target: dom,
  tileURL: tileURL,
  minZoom: 4,
  zoom: 15,
  center: center,
  isVector: true,
  deviceIconAlarmGifUrl: '',
  multiWorld: false,
  // dbService_GeoSpatial: config.gis_url + '/api/v3/bw-svc-indoor-gis-geospatial-service/GeoSpatial',
  // ovUnitModelUrlHeader: config.gisUnitModel,
  onLoad: mapOnLoad
})
const gisMap3DM = new window.IndoorThree(option)
const getIndoorMap = () => {
  return gisMap3DM
}
const getDom = () => {
  return dom
}
const render = () => {
  gisMap3DM.render()
}
const initDom = (traget: HTMLElement) => {
  traget.appendChild(dom)
}
let popup
const strHTML = document.createElement('div')
strHTML.classList.add('Build-popup')
strHTML.innerHTML = `<div id="BuildPopup"></div>`
const getPopup = () => {
  popup = gisMap3DM.addPopup({
    width: 32,
    height: 32,
    custom: true,
    offset: [0, -10],
    positioning: 'bottom-center',
    element: strHTML
  })
  return popup
}

let el: HTMLElement | null = null
const setPopup = (config: { options: any; coms: any }) => {
  const { options, coms } = config
  if (!strHTML) return
  if (el) {
    el.remove()
    el = null
  }
  const PopUpConstructor = createVNode(coms, {
    ...options
  }) // 返回一个vue子类
  //创建实例并且挂载
  el = document.createElement('div')
  vRender(PopUpConstructor, el)
  strHTML.appendChild(el)
  return el
}

export const GISOBJ = {
  base_url,
  getIndoorMap: getIndoorMap,
  getDom,
  render,
  initDom,
  getPopup,
  setPopup
}

// dbService: newIndoorService(window.CONST_GSUrlHeader.gsUrlHeader + '/api/v3/bw-svc-indoor-gis-service/indoorMap'),
//     dbService_Record: newIndoorService(
//       window.CONST_GSUrlHeader.gsUrlHeader + '/api/v3/bw-svc-indoor-gis-service/record'
//     ),
//     dbService_GeoSpatial: newIndoorService(
//       window.CONST_GSUrlHeader.gsUrlHeader + '/api/v3/bw-svc-indoor-gis-service/GeoSpatial'
//     ),
//     dbService_Analysis: newIndoorService(
//       window.CONST_GSUrlHeader.gsUrlHeader + '/api/v3/bw-svc-indoor-gis-service/indoorMapAnalysis'
//     ),
//     unitUrlHeader: window.CONST_GSUrlHeader.gsUrlHeader + '/img1/floorImage',
//     deviceIconUrlHeader: window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/_v3.0',
//     sky: false,
//     deviceIconAlarmGifUrl: window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/gif/alarm.gif',
//     skyUrl: [
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/right.jpg',
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/left.jpg',
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/back.jpg',
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/front.jpg',
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/up.jpg',
//       window.CONST_GSUrlHeader.gsUrlHeader + '/img1/deviceIcons/z/sky/box_z/7/down.jpg',
//     ],
//     ovUnitModelUrlHeader: window.CONST_GSUrlHeader.gsUrlHeader + '/img1/indoor',
//     wmsURL: window.CONST_GSUrlHeader.gsUrlHeader + '/geoserver/GS/wms',
//     videoBufferQueryVideoTypeCode: '25030000',
//     deviceFieldNameState: 'priorityEventType', // eventType priorityEventType
//     deviceFieldNameOnlineState: undefined,
//     deviceStateValueConvertFun: CONST_Function_DeviceStateValueConvertFun_Default_3,
//     gridLoad: false,
