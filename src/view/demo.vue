<!-- fireAlarmAnalysis 火警-->
<template>
  <div class="w-full h-full flex flex-col">
    <div class="main_search">
      <header-item title="末次接收时间">
        <el-date-picker
          v-model="timeAll"
          type="daterange"
          clearable
          value-format="YYYY/MM/DD"
          @change="timeChange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </header-item>

      <header-item title="火警编号">
        <el-input v-model="searchFrom.disposeId" placeholder="请输入火警编号" clearable @change="getTableData" />
      </header-item>

      <header-item title="处置动作">
        <el-select v-model="searchFrom.disposeAction" clearable placeholder="全部" @change="getTableData">
          <el-option v-for="item in disposalActionList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>
      <header-item title="处置结果">
        <el-select v-model="searchFrom.disposeResult" clearable placeholder="全部" @change="getTableData">
          <el-option v-for="item in disposalResultsList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>
      <header-item title="处置结果">
        <el-select v-model="searchFrom.disposeResult" clearable placeholder="全部" @change="getTableData">
          <el-option v-for="item in disposalResultsList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>
      <header-item title="处置结果">
        <el-select v-model="searchFrom.disposeResult" clearable placeholder="全部" @change="getTableData">
          <el-option v-for="item in disposalResultsList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>
      <header-item title="处置结果">
        <el-select v-model="searchFrom.disposeResult" clearable placeholder="全部" @change="getTableData">
          <el-option v-for="item in disposalResultsList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </header-item>

      <div class="export action" style="margin-bottom: 0">
        <el-button type="primary" class="export-btn"> 导出 </el-button>
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <table-list
        @current-change="currentChange"
        @size-change="handleSizeChange"
        :data="tableData"
        v-loading="loading"
        :pageModel="pageModel"
        stripe
      >
        <el-table-column
          v-for="(item, index) in columns"
          :prop="item.key"
          :label="item.title"
          :width="item.width"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
          :key="index"
        >
          <template #header v-if="['disposeActionStr'].includes(item.key)">
            <div class="flex items-center">
              <div class="mr-3px">
                {{ item.title }}
              </div>
              <div class="cursor-pointer flex items-center">
                <el-tooltip placement="top-end">
                  <template #content>
                    <p>现场核警：由企业单位从消防管家APP进行的核警操作；</p>
                    <p>值守核警：由监测值守服务进行的核警操作。</p>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>
          <template #default="scope">
            {{ scope.row[item.key] }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <div class="table-operate">
              <span class="operate-item" track @click="handleDetails(scope.row)">详情</span>
              <span class="operate-item" track @click="examineLocation(scope.row)">位置</span>
            </div>
          </template>
        </el-table-column>
      </table-list>
    </div>
  </div>
</template>

<script lang="ts" setup name="fire">
import { onMounted, ref, reactive } from 'vue'
import $API from '@/common/api'
import { useUserInfo } from '@/store'

const userInfo = useUserInfo()

const props = defineProps({
  disposeId: {
    type: String,
    default: '',
  },
  propSearch: {
    type: Object,
    default: () => {},
  },
  searchData: {
    type: Object,
    default: () => {},
  },
})

// 分页
const pageModel = reactive<{
  pageNo: number
  pageSize: number
  total: number
}>({
  pageNo: 1,
  pageSize: 10,
  total: 999,
})
const disposalActionList: any = ref([
  {
    value: '1',
    label: '现场核警',
  },
  {
    value: '2',
    label: '值守核警',
  },
])
const disposalResultsList: any = ref([
  {
    value: '1',
    label: '真警',
  },
  {
    value: '2',
    label: '误报',
  },
  {
    value: '3',
    label: '忽略',
  },
])

const searchFrom = reactive({
  chargeId: '',
  unitId: '', // 单位id
  deviceTypePid: '', // 系统类型
  deviceTypeId: '' as any, // 设备类型
  fireResult: '', // 企业核警结果
  disposeId: props.disposeId, // 火警编号
  operationResult: '', // 监测值守核警结果
  eventTimeStart: '', //末次接收时间-开始时间
  eventTimeEnd: '', //末次接收时间-结束时间
  unitType: '0',
  superviseId: userInfo.value.orgCode, // 接口请求把分管单位id赋值给该参数
  disposeAction: '', // 处置动作
  disposeResult: '', // 处置结果
  isRegular: '', // 是否规范
})

// 离线时间
const timeAll: any = ref([])
timeAll.value = $API.getDays(30)
searchFrom.eventTimeStart = timeAll.value[0] + ' 00:00:00'
searchFrom.eventTimeEnd = timeAll.value[1] + ' 23:59:59'

const popupShow = ref(false)

const loading = ref<boolean>(false)

const tableData = ref<any>([])
const columns = ref<any>([])

// 位置
function examineLocation(info: any) {
  if (info.value?.onlineState == '离线') {
    info.eventType = '7'
  }
}

/**
 * 点击详情按钮
 * @param item 当前点击的表格的行数据
 */
const deviceInfo: any = ref({})
function handleDetails(item: any) {
  deviceInfo.value = item
  popupShow.value = true
}

onMounted(async () => {
  getTableColumns()
  getTableData()
})

function timeChange(val: any) {
  searchFrom.eventTimeStart = ''
  searchFrom.eventTimeEnd = ''
  if (val && val.length > 0) {
    searchFrom.eventTimeStart = val[0] + ' 00:00:00'
    searchFrom.eventTimeEnd = val[1] + ' 23:59:59'
  }
  getTableData()
}

function getTableData() {
  loading.value = true
  // $API
  //   .post({
  //     url: '/alarmRecord/getAlarmPageList',
  //     params: Object.assign(searchFrom, {
  //       pageSize: pageModel.pageSize,
  //       pageNo: pageModel.pageNo,
  //     }),
  //   })
  //   .then((res: any) => {
  //     tableData.value = res.data.rows || []
  //     pageModel.total = res.data.total || 0
  //   })
  //   .finally(() => {
  //     loading.value = false
  //   })

  const res = {
    code: 'success',
    data: {
      rows: [
        {
          eventSourceId: '20240702190809564015',
          fireStateStr: '已处置',
          operationState: '1',
          misinfoType: '',
          operationStateStr: '已处置',
          floorId: '310100DW1630462420088520704_002_U001',
          reachState: 0,
          eventDesc: '火点报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [
            {
              invalidType: '消防演练',
              disposeTime: '2024-07-03 19:08:01',
              createTime: '2024-07-03T19:08:01.838',
              disposeNodeCode: '2',
              disposeResult: '3',
              disposeUserName: '王静',
              detailId: '1808457666467893249',
              remark: '消防演练',
              failReason: '2',
              disposeResultStr: '忽略',
              disposeId: '1808447248806912001',
            },
          ],
          serviceModelCode: 3,
          resetTime: '2024-07-03 19:06:45',
          regularDesc: '未在规定时间内完成核警处置',
          ignoreStateStr: '其他',
          eventId: '92b8be31d8b222cb87ea8850f1cd9873',
          isRegular: '不规范',
          tocNoticeState: '0',
          disposeDesc: '',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '1',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          laMake: '',
          purpose: 601,
          twoCode: '',
          deviceTime: '2024-07-03 18:25:19',
          fireDisposeTime: '2024-07-03 19:08:01',
          deviceNum: '202302231724',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          ignoreReason: '',
          eventTime: '2024-07-03 18:25:19',
          eventChangeState: 1,
          deviceOnlyId: '6b57d815cba2d71860c5d58ebc731c2d',
          unitId: '310100DW1630462420088520704',
          opNoticeStateStr: '超时未告警',
          laPoint: '',
          resetStateStr: '已复位',
          fireResultStr: '待核警',
          operationResult: '3',
          disposeState: 1,
          operationResultStr: '忽略',
          subCenterCode: '340100YYZX201805230001',
          resetState: 1,
          channelNum: '',
          disposeFinishTime: '2024-07-03 19:08:01',
          disposeId: '1808447248806912001',
          opNoticeState: '3',
          buildingId: '310100DW1630462420088520704_002',
          disposeTime: '2024-07-03 19:08:01',
          disturbState: 1,
          lastEventTime: '2024-07-03 18:25:19',
          lastDeviceTime: '2024-07-03 18:25:19',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '2',
          disposeResult: '3',
          disposeActionStr: '值守核警',
          disposeResultStr: '忽略',
          disposeStateStr: '已处置',
          disposeTimeStr: '2024-07-03 19:08:01',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '310100DW1630462420088520704',
          nonHostInfo: '',
          floorAreaImg: '',
          deviceId: '20240702190809564015',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3597590.760644,
          unitPointX: 13555825.733914,
          longitude: '',
          cardInfo: '',
          unitName: '上海中外运海港国际物流有限公司',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '综合经济开发区邵厂镇新杨公路778号',
          floorMapType: 1,
          buildingName: '1号仓库',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: '51401A1325F042C4AD1D8BD917D0ECEE',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3597606.8204753813,
          mapX: 13555631.026442075,
          deviceAddress: '充电区监测天泽盒子',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '1层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:08:09',
          deviceTask: '',
          createTime: '2024-07-02 19:08:09',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "123456", "usage_scenarios": "601"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20240702190809564015',
          fireStateStr: '已处置',
          operationState: '1',
          misinfoType: '',
          operationStateStr: '已处置',
          floorId: '310100DW1630462420088520704_002_U001',
          reachState: 0,
          eventDesc: '火点报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [
            {
              invalidType: '消防演练',
              disposeTime: '2024-07-03 18:26:14',
              createTime: '2024-07-03T18:26:14.18',
              disposeNodeCode: '2',
              disposeResult: '3',
              disposeUserName: '王静',
              detailId: '1808447148801822721',
              remark: '消防演练',
              failReason: '2',
              disposeResultStr: '忽略',
              disposeId: '1808446570395017217',
            },
          ],
          serviceModelCode: 3,
          resetTime: '2024-07-03 18:24:57',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: 'f5174fc81ffe0b89b045e78bc4a97fc3',
          isRegular: '规范',
          tocNoticeState: '0',
          disposeDesc: '',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '1',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          laMake: '',
          purpose: 601,
          twoCode: '',
          deviceTime: '2024-07-03 18:22:38',
          fireDisposeTime: '2024-07-03 18:26:14',
          deviceNum: '202302231724',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '0',
          ignoreState: '',
          ignoreReason: '',
          eventTime: '2024-07-03 18:22:38',
          eventChangeState: 1,
          deviceOnlyId: '6b57d815cba2d71860c5d58ebc731c2d',
          unitId: '310100DW1630462420088520704',
          opNoticeStateStr: '忽略',
          laPoint: '',
          resetStateStr: '已复位',
          fireResultStr: '待核警',
          operationResult: '3',
          disposeState: 1,
          operationResultStr: '忽略',
          subCenterCode: '340100YYZX201805230001',
          resetState: 1,
          channelNum: '',
          disposeFinishTime: '2024-07-03 18:26:14',
          disposeId: '1808446570395017217',
          opNoticeState: '5',
          buildingId: '310100DW1630462420088520704_002',
          disposeTime: '2024-07-03 18:26:14',
          disturbState: 1,
          lastEventTime: '2024-07-03 18:22:38',
          lastDeviceTime: '2024-07-03 18:22:38',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '2',
          disposeResult: '3',
          disposeActionStr: '值守核警',
          disposeResultStr: '忽略',
          disposeStateStr: '已处置',
          disposeTimeStr: '2024-07-03 18:26:14',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '310100DW1630462420088520704',
          nonHostInfo: '',
          floorAreaImg: '',
          deviceId: '20240702190809564015',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3597590.760644,
          unitPointX: 13555825.733914,
          longitude: '',
          cardInfo: '',
          unitName: '上海中外运海港国际物流有限公司',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '综合经济开发区邵厂镇新杨公路778号',
          floorMapType: 1,
          buildingName: '1号仓库',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: '51401A1325F042C4AD1D8BD917D0ECEE',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3597606.8204753813,
          mapX: 13555631.026442075,
          deviceAddress: '充电区监测天泽盒子',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '1层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:08:09',
          deviceTask: '',
          createTime: '2024-07-02 19:08:09',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "123456", "usage_scenarios": "601"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20240702190809564015',
          fireStateStr: '已处置',
          disposeUserPhone: '17612341234',
          operationState: '1',
          misinfoType: '',
          detailId: '1808446388353536001',
          operationStateStr: '已处置',
          floorId: '310100DW1630462420088520704_002_U001',
          reachState: 0,
          eventDesc: '火点报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [
            {
              bluetoothState: '未触发',
              disposeUserPhone: '17612341234',
              disposeNodeCode: '3',
              disposeDesc: '',
              misinfoType: '粉尘',
              disposeUserName: '值班人员',
              detailId: '1808446388353536001',
              fireDisposeVideoAttchList: [],
              fireDisposeAttchList: [
                '/img1/dispose/310100DW1630462420088520704/dispose/2024-07-03/ebb542761cfc44278c3f9a6eca26418a.jpg',
              ],
              disposeTime: '2024-07-03 18:23:12',
              ignoreState: '3',
              createTime: '2024-07-03T18:23:12',
              disposeResult: '误报',
            },
            {
              invalidType: '消防演练',
              disposeTime: '2024-07-03 18:26:14',
              createTime: '2024-07-03T18:26:14.948',
              disposeNodeCode: '2',
              disposeResult: '3',
              disposeUserName: '王静',
              detailId: '1808447152090157057',
              remark: '消防演练',
              failReason: '2',
              disposeResultStr: '忽略',
              disposeId: '1808444929902059521',
            },
          ],
          serviceModelCode: 3,
          resetTime: '2024-07-03 18:22:00',
          regularDesc: '未在规定时间内完成核警处置',
          ignoreStateStr: '粉尘',
          eventId: '04249cb42349affe519f18dabd2cd5d8',
          isRegular: '不规范',
          tocNoticeState: '0',
          disposeDesc: '',
          eventSourceType: 0,
          fireDisposeVideoAttchList: [],
          eventType: 1,
          fireDisposeAttchList: [
            '/img1/dispose/310100DW1630462420088520704/dispose/2024-07-03/ebb542761cfc44278c3f9a6eca26418a.jpg',
          ],
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '1',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          bluetoothState: '未触发',
          laMake: '',
          purpose: 601,
          twoCode: '',
          deviceTime: '2024-07-03 18:16:07',
          fireDisposeTime: '2024-07-03 18:23:12',
          deviceNum: '202302231724',
          unitType: 0,
          fireResult: '2',
          disposeTimeout: '1',
          ignoreState: '3',
          ignoreReason: '',
          eventTime: '2024-07-03 18:16:07',
          disposeResult: '2',
          eventChangeState: 1,
          deviceOnlyId: '6b57d815cba2d71860c5d58ebc731c2d',
          unitId: '310100DW1630462420088520704',
          opNoticeStateStr: '超时未告警',
          laPoint: '',
          resetStateStr: '已复位',
          fireResultStr: '误报',
          operationResult: '3',
          disposeState: 1,
          operationResultStr: '忽略',
          disposeNodeCode: '3',
          subCenterCode: '340100YYZX201805230001',
          resetState: 1,
          disposeUserName: '值班人员',
          channelNum: '',
          disposeFinishTime: '2024-07-03 18:26:14',
          disposeId: '1808444929902059521',
          opNoticeState: '3',
          buildingId: '310100DW1630462420088520704_002',
          disposeTime: '2024-07-03 18:23:12',
          createTime: '2024-07-02 19:08:09',
          disturbState: 1,
          lastEventTime: '2024-07-03 18:16:51',
          lastDeviceTime: '2024-07-03 18:16:51',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '1',
          disposeActionStr: '现场核警',
          disposeResultStr: '误报',
          disposeStateStr: '已处置',
          disposeTimeStr: '2024-07-03 18:23:12',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '310100DW1630462420088520704',
          nonHostInfo: '',
          floorAreaImg: '',
          deviceId: '20240702190809564015',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3597590.760644,
          unitPointX: 13555825.733914,
          longitude: '',
          cardInfo: '',
          unitName: '上海中外运海港国际物流有限公司',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '综合经济开发区邵厂镇新杨公路778号',
          floorMapType: 1,
          buildingName: '1号仓库',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: '51401A1325F042C4AD1D8BD917D0ECEE',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3597606.8204753813,
          mapX: 13555631.026442075,
          deviceAddress: '充电区监测天泽盒子',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '1层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:08:09',
          deviceTask: '',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "123456", "usage_scenarios": "601"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20240702190809564015',
          fireStateStr: '已处置',
          disposeUserPhone: '17612341234',
          operationState: '1',
          misinfoType: '',
          detailId: '1808444791598780417',
          operationStateStr: '已处置',
          floorId: '310100DW1630462420088520704_002_U001',
          reachState: 0,
          eventDesc: '烟火报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [
            {
              disposeTime: '2024-07-03 18:16:51',
              ignoreState: '0',
              disposeUserPhone: '17612341234',
              createTime: '2024-07-03T18:16:51',
              disposeNodeCode: '3',
              disposeDesc: '',
              ignoreReason: '维保检修',
              disposeResult: '忽略',
              disposeUserName: '值班人员',
              detailId: '1808444791598780417',
              fireDisposeVideoAttchList: [],
              fireDisposeAttchList: [
                '/img1/dispose/310100DW1630462420088520704/dispose/2024-07-03/c48a719453b14f2a88e45f94a5546e7b.jpg',
              ],
            },
            {
              invalidType: '消防演练',
              disposeTime: '2024-07-03 18:26:14',
              createTime: '2024-07-03T18:26:15.356',
              disposeNodeCode: '2',
              disposeResult: '3',
              disposeUserName: '王静',
              detailId: '1808447153797238786',
              remark: '消防演练',
              failReason: '2',
              disposeResultStr: '忽略',
              disposeId: '1808097262726488066',
            },
          ],
          serviceModelCode: 3,
          resetTime: '2024-07-03 18:15:37',
          regularDesc: '',
          ignoreStateStr: '维保检修',
          eventId: '92bbb3dc53190a2634a03cd90e268ab1',
          isRegular: '规范',
          tocNoticeState: '0',
          disposeDesc: '',
          eventSourceType: 0,
          fireDisposeVideoAttchList: [],
          eventType: 1,
          fireDisposeAttchList: [
            '/img1/dispose/310100DW1630462420088520704/dispose/2024-07-03/c48a719453b14f2a88e45f94a5546e7b.jpg',
          ],
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '1',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          laMake: '',
          purpose: 601,
          twoCode: '',
          deviceTime: '2024-07-02 19:14:37',
          fireDisposeTime: '2024-07-03 18:16:51',
          deviceNum: '202302231724',
          unitType: 0,
          fireResult: '3',
          disposeTimeout: '1',
          ignoreState: '0',
          ignoreReason: '',
          eventTime: '2024-07-02 19:14:37',
          disposeResult: '3',
          eventChangeState: 1,
          deviceOnlyId: '6b57d815cba2d71860c5d58ebc731c2d',
          unitId: '310100DW1630462420088520704',
          opNoticeStateStr: '超时未告警',
          laPoint: '',
          resetStateStr: '已复位',
          fireResultStr: '忽略',
          operationResult: '3',
          disposeState: 1,
          operationResultStr: '忽略',
          disposeNodeCode: '3',
          subCenterCode: '340100YYZX201805230001',
          resetState: 1,
          disposeUserName: '值班人员',
          channelNum: '',
          disposeFinishTime: '2024-07-03 18:26:14',
          disposeId: '1808097262726488066',
          opNoticeState: '3',
          buildingId: '310100DW1630462420088520704_002',
          disposeTime: '2024-07-03 18:16:51',
          createTime: '2024-07-02 19:08:09',
          disturbState: 1,
          lastEventTime: '2024-07-03 18:15:07',
          lastDeviceTime: '2024-07-03 18:15:07',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '1',
          disposeActionStr: '现场核警',
          disposeResultStr: '忽略',
          disposeStateStr: '已处置',
          disposeTimeStr: '2024-07-03 18:16:51',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '310100DW1630462420088520704',
          nonHostInfo: '',
          floorAreaImg: '',
          deviceId: '20240702190809564015',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3597590.760644,
          unitPointX: 13555825.733914,
          longitude: '',
          cardInfo: '',
          unitName: '上海中外运海港国际物流有限公司',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '综合经济开发区邵厂镇新杨公路778号',
          floorMapType: 1,
          buildingName: '1号仓库',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: '51401A1325F042C4AD1D8BD917D0ECEE',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3597606.8204753813,
          mapX: 13555631.026442075,
          deviceAddress: '充电区监测天泽盒子',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '1层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:08:09',
          deviceTask: '',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "123456", "usage_scenarios": "601"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20240702154711856111',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: '110100DW1578672315292450816_002_U001',
          reachState: 0,
          eventDesc: '烟火报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 2,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: '8cec5a768e269e5e4f4a55f1b14b2ba0',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '0',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          laMake: '',
          purpose: '',
          twoCode: '',
          deviceTime: '2024-07-02 19:33:03',
          fireDisposeTime: '',
          deviceNum: '86202303161501',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          eventTime: '2024-07-02 19:33:03',
          eventChangeState: 0,
          deviceOnlyId: 'b67377cfeb05221f110ed0389c10202b',
          unitId: '110100DW1578672315292450816',
          opNoticeStateStr: '超时未告警',
          laPoint: '',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '',
          disposeFinishTime: '',
          disposeId: '1808101904038436865',
          opNoticeState: '3',
          buildingId: '110100DW1578672315292450816_002',
          disposeTime: '',
          disturbState: 1,
          lastEventTime: '2024-07-02 19:33:03',
          lastDeviceTime: '2024-07-02 19:33:03',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          serviceModelName: '图纸模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '110100DW1578672315292450816',
          nonHostInfo: '',
          floorAreaImg: 'install/110100DW1578672315292450816/002/U001.jpg',
          deviceId: '20240702154711856111',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3722671.981402,
          unitPointX: 13059708.184092,
          longitude: 513222.2222222223,
          cardInfo: '',
          unitName: '搜狗科技',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '安徽省合肥市瑶海区明光路1号',
          floorMapType: 0,
          buildingName: '室外',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: 'D26E7D695FAE4175B830BE46E8B56473',
          status: 0,
          videoLatitude: '',
          latitude: 105541.6666666667,
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: '',
          mapX: '',
          deviceAddress: '场景为空',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '室外',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:33:50',
          deviceTask: '',
          createTime: '2024-07-02 15:47:11',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "123456788888888"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20240702191801396105',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: '110100DW1578672315292450816_003_U004',
          reachState: 0,
          eventDesc: '烟火报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 2,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: 'aaccdc6deed33c3c26396dec9144f5e8',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '0',
          deviceTypePid: '25000000',
          deviceTypeId: '25030000',
          laMake: '',
          purpose: 601,
          twoCode: '',
          deviceTime: '2024-07-02 19:18:18',
          fireDisposeTime: '',
          deviceNum: '86202303161501',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          eventTime: '2024-07-02 19:18:18',
          eventChangeState: 0,
          deviceOnlyId: 'b67377cfeb05221f110ed0389c10202b',
          unitId: '110100DW1578672315292450816',
          opNoticeStateStr: '超时未告警',
          laPoint: '',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '',
          disposeFinishTime: '',
          disposeId: '1808098189218881537',
          opNoticeState: '3',
          buildingId: '110100DW1578672315292450816_003',
          disposeTime: '',
          disturbState: 1,
          lastEventTime: '2024-07-02 19:30:26',
          lastDeviceTime: '2024-07-02 19:30:26',
          laLoop: '',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          serviceModelName: '图纸模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: '110100DW1578672315292450816',
          nonHostInfo: '',
          floorAreaImg: 'install/110100DW1578672315292450816/003/U004.jpg',
          deviceId: '20240702191801396105',
          deviceName: '视频采集终端',
          produceInfo:
            '{"brand": "天泽智联", "model": "C6C", "brand_id": "1620323774641012738", "model_id": "655ad419e4b01779cb130fb1", "device_number": "", "obsolete_date": "", "validity_date": "", "manufacture_date": ""}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3722671.981402,
          unitPointX: 13059708.184092,
          longitude: 600166.6666666665,
          cardInfo: '',
          unitName: '搜狗科技',
          aerialMapType: -1,
          deviceTypeName: '视频采集终端',
          manufacturerCode: 'tanZerVideo',
          unitAddress: '安徽省合肥市瑶海区明光路1号',
          floorMapType: 0,
          buildingName: 'B栋',
          brandId: '1620323774641012738',
          deviceClassification: 6,
          projectId: 'D26E7D695FAE4175B830BE46E8B56473',
          status: 0,
          videoLatitude: '',
          latitude: 350097.2222222221,
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: '',
          mapX: '',
          deviceAddress: '搜狗盒子监测充电区',
          obsoleteDate: '',
          installInfo: '{"install_date": "2024-07-02"}',
          deviceTypePname: '其他物联网设备',
          timeInterval: 20,
          floorName: '1层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2024-07-02 19:33:29',
          deviceTask: '',
          createTime: '2024-07-02 19:18:01',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '{"video_url": "1234567777777777", "usage_scenarios": "601"}',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20230317164636261462',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: 'AHHF_QHHFY_20180408_004_U007',
          reachState: 0,
          eventDesc: '烟感报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 3,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: '81fe596250f25eaadda4ecc6ae204d38',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '2024-07-01 15:15:57',
          fireState: '0',
          deviceTypePid: '02000000',
          deviceTypeId: '02030000',
          laMake: '6',
          purpose: '',
          twoCode: '',
          deviceTime: '2024-06-29 21:35:00',
          fireDisposeTime: '',
          deviceNum: '113',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          eventTime: '2024-06-29 21:35:00',
          eventChangeState: 0,
          deviceOnlyId: '820c7abddce680eb9f52a4656b2e3708',
          unitId: 'AHHF_QHHFY_20180408',
          opNoticeStateStr: '超时已告警',
          laPoint: '5',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '1',
          disposeFinishTime: '',
          disposeId: '1807045427561861122',
          opNoticeState: '4',
          buildingId: 'AHHF_QHHFY_20180408_004',
          disposeTime: '2024-07-01 15:17:28',
          disturbState: 1,
          lastEventTime: '2024-06-29 21:35:00',
          lastDeviceTime: '2024-06-29 21:35:00',
          laLoop: '2',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: 'AHHF_QHHFY_20180408',
          nonHostInfo: '',
          floorAreaImg: 'image/floorImage/AHHF_QHHFY_20180408/004/U007/7.jpg',
          deviceId: '20230317164636261462',
          deviceName: '感烟火灾探测器',
          produceInfo:
            '{"brand": "无锡蓝天", "model": "bj-model-v1", "brand_id": "1542028701717336066", "model_id": "63105f3de4b0e373096352aa"}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3710320.81,
          unitPointX: 13055357.529999,
          longitude: '',
          cardInfo: '',
          unitName: '清华大学合肥公共安全研究院',
          aerialMapType: -1,
          deviceTypeName: '点型感烟火灾探测器',
          manufacturerCode: 'tanZer',
          unitAddress: '经开区习友路5999号',
          floorMapType: 1,
          buildingName: '综合科研大楼（4号楼）',
          brandId: '1542028701717336066',
          deviceClassification: 1,
          projectId: 'B1386886E9E246C5B24714F4F58EA91C',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3710412.833059439,
          mapX: 13055341.771535493,
          deviceAddress: '活动室123',
          obsoleteDate: '',
          installInfo: '{"install_date": "2023-03-17"}',
          deviceTypePname: '火灾自动报警系统',
          timeInterval: 0,
          floorName: '7层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2023-03-17 16:47:54',
          deviceTask: '',
          createTime: '2023-03-17 16:46:36',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20230512145527615095',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: 'AHHF_QHHFY_20180408_004_U002',
          reachState: 0,
          eventDesc: '火警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 3,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: '092b680b617982f075f017e5aaa435ea',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '2024-07-01 16:31:17',
          fireState: '0',
          deviceTypePid: '02000000',
          deviceTypeId: '02030000',
          laMake: '312',
          purpose: '',
          twoCode: '',
          deviceTime: '2024-04-29 09:58:16',
          fireDisposeTime: '',
          deviceNum: '10002',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '0',
          ignoreState: '',
          eventTime: '2024-04-29 09:58:16',
          eventChangeState: 1,
          deviceOnlyId: '201af25de044d26881345a9486cb6f58',
          unitId: 'AHHF_QHHFY_20180408',
          opNoticeStateStr: '超时未告警',
          laPoint: '1',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '',
          disposeFinishTime: '2024-04-30 16:20:03',
          disposeId: '1784771589468495874',
          opNoticeState: '3',
          buildingId: 'AHHF_QHHFY_20180408_004',
          disposeTime: '2024-07-01 16:32:52',
          disturbState: 1,
          lastEventTime: '2024-06-29 21:34:33',
          lastDeviceTime: '2024-06-29 21:34:33',
          laLoop: '1',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          serviceModelName: '全托管模式',
          ownerType: 0,
          installImg: '',
          standardInfo: '',
          ownerId: 'AHHF_QHHFY_20180408',
          nonHostInfo: '',
          floorAreaImg: 'image/floorImage/AHHF_QHHFY_20180408/004/U002/2.jpg',
          deviceId: '20230512145527615095',
          deviceName: '点型感烟火灾探测器',
          produceInfo:
            '{"brand": "海湾", "model": "hw", "brand_id": "1539438759251873793", "model_id": "62b281cde4b00973c33782b3"}',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3710320.81,
          unitPointX: 13055357.529999,
          longitude: '',
          cardInfo: '',
          unitName: '清华大学合肥公共安全研究院',
          aerialMapType: -1,
          deviceTypeName: '点型感烟火灾探测器',
          manufacturerCode: 'bwwxlt',
          unitAddress: '经开区习友路5999号',
          floorMapType: 1,
          buildingName: '综合科研大楼（4号楼）',
          brandId: '1539438759251873793',
          deviceClassification: 1,
          projectId: 'B1386886E9E246C5B24714F4F58EA91C',
          status: 0,
          videoLatitude: '',
          latitude: '',
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: 3710404.8833219986,
          mapX: 13055325.518325489,
          deviceAddress: '数据决策测试点位1-1',
          obsoleteDate: '',
          installInfo: '{"install_date": "2023-05-12"}',
          deviceTypePname: '火灾自动报警系统',
          timeInterval: 0,
          floorName: '2层',
          subordinateUnits: '',
          videoLongitude: '',
          updateTime: '2023-05-12 14:55:27',
          deviceTask: '',
          createTime: '2023-05-12 14:55:27',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20230223173201683982',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: '110100DW1578672315292450816_001_U002',
          reachState: 0,
          eventDesc: '手动报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 2,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: '508731a911e545ef1f64d37dc25a9feb',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '0',
          deviceTypePid: '02000000',
          deviceTypeId: '02030000',
          laMake: '1',
          purpose: '',
          twoCode: '',
          deviceTime: '2024-06-27 15:59:06',
          fireDisposeTime: '',
          deviceNum: '800036',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          eventTime: '2024-06-27 15:59:06',
          eventChangeState: 0,
          deviceOnlyId: 'f826864c6659a98dd0946191eebb62cc',
          unitId: '110100DW1578672315292450816',
          opNoticeStateStr: '超时未告警',
          laPoint: '8',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '2',
          disposeFinishTime: '',
          disposeId: '1806236112590786561',
          opNoticeState: '3',
          buildingId: '110100DW1578672315292450816_001',
          disposeTime: '',
          disturbState: 1,
          lastEventTime: '2024-06-27 16:56:39',
          lastDeviceTime: '2024-06-27 16:56:39',
          laLoop: '2',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          ownerType: 0,
          serviceModelName: '图纸模式',
          standardInfo: '',
          installImg: '',
          nonHostInfo: '',
          ownerId: '110100DW1578672315292450816',
          deviceId: '20230223173201683982',
          deviceName: '感烟火灾探测器',
          floorAreaImg: 'install/110100DW1578672315292450816/001/U222.jpg',
          produceInfo: '',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3722671.981402,
          unitPointX: 13059708.184092,
          longitude: 554350.1870070904,
          cardInfo: '',
          unitName: '搜狗科技',
          aerialMapType: -1,
          deviceTypeName: '点型感烟火灾探测器',
          manufacturerCode: 'bwhw',
          unitAddress: '安徽省合肥市瑶海区明光路1号',
          floorMapType: 0,
          buildingName: 'A栋',
          brandId: '',
          deviceClassification: 1,
          projectId: 'D26E7D695FAE4175B830BE46E8B56473',
          status: 0,
          videoLatitude: '',
          latitude: 581629.2566449379,
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: '',
          mapX: '',
          deviceAddress: '西机房',
          obsoleteDate: '',
          installInfo: '',
          deviceTypePname: '火灾自动报警系统',
          timeInterval: 0,
          floorName: '二层',
          subordinateUnits: 0,
          videoLongitude: '',
          updateTime: '2024-04-18 15:51:56',
          deviceTask: '',
          createTime: '2023-02-23 17:32:01',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '',
          keyPartId: '',
          priorityEventType: '1',
        },
        {
          eventSourceId: '20230223173201480093',
          fireStateStr: '待处置',
          operationState: '0',
          operationStateStr: '待处置',
          floorId: '110100DW1578672315292450816_001_U002',
          reachState: 0,
          eventDesc: '手动报警',
          opFeedbackStatus: '0',
          opFeedbackTime: '',
          options: [],
          serviceModelCode: 2,
          resetTime: '',
          regularDesc: '',
          ignoreStateStr: '其他',
          eventId: '63afc478f38724382b29c4923f7d16cb',
          isRegular: '--',
          tocNoticeState: '0',
          eventSourceType: 0,
          eventType: 1,
          tocNoticeTime: '',
          opNoticeTime: '',
          fireState: '0',
          deviceTypePid: '02000000',
          deviceTypeId: '02030000',
          laMake: '1',
          purpose: '',
          twoCode: '',
          deviceTime: '2024-06-27 15:58:58',
          fireDisposeTime: '',
          deviceNum: '800036',
          unitType: 0,
          fireResult: '0',
          disposeTimeout: '1',
          ignoreState: '',
          eventTime: '2024-06-27 15:58:58',
          eventChangeState: 0,
          deviceOnlyId: '3f932a355289be537d86b66dfbbc22f9',
          unitId: '110100DW1578672315292450816',
          opNoticeStateStr: '超时未告警',
          laPoint: '7',
          resetStateStr: '未复位',
          fireResultStr: '待核警',
          operationResult: '0',
          disposeState: 0,
          operationResultStr: '待核警',
          subCenterCode: '340100YYZX201805230001',
          resetState: 0,
          channelNum: '2',
          disposeFinishTime: '',
          disposeId: '1806236079430619137',
          opNoticeState: '3',
          buildingId: '110100DW1578672315292450816_001',
          disposeTime: '',
          disturbState: 1,
          lastEventTime: '2024-06-27 15:58:58',
          lastDeviceTime: '2024-06-27 15:58:58',
          laLoop: '2',
          unitTypeName: '企业单位',
          disposeAction: '0',
          disposeResult: '0',
          disposeActionStr: '- -',
          disposeResultStr: '- -',
          disposeStateStr: '待处置',
          disposeTimeStr: '',
          ownerType: 0,
          serviceModelName: '图纸模式',
          standardInfo: '',
          installImg: '',
          nonHostInfo: '',
          ownerId: '110100DW1578672315292450816',
          deviceId: '20230223173201480093',
          deviceName: '感烟火灾探测器',
          floorAreaImg: 'install/110100DW1578672315292450816/001/U222.jpg',
          produceInfo: '',
          isAerialMap: 1,
          monitoringInfo: '',
          unitPointY: 3722671.981402,
          unitPointX: 13059708.184092,
          longitude: 489531.5870212609,
          cardInfo: '',
          unitName: '搜狗科技',
          aerialMapType: -1,
          deviceTypeName: '点型感烟火灾探测器',
          manufacturerCode: 'bwhw',
          unitAddress: '安徽省合肥市瑶海区明光路1号',
          floorMapType: 0,
          buildingName: 'A栋',
          brandId: '',
          deviceClassification: 1,
          projectId: 'D26E7D695FAE4175B830BE46E8B56473',
          status: 0,
          videoLatitude: '',
          latitude: 581629.2566449379,
          houseNumber: '',
          subCenterName: '总部（合肥）运营中心',
          mapZ: 0,
          mapY: '',
          mapX: '',
          deviceAddress: '西机房',
          obsoleteDate: '',
          installInfo: '',
          deviceTypePname: '火灾自动报警系统',
          timeInterval: 0,
          floorName: '二层',
          subordinateUnits: 0,
          videoLongitude: '',
          updateTime: '2024-04-18 15:51:53',
          deviceTask: '',
          createTime: '2023-02-23 17:32:01',
          videoMapZ: 0,
          videoMapY: '',
          videoMapX: '',
          useInfo: '',
          keyPartId: '',
          priorityEventType: '1',
        },
      ],
      pageNo: 1,
      pageSize: 10,
      pages: 16,
      total: 157,
    },
    dataType: 'json',
    message: '火警事件-列表-通用成功',
    status: '',
    token: '',
  }
  loading.value = false
  tableData.value = res.data.rows || []
  pageModel.total = res.data.total || 0
}

function currentChange(pageNo: number) {
  console.log('🚀 ~ currentChange ~ pageNo:', pageNo)
  pageModel.pageNo = pageNo
  getTableData()
}

function handleSizeChange(pageSize: number) {
  console.log('🚀 ~ handleSizeChange ~ pageSize:', pageSize)
  pageModel.pageNo = 1
  pageModel.pageSize = pageSize
  getTableData()
}

function getTableColumns() {
  columns.value = [
    {
      title: '火警编号',
      key: 'disposeId',
      width: 116,
      showOverflowTooltip: true,
    },
    {
      title: '单位名称',
      key: 'unitName',
      showOverflowTooltip: true,
      minWidth: 120,
    },
    {
      title: '末次接收时间',
      key: 'lastEventTime',
      showOverflowTooltip: true,
    },
    {
      title: '设备类型',
      key: 'deviceTypeName',
      showOverflowTooltip: true,
    },
    {
      title: '设备位置',
      key: '_deviceAddres',
      showOverflowTooltip: true,
    },
    {
      title: '主机回路点位',
      key: 'deviceClassification',
      field: '_laLoop',
      width: '125',
      showOverflowTooltip: true,
    },
    {
      title: '处置状态',
      key: 'disposeStateStr',
      dot: true,
      field: 'disposeStateStr',
      showOverflowTooltip: true,
      width: '100',
    },
    {
      title: '处置动作',
      key: 'disposeActionStr',
      dot: true,
      field: 'disposeActionStr',
      width: '115',
      showOverflowTooltip: true,
    },
    {
      title: '处置结果',
      key: 'disposeResultStr',
      dot: true,
      field: 'disposeResultStr',
      width: '100',
      showOverflowTooltip: true,
    },
    {
      title: '是否规范',
      key: 'isRegular',
      width: '100',
      showOverflowTooltip: false,
    },

    // {
    //   title: '企业核警结果',
    //   key: 'fireResultName',
    //   field: 'fireResult',
    //   showOverflowTooltip: true
    // },
    // {
    //   title: '监测值守核警结果',
    //   key: 'operationResultStr',
    //   field: 'operationResult',
    //   showOverflowTooltip: true,
    //   minWidth: 90
    // }
  ]
}
</script>
<style lang="scss" scoped></style>
