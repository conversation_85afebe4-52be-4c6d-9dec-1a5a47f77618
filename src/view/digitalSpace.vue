<!--
 * @Author: jingjf <EMAIL>
 * @Date: 2024-07-11 19:46:38
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-15 11:32:25
 * @FilePath: \ehs-partner-mgr\src\view\digitalSpace.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="digitalSpace w-full h-full">
    <!-- 顶部 -->
    <div class="right flex items-center">
      {{ formatted }}
      <el-icon class="ml-[10px] cursor-pointer" color="#29A2DD" :size="20" @click="router.push('/implementationTasks')">
        <SwitchButton />
      </el-icon>
    </div>
  </div>
  <!-- 中部内容 -->
  <div class="digitalMiddle">
    <!-- 地球 -->
    <div class="earth">
      <div class="total">
        <span class="text">已签到人数</span>
        <span class="number"><span class="text-3.5vw">{{ getSigninNum() }}</span>/{{ allData.taskUserCount }}</span>
      </div>
      <div class="middle-show-box">
        <div class="middle-show-item">
          <div class="icon"></div>
          <div class="content">培训类型：{{ allData.trainTypeName }}</div>
        </div>
        <div class="middle-show-item2">
          <div class="icon"></div>
          <div class="content">培训形式：{{ allData.trainingWayName }}</div>
        </div>
        <div class="middle-show-item3">
          <div class="icon"></div>
          <div class="content">培训时间：{{ allData.taskTime }}</div>
        </div>
        <div class="middle-show-item4">
          <div class="icon"></div>
          <div class="content">培训时长：{{ allData.trainMinites }}分钟</div>
        </div>
      </div>
    </div>
    <!-- 二维码 -->
    <div class="qr-code">
      <div class="sign-in">
        <div class="sign-in-code">
          <qrcode-vue v-if="allData.signInUrl" :width="200" :height="200" :margin="4" :value="allData.signInUrl" />
        </div>
        <div class="sign-in-type"></div>
      </div>
      <div class="exam">
        <div class="exam-code" @click="showExamCode">
          <qrcode-vue v-if="isShowExamCode" :width="200" :height="200" :margin="5" :value="allData.examinationUrl"
            :dotsOptions="{ type: 'square', color: '#6B1A4C' }"
            :cornersSquareOptions="{ type: 'dot', color: '#171717' }"
            :cornersDotOptions="{ type: 'square', color: '#171717' }" />
          <div v-else class="exam-code-null"></div>
        </div>
        <div class="exam-type"></div>
      </div>
    </div>
  </div>
  <!-- 标题 -->
  <div class="mainTitle">{{ allData.trainTopic }}</div>
  <!-- 底部头像区域 -->
  <div class="mainAvatar" v-loading="isloading" element-loading-text="正在刷新...">
    <div v-if="getSigninNum() > 12" class="avatar-box" ref="scrollRef">
      <div class="avatar-box-item" id="scrollBox">
        <template v-for="item in signinInfo" :key="item">
          <div class="avatar">
            <el-image v-if="item.userName" style="width: 4vw; height: 4vw" :src="avatar" fit="cover" />
            <el-image v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />
            <div class="name">{{ item.userName.slice(0, 4) }}</div>
          </div>
        </template>
      </div>
      <div class="avatar-box-item mt-[16px]">
        <template v-for="item in signinInfo" :key="item">
          <div class="avatar">
            <el-image v-if="item.userName" style="width: 4vw; height: 4vw" :src="avatar" fit="cover" />
            <el-image v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />
            <div class="name">{{ item.userName.slice(0, 4) }}</div>
          </div>
        </template>
      </div>
    </div>
    <div v-else class="avatar-box-not-enough">
      <div class="avatar-box-item">
        <template v-for="item in signinInfo" :key="item">
          <div class="avatar">
            <el-image v-if="item.userName" style="width: 4vw; height: 4vw" :src="avatar" fit="cover" />
            <el-image v-else style="width: 4vw; height: 4vw" :src="avatarNull" fit="cover" />
            <div class="name">{{ item.userName.slice(0, 4) }}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import avatar from '@/assets/image/digitalspace/avatar.png'
import avatarNull from '@/assets/image/digitalspace/avatar-null.png'
import $API from '~/common/api'
import { useRoute, useRouter } from 'vue-router'
import QrcodeVue from 'qrcode-vue3'
import { nextTick } from 'vue'
import { useDateFormat, useNow } from '@vueuse/core'
import { SwitchButton } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const isloading = ref(false)
const isShowExamCode = ref(false)
const scrollRef = ref()
const formatted = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm:ss')
// const router = useRouter()
const allData = ref({
  taskTime: '',
  trainType: '', //培训类型
  trainTypeName: '', // 培训类型名称
  allStaffNum: 0,
  examPaperName: '',
  trainMinites: '', // 培训分钟
  trainTopic: '', // 培训主题
  trainingWay: '', // 培训形式
  trainingWayName: '', // 培训形式名称examinationUrl
  examinationUrl: '', // 考试地址url
  signInUrl: '', // 签到地址url
  taskUserCount: 0, // 培训人数
})
const avatarBoxHeight = ref('-530px')
const signinInfo = ref<
  {
    userName: string
  }[]
>([])
//
const examId = ref('')
const getDate = () => {
  $API.get({ url: `/api/task/getTaskById?id=${examId.value}` }).then((res: any) => {
    console.log('res -----> 🚀', res)
    if (res && res.code === 'success') {
      allData.value = res.data
      console.log('allData.value -----> 🚀', allData.value)
    }
  })
}

const getSignDate = () => {
  isloading.value = true
  $API
    .get({ url: `/api/task/user/getSignInStaffByTaskId?taskId=${examId.value}` })
    .then((res: any) => {
      console.log('res111 -----> 🚀', res)
      if (res && res.code === 'success') {
        if (res.data.length < 12) {
          signinInfo.value = res.data
          const num = 12 - res.data.length
          for (let i = 0; i < num; i++) {
            signinInfo.value.push({
              userName: '',
            })
          }
          console.log('signinInfo.value -----> 🚀', signinInfo.value)
        } else {
          signinInfo.value = res.data
        }
        nextTick(() => {
          if (scrollRef.value) {
            // scrollRef.value.style.animation = 'move 10s 0s linear infinite'
          }
          const box = document.getElementById('scrollBox')
          if (box) {
            avatarBoxHeight.value = `-${box!.offsetHeight}px`
            console.log('box!.offsetHeight -----> 🚀', box!.offsetHeight)
          }
        })
      }
    })
    .finally(() => {
      isloading.value = false
    })
}
// 定时刷新签到人数
const timer = setInterval(() => {
  getSignDate()
  // 如果签到人数达到总人数，则停止定时器
  if (getSigninNum() === allData.value.taskUserCount) {
    clearInterval(timer)
  }
}, 3000)

const showExamCode = () => {
  isShowExamCode.value = !isShowExamCode.value
}

const getSigninNum = () => {
  return signinInfo.value.filter((item) => item.userName).length
}

onMounted(async () => {
  examId.value = route.query.id as string
  getDate()
  await getSignDate()
})
onUnmounted(() => {
  clearInterval(timer) // 退出页面关闭轮询
})
</script>

<style lang="scss" scoped>
.digitalSpace {
  position: relative;
  background: url('../assets/image/digitalspace/digital.png') no-repeat center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;

  .digitalTop {
    height: 77px;
    width: 100%;
    background: url('../assets/image/digitalspace/top.png') no-repeat center/cover;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;

    .left,
    .right {
      color: #fff;
      font-size: 14px;
      height: 55px;
      line-height: 55px;
    }
  }

  .digitalMiddle {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1;

    .earth {
      position: absolute;
      top: 5vh;
      left: 50px;
      width: 36vw;
      height: 33vw;
      background: url('../assets/image/digitalspace/earth.png') no-repeat center;
      background-size: 100% 100%;

      .total {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 21.8vw;
        height: 10vw;
        border-top: 1px solid;
        border-bottom: 1px solid;
        border-image: linear-gradient(to right, transparent 0%, #35afff 10%, #35afff 90%, transparent) 1;
        /* 边框渐变 */
        background: linear-gradient(to right,
            transparent 0%,
            rgba(52, 172, 252, 0.2) 10%,
            rgba(52, 172, 252, 0.2) 90%,
            transparent 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .text {
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 3vw;
          color: #ffffff;
        }

        .number {
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 2.5vw;
          color: #ffffff;
        }
      }

      .middle-show-box {
        position: absolute;
        top: 57%;
        right: -58%;
        width: 25vw;
        height: 21vw;
        transform: translateY(-50%);
        background: url('../assets/image/digitalspace/content.png') no-repeat center;
        background-size: 100% 100%;

        .middle-show-item,
        .middle-show-item2,
        .middle-show-item3,
        .middle-show-item4 {
          position: absolute;
          top: 0;
          left: 6vw;
          width: 40vw;
          height: 5vw;
          display: flex;
          align-items: center;

          .icon {
            margin-right: 20px;
            width: 3vw;
            height: 3vw;
            background: url('../assets/image/digitalspace/type.png') no-repeat center/cover;
          }

          .content {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 1.2vw;
            color: #ffffff;
            line-height: 133px;
            text-shadow: 0px 3px 4px rgba(166, 250, 255, 0.69);
          }
        }

        .middle-show-item2 {
          top: 24%;
          left: 7vw;

          .icon {
            background: url('../assets/image/digitalspace/style.png') no-repeat center/cover;
          }
        }

        .middle-show-item3 {
          top: 50%;
          left: 7vw;

          .icon {
            background: url('../assets/image/digitalspace/time.png') no-repeat center/cover;
          }
        }

        .middle-show-item4 {
          top: 75%;
          left: 5vw;

          .icon {
            background: url('../assets/image/digitalspace/duration.png') no-repeat center/cover;
          }
        }
      }
    }

    .qr-code {
      position: absolute;
      top: 0;
      right: 20px;
      height: 100%;
      width: 20vw;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;

      .sign-in,
      .exam {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 23vw;

        .sign-in-code,
        .exam-code {
          width: 10vw;
          height: 10vw;
          background-color: #fff;

          .exam-code-null {
            width: 10vw;
            height: 10vw;
            background: url('../assets/image/digitalspace/mohu.png') no-repeat center;
            background-size: 95% 95%;
            position: relative;

            &::before {
              content: '展示';
              width: 10vw;
              // height: 10%;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background-color: rgba(0, 0, 0, 0.5);
              color: #fff;
              text-align: center;
              padding: 10px;
            }
          }
        }

        .sign-in-type,
        .exam-type {
          width: 100%;
          height: 7vw;
          background: url('../assets/image/digitalspace/signInCode.png') no-repeat center/cover;
        }

        .exam-type {
          background: url('../assets/image/digitalspace/examCode.png') no-repeat center/cover;
        }
      }
    }
  }

  .mainTitle {
    position: absolute;
    top: 7vw;
    left: 60%;
    transform: translate(-50%);
    width: 45vw;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 3vw;
    color: #ffffff;
    text-shadow: 0px 3px 4px rgba(166, 250, 255, 0.69);
    // 不换行，且超出使用省略号
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .mainAvatar {
    padding: 10px;
    width: 40vw;
    height: 20vh;
    position: absolute;
    left: 58%;
    bottom: 2%;
    transform: translate(-50%);
    background: linear-gradient(to right,
        transparent 0%,
        rgba(52, 172, 252, 0.2) 10%,
        rgba(52, 172, 252, 0.2) 90%,
        transparent 100%);

    border-top: 1px solid;
    border-image: linear-gradient(to right, transparent 0%, #35afff 10%, #35afff 90%, transparent) 1;
    /* 边框渐变 */
    border-bottom: 1px solid;
    overflow: hidden;

    .avatar-box,
    .avatar-box-not-enough {
      width: 100%;
      animation: move 10s 0s linear infinite;

      .avatar-box-item {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        justify-items: center;
        row-gap: 16px;

        .avatar {
          width: 4vw;
          height: 4vw;
          // background: fixed url('../assets/image/digitalspace/avatar.png') no-repeat center/cover;
          color: #fff;
          position: relative;

          img {
            width: 4vw;
            height: 4vw;
          }

          .name {
            position: absolute;
            bottom: 1px;
            width: 100%;
            font-size: 0.65vw;
            text-align: center;
          }
        }
      }
    }

    .avatar-box-not-enough {
      animation: none;
    }
  }

  // 动画
  @keyframes move {
    from {
      transform: translate(0, 0);
    }

    to {
      transform: translate(0, v-bind(avatarBoxHeight));
    }
  }
}

.v-mini-weather-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.v-weather {
  display: flex;
  align-items: center;
}

/*修改图标样式*/
.v-mini-weather-icon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

/*修改文本样式*/
.weather-text {
  color: #000;
}
</style>
