<template>
  <div class="workflow">
    <el-scrollbar>
      <div class="content-box">
        <div class="top h-[150px] pr-[20px]">
          {{
            ui.zhLogo === 'yanchang'
              ? '承包商全流程管理，建立四道安全防线，打造承包商高质量人才队伍'
              : '相关方全流程管理，建立四道安全防线，打造相关方高质量人才队伍'
          }}
        </div>
        <div class="process">
          <div class="p-title">流程管理</div>
          <div class="p-line flex">
            <div class="p-text relative">
              <div class="p-text-1 items-start">
                <div class="flex items-center mr-[80px] mt-[20px]">
                  <div class="text-right mr-[10px]">
                    <div class="p-first mb-[5px]">第一道安全防线</div>
                    <div class="p-bottom">资质培训，合规入库</div>
                  </div>
                  <div class="p-text-img"></div>
                </div>
                <div class="p-text-2 flex">
                  <div v-for="(item, index) in flowList1" :key="index" class="p-text-3 w-[178px]">
                    <div class="p-text-img2" :style="{ background: `url(${item.url}) no-repeat` }"></div>
                    <div class="p-text-name">{{ item.name }}</div>
                  </div>
                </div>
              </div>
              <div class="line"></div>
            </div>
            <div class="p-text relative">
              <div class="p-text-1 items-start">
                <div class="flex items-center mr-[80px] mt-[20px]">
                  <div class="text-right mr-[10px]">
                    <div class="p-first mb-[5px]">第二道安全防线</div>
                    <div class="p-bottom">严控作业票入场审批</div>
                  </div>
                  <div class="p-text-img"></div>
                </div>
                <div class="p-text-2 flex">
                  <div v-for="(item, index) in flowList2" :key="index" class="p-text-3">
                    <div class="p-text-img2" :style="{ background: `url(${item.url}) no-repeat` }"></div>
                    <div class="p-text-name">{{ item.name }}</div>
                  </div>
                </div>
              </div>
              <div class="line"></div>
            </div>
            <div class="p-text relative">
              <div class="p-text-1 items-start">
                <div class="flex items-center mr-[80px] mt-[20px] ml-[20px]">
                  <div class="text-right mr-[10px]">
                    <div class="p-first mb-[5px]">第三道安全防线</div>
                    <div class="p-bottom">作业过程智能监控</div>
                  </div>
                  <div class="p-text-img"></div>
                </div>
                <div class="p-text-2 flex">
                  <div v-for="(item, index) in flowList3" :key="index" class="p-text-3">
                    <div class="p-text-img2" :style="{ background: `url(${item.url}) no-repeat` }"></div>
                    <div class="p-text-name">{{ item.name }}</div>
                  </div>
                </div>
              </div>
              <div class="line"></div>
            </div>
            <div class="p-text relative">
              <div class="p-text-1 items-start">
                <div class="flex items-center mr-[80px] mt-[20px] ml-[-35px]">
                  <div class="text-right mr-[10px]">
                    <div class="p-first mb-[5px]">第四道安全防线</div>
                    <div class="p-bottom">人企量化评价，优胜劣汰</div>
                  </div>
                  <div class="p-text-img"></div>
                </div>
                <div class="p-text-2 flex">
                  <div v-for="(item, index) in flowList4" :key="index" class="p-text-3">
                    <div class="p-text-img2" :style="{ background: `url(${item.url}) no-repeat` }"></div>
                    <div class="p-text-name">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import one from '@/assets/img/flow-1.png'
import two from '@/assets/img/flow-2.png'
import three from '@/assets/img/flow-3.png'
import four from '@/assets/img/flow-4.png'
import five from '@/assets/img/flow-5.png'
import six from '@/assets/img/flow-6.png'
import seven from '@/assets/img/flow-7.png'
import eight from '@/assets/img/flow-8.png'
import nine from '@/assets/img/flow-9.png'
import { useWindowSize } from '@vueuse/core'
import { useUserInfo } from '~/store'
const ui = useUserInfo()
const windowSize = useWindowSize()
const flowList1 = [
  { name: ui.value.zhLogo === 'yanchang' ? '承包商自主合规录入信息' : '相关方自主合规录入信息', url: one },
  { name: '必学培训课程', url: two },
  { name: '在线学习，通过才能作业', url: three }
]
const flowList2 = [
  { name: '严控作业票入场审批', url: four },
  { name: '作业票审批，系统即时通知', url: five }
]
const flowList3 = [
  { name: '视频智能监管，及时预警', url: six },
  { name: '隐患排查，违规上报', url: seven }
]
const flowList4 = [
  { name: '数据汇聚，人企安全履历', url: eight },
  { name: '人企量化评估模型，决策依据', url: nine }
]
</script>

<style scoped lang="scss">
.workflow {
  @apply w-full h-full;
}
.content-box {
  min-height: 100%;
  padding: 0 20px 20px;

  .top {
    background: url('@/assets/image/top-bg.png') no-repeat;
    background-size: 100% 100%;
    font-size: 30px;
    color: #ffffff;
    line-height: 150px;
    padding-left: 3.0625rem;
    margin-bottom: 30px;
  }

  .process {
    //margin-bottom: 20px;
    background: #eef7ff;
    box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.05);
    border: 1px solid #ffffff;
    border-radius: 9px;

    .p-title {
      font-size: 16px;
      color: #222222;
      height: 3.25rem;
      line-height: 3.25rem;
      background: #dce4f4;
      border-radius: 9px 9px 0px 0px;
      padding-left: 1.25rem;
    }

    .p-line {
      display: flex;
      flex-direction: column;
      padding-left: 100px;
    }

    .p-text {
      .line {
        position: absolute;
        top: 68px;
        left: 200px;
        width: 1px;
        height: 100px;
        border: 1px solid #5b83ff;
      }

      .p-box {
        width: 220px;
      }

      .p-text-1 {
        display: flex;
        justify-content: flex-start;

        .p-text-img {
          width: 50px;
          height: 50px;
          background: url('@/assets/img/timeline.png') no-repeat;
          background-size: 100% 100%;
        }

        .p-first {
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }

        .p-bottom {
          font-size: 18px;
          color: #333333;
          line-height: 25px;
          font-weight: 700;
        }
      }

      .p-text-2 {
        .p-text-3 {
          // margin-right: 120px;
          min-width: 200px;
          text-align: center;

          .p-text-img2 {
            margin: 0 auto;
            width: 68px;
            height: 68px;
            background-size: 100% 100% !important;
          }

          .p-text-name {
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #212121;
          }
        }
      }
    }
  }
}
.ifm-child {
  .workflow {
    .content-box {
      padding: 0 !important;
    }
  }
}
</style>
