<template>
  <div class="projectMgr-left" :style="boxStyle">
    <TrainTree :collapsed="collapsed" @serach="clickNodeHandle" />
    <div :class="[collapsed ? 'right-[-20px]' : 'right-0', 'collapsBtn']" @click="collapseHandle"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import TrainTree from '@/components/tree/index.vue'
import { treeSerivce } from './treeService'

defineOptions({ name: 'ProjectMgrLeft' })

const collapsed = ref(true)
const boxStyle = computed(() => ({
  width: collapsed.value ? '323px' : '0px',
  padding: collapsed.value ? '20px' : '0px',
  marginRight: collapsed.value ? '20px' : '0px'
}))

const collapseHandle = () => {
  collapsed.value = !collapsed.value
}

// const emits = defineEmits(['nodeClick'])
const clickNodeHandle = (data: any) => {
  treeSerivce.curNode.value = data
  // emits('nodeClick', data)
}
</script>

<style scoped lang="scss">
.projectMgr-left {
  @apply relative bg-[#eef7ff] h-full;

  .collapsBtn {
    @apply absolute w-[34px] h-[36px] cursor-pointer;
    background: url('@/assets/expand.png') no-repeat 0 0;
    background-size: 100% 100%;
    top: 50%;
    transform: translate(50%, -50%);
  }
}
</style>
