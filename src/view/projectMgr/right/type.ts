export interface ITableRow {
  constructScheme: string
  emergencyPlan: string
  endDate: string
  orgCode: string
  orgName: string
  projectDesc: string
  projectId: string
  projectName: string
  projectNo: string
  projectStatus: string
  projectStatusName: string
  responsibleUserName: string
  securityProtocol: string
  startDate: string
  xmlxMc: string
  [prop: string]: any
}

export interface IFormData {
  projectName: string
}
