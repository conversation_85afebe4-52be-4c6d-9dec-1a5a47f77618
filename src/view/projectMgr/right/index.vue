<template>
  <div class="projectMgr-right">
    <DashBar ref="dashBarRef" />
    <Filter @action="actionHandle" />
    <div class="w-full flex-1 h-0">
      <ListComp ref="listRef" @action="actionHandle" />
    </div>

    <AsideComp ref="asideRef" @action="actionHandle" />
  </div>
</template>

<script setup lang="ts">
import { provide, Ref, ref } from 'vue'
import DashBar from './comp/dashBar.vue'
import Filter from './comp/filter.vue'
// import TableComp from './comp/table/index.vue'
import ListComp from './comp/list/index.vue'
import AsideComp from './comp/aside/index.vue'
import { ACTION } from '@/view/projectMgr/constant'
import { IActionData, PROVIDE_KEY } from '../constant'

defineOptions({ name: 'projectMgrRight' })

const dashBarRef = ref()
const listRef = ref()
const asideRef = ref()

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} })
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction)

const actionHandle = (payload: any) => {
  const { action, data } = payload

  if (action === ACTION.SEARCH) {
    listRef.value.resetTableData(data)
  }

  if (
    [
      ACTION.ADD,
      ACTION.EDIT,
      ACTION.DETAIL,
      ACTION.PEOPLEMGR,
      ACTION.ENTRYTRAININGTRACK,
      ACTION.ENTRYTRAININGSET,
      ACTION.RISK,
      ACTION.DANGERWORK,
      ACTION.OUT
    ].includes(action)
  ) {
    currentAction.value = payload
    console.log(payload, '===')
    asideRef.value.asideShowHandle({
      action,
      data
    })
  }

  // if (action === ACTION.DELETE) {
  //   listRef.value.deleteConfirm()
  // }

  if (action === ACTION.UPDATESUCCESS) {
    dashBarRef.value.getData()
    listRef.value.resetTableData()
  }
}
</script>

<style scoped lang="scss">
.projectMgr-right {
  @apply flex flex-col;
  // .header-filter {
  //   @apply flex flex-row flex-nowrap justify-between;
  // }
  // .header-action {
  //   @apply flex flex-row flex-nowrap gap-[8px];
  // }
}
</style>
