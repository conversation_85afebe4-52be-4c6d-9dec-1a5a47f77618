<template>
  <div v-if="dialogShow">
    <el-dialog v-model="dialogShow" width="1064" higtht="892" @closed="cancelHandle">
      <template v-slot:header>
        <HeadTitle title="添加企业"></HeadTitle>
      </template>
      <div class="main">
        <el-form inline :label-width="80">
          <el-form-item label="企业名称">
            <el-input v-model="relateOrgName" placeholder="请输入企业名称" clearable />
          </el-form-item>
        </el-form>

        <el-table
          ref="tableRef"
          :data="tableData"
          stripe
          style="width: 100%"
          height="400"
          row-key="relateOrgCode"
          @selection-change="selectionChange"
        >
          <el-table-column type="selection" :reserve-selection="true" fixed width="45" />
          <el-table-column prop="relateOrgName" label="企业名称" align="center" show-overflow-tooltip />
          <el-table-column prop="responsibleUserName" label="企业负责人" align="center" how-overflow-tooltip />
          <el-table-column
            prop="responsibleUserContactNo"
            label="企业负责人手机号"
            min-width="120px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="orgName" label="所属单位" align="center" show-overflow-tooltip />
        </el-table>

        <div class="pagination-box">
          <el-pagination
            v-model:currentPage="pagi.pageNo"
            v-model:page-size="pagi.pageSize"
            :total="pagi.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="getTableData"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="cancelHandle">取消</el-button>
        <el-button type="primary" @click="confirmHandle">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { relatedUnit } from './modifyFrom'
import $API from '~/common/api'
import { formData } from './modifyFrom'

defineOptions({ name: 'ProjectMgrModifyUnitselect' })

const dialogShow = ref(false)

const relateOrgName = ref('')
watch(
  () => relateOrgName.value,
  () => {
    handleSizeChange()
  }
)

const tableRef = ref()
const tableData = ref<any[]>([])
const isUpdating = ref(false)

const pagi = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

const getTableData = () => {
  const params = {
    pageNo: pagi.value.pageNo,
    pageSize: pagi.value.pageSize,
    relateOrgName: relateOrgName.value,
    orgCode: formData.value.orgCode
  }

  $API
    .post({
      url: 'atomic-upms-service/org/project/queryRelateInfoByOrgCode',
      data: params
    })
    .then((res: any) => {
      if (+res.code === 200) {
        isUpdating.value = true
        tableData.value = [...res.data.rows]
        pagi.value.total = res.data?.total || 0

        nextTick(() => {
          isUpdating.value = false
          // toggleSelection()
        })
      }
    })
}
const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

let selectedList: any[] = []
const setSelectedList = () => {
  selectedList = JSON.parse(JSON.stringify(relatedUnit.selectedUnitList.value))
}

const selectionChange = (selectRows) => {
  selectedList = selectRows
}

const toggleSelection = () => {
  if (selectedList.length > 0) {
    selectedList.forEach((row: any) => {
      tableRef.value?.toggleRowSelection(row, true)
    })
  }
}

const emits = defineEmits(['confirm', 'cancel'])
const confirmHandle = () => {
  emits('confirm', selectedList)
  showDialogHandle(false)
}
const cancelHandle = () => {
  emits('cancel')
  showDialogHandle(false)
}

const showDialogHandle = (visible = true) => {
  dialogShow.value = visible
  if (visible) {
    handleSizeChange()
    setSelectedList()
    nextTick(() => {
      toggleSelection()
    })
  }
}
defineExpose({
  showDialogHandle
})
</script>

<style scoped lang="scss">
.pagination-box {
  @apply my-[20px] flex justify-end;
}
</style>
