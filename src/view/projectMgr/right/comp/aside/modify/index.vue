<template>
  <div class="aside-modify">
    <div class="modify-item">
      <h3 class="title">项目信息：</h3>
      <el-form ref="formRef" label-width="auto" :model="formData" :rules="formRules">
        <el-form-item label="所属单位" prop="orgCode">
          <el-input readonly v-model="orgName" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="formData.projectName" placeholder="请输入项目名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="项目类型" prop="xmlx">
          <el-select v-model="formData.xmlx" placeholder="全部" clearable :disabled="isEdit">
            <el-option v-for="item in xmlxOpt" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目负责人" prop="responsibleUserName">
          <el-input v-model="formData.responsibleUserName" placeholder="请选择项目负责人" readonly>
            <template #append>
              <el-button type="primary" plain size="small" @click="showPreson">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="项目起止日期" prop="startDate">
          <el-date-picker :disabled="isEdit" v-model="dateRangeValue" type="daterange" range-separator="~"
            start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="项目地点" prop="sgdd">
          <el-input v-model="formData.sgdd" placeholder="请输入项目地点" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="项目描述" prop="projectDesc">
          <el-input v-model="formData.projectDesc" type="textarea" placeholder="请输入项目描述"
            :autosize="{ minRows: 4, maxRows: 5 }" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
    </div>
    <div class="modify-item">
      <h3 class="title">关联企业：</h3>
      <RelatedUnit />
    </div>
    <div class="modify-action">
      <el-button :disabled="isSubmitting" @click="cancelHandle">取消</el-button>
      <el-button type="primary" :loading="isSubmitting" @click="submitHandle">{{
        isSubmitting ? '提交中...' : '确定'
        }}</el-button>
    </div>

    <SelectUsers ref="selectUsersRef" :limit="1" @selectedUser="selectedUser"></SelectUsers>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, watchEffect, toRaw, computed } from 'vue'
import RelatedUnit from './relatedUnit.vue'
import SelectUsers from '@/view/components/selectUsers/index.vue'
import { formData, formRules, relatedUnit } from './modifyFrom'
import { treeSerivce } from '@/view/projectMgr/left/treeService'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'ProjectMgrModify' })

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const isEdit = computed(() => {
  return !!props.id
})

const formRef = ref()

const orgName = ref('')

watchEffect(() => {
  if (!isEdit.value) {
    orgName.value = treeSerivce.curNode.value.orgName
    formData.value.orgCode = treeSerivce.curNode.value.orgCode
  }
})

const dateRangeValue = ref<any[]>([])
watch(
  () => dateRangeValue.value,
  (v) => {
    formData.value.startDate = v ? v[0] : ''
    formData.value.endDate = v ? v[1] : ''
  }
)

const xmlxOpt = ref<
  Array<{
    dictLabel: string
    dictValue: string
    [prop: string]: any
  }>
>([])
const getDict = () => {
  $API
    .get({
      url: 'atomic-upms-service/common/v1/queryDictList',
      params: {
        type: 'xmsg_xmlx'
      }
    })
    .then((res: any) => {
      if (+res.code === 200) {
        xmlxOpt.value = res.data
      }
    })
}
getDict()

const getBaseInfo = () => {
  $API
    .get({
      url: 'atomic-upms-service/org/project/detail',
      params: {
        projectId: props.id
      }
    })
    .then((res: any) => {
      const { code, data } = res
      if (+code === 200) {
        orgName.value = data.orgName
        formData.value.orgCode = data.orgCode
        formData.value.projectName = data.projectName
        formData.value.responsibleUserName = data.responsibleUserName
        formData.value.responsibleUserId = data.responsibleUserId
        formData.value.projectDesc = data.projectDesc
        formData.value.xmlx = data.xmlx
        formData.value.sgdd = data.sgdd
        dateRangeValue.value = [data.startDate, data.endDate]
      }
    })
}
const getRelatedUnitList = () => {
  $API
    .post({
      url: 'atomic-upms-service/org/project/queryProjectRelateList',
      data: {
        projectId: props.id,
        pageNo: 1,
        pageSize: -1
      }
    })
    .then((res: any) => {
      const { code, data } = res
      if (+code === 200) {
        relatedUnit.selectedUnitList.value = data.rows
      }
    })
}
const getDetail = () => {
  if (!isEdit.value) return
  formData.value.projectId = props.id
  getBaseInfo()
  getRelatedUnitList()
}

// 显示人员弹框
const selectUsersRef = ref()
const showPreson = () => {
  selectUsersRef.value.outerVisible = true
  let selectedList: {
    id: string
    userName: any
  }[] = []
  if (formData.value.responsibleUserId) {
    const idList = formData.value.responsibleUserId.split(',')
    const nameList = formData.value.responsibleUserName.split(',')
    selectedList = nameList.map((item: string, index: number) => {
      return {
        id: idList[index],
        userName: item
      }
    })
    nextTick(() => {
      selectUsersRef.value.toggleSelection(selectedList)
    })
  }
}
const selectedUser = (list: any) => {
  formData.value.responsibleUserName = list.map((item: any) => item.userName).join(',')
  formData.value.responsibleUserId = list.map((item: any) => item.id).join(',')
}

const emits = defineEmits(['cancel', 'confirm'])

const isSubmitting = ref(false)
const submitHandle = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      let _updateProject = 'addProject'

      const params = JSON.parse(JSON.stringify(formData.value))
      params.relateInfoList = toRaw(relatedUnit.selectedUnitList.value)

      if (isEdit.value) {
        const _deleteOrgCode = toRaw(relatedUnit.preDeleteUnit.value)
        params.deleteRelateOrgCodeList = _deleteOrgCode.map((item) => item.relateOrgCode)
        _updateProject = 'updateProject'
      }

      isSubmitting.value = true
      $API
        .post({
          url: 'atomic-upms-service/org/project/' + _updateProject,
          data: params
        })
        .then((res: any) => {
          if (+res.code === 200) {
            const msg = isEdit.value ? '编辑成功' : '新增成功'
            ElMessage.success(msg)
            emits('confirm')
          }
        })
        .finally(() => {
          isSubmitting.value = false
        })
    }
  })
}
const cancelHandle = () => {
  emits('cancel')
}

getDetail()
</script>

<style scoped lang="scss">
.aside-modify {
  @apply w-full;

  .modify-item {
    @apply mb-[30px] font-bold text-[#333];
  }

  .title {
    @apply mb-[20px];
  }

  .modify-action {
    @apply absolute bottom-0 h-[62px] bg-[#fff] flex justify-end;
    width: calc(100% - 40px);
    border-top: 1px solid rgb(235, 238, 245);
    padding: 10px 20px 20px;
    z-index: 9;
  }
}
</style>
