import { ref } from 'vue'

export const initFormData = () => ({
  orgCode: '',
  projectId: '',
  projectName: '',
  responsibleUserName: '',
  responsibleUserId: '',
  projectDesc: '',
  startDate: '',
  endDate: '',
  xmlx: '',
  sgdd: '',
  deleteRelateOrgCodeList: [],
  relateInfoList: []
})
export const formData = ref(initFormData())

export const resetFormData = () => {
  formData.value = initFormData()
}

export const formRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 50, message: '项目名称长度最多50个字', trigger: 'blur' }
  ],
  sgdd: [
    { required: true, message: '请输入项目地点', trigger: 'blur' },
    { min: 1, max: 50, message: '项目地点长度最多50个字', trigger: 'blur' }
  ],
  xmlx: [{ required: true, message: '请选择项目类型', trigger: ['blur', 'change'] }],
  responsibleUserName: [{ required: true, message: '请选择项目负责人', trigger: ['blur', 'change'] }],
  startDate: [{ required: true, message: '请选择项目起止日期', trigger: ['blur', 'change'] }]
}

// 已选择的关联企业
export class relatedUnit {
  static selectedUnitList = ref<any[]>([])

  static preDeleteUnit = ref<any[]>([])

  static deleteSelectedList = (row: any, index: number) => {
    relatedUnit.preDeleteUnit.value.push(row)
    relatedUnit.selectedUnitList.value.splice(index, 1)
  }

  static clear = () => {
    relatedUnit.preDeleteUnit.value = []
    relatedUnit.selectedUnitList.value = []
  }
}
