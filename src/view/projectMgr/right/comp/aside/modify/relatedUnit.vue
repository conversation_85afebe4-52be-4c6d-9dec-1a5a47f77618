<template>
  <div class="related-units">
    <div class="filter-box">
      <!-- <el-form inline :label-width="80">
        <el-form-item label="企业名称">
          <el-input v-model="unitName" placeholder="请输入企业名称" clearable />
        </el-form-item>
      </el-form> -->
      <el-button type="primary" @click="addHandle">添加企业</el-button>
    </div>

    <el-table :data="tableData" stripe style="width: 100%" max-height="500">
      <el-table-column prop="relateOrgName" label="企业名称" align="center" show-overflow-tooltip />
      <el-table-column prop="responsibleUserName" label="企业负责人" align="center" how-overflow-tooltip />
      <el-table-column
        prop="responsibleUserContactNo"
        label="企业负责人手机号"
        min-width="120px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="orgName" label="所属单位" align="center" show-overflow-tooltip />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-popconfirm
            width="220"
            title="确定要删除这家企业吗?"
            confirm-button-text="确定"
            cancel-button-text="取消"
            cancel-button-type="default"
            @confirm="relatedUnit.deleteSelectedList(scope.row, scope.$index)"
          >
            <template #reference>
              <el-button>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <UnitSelect ref="unitSelectRef" @confirm="confirmHandle" @cancel="cancelHandle" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UnitSelect from './unitSelect.vue'
import { relatedUnit } from './modifyFrom'

defineOptions({ name: 'ProjectMgrModifyUnits' })

const unitSelectRef = ref()

// const unitName = ref('')

const tableData = computed(() => {
  return relatedUnit.selectedUnitList.value
})

const addHandle = () => {
  unitSelectRef.value.showDialogHandle()
}

const confirmHandle = (selectedUnits: any) => {
  if (selectedUnits.length > 0) {
    relatedUnit.selectedUnitList.value.forEach((item) => {
      const _index = selectedUnits.findIndex((row) => row.relateOrgCode === item.relateOrgCode)
      if (_index < 0) {
        relatedUnit.preDeleteUnit.value.push(item)
      }
    })
  } else {
    if (relatedUnit.selectedUnitList.value.length > 0) {
      relatedUnit.preDeleteUnit.value = relatedUnit.selectedUnitList.value
    }
  }
  relatedUnit.selectedUnitList.value = selectedUnits
}
const cancelHandle = () => {}
</script>

<style scoped lang="scss">
.related-units {
  .filter-box {
    @apply flex flex-row flex-nowrap justify-end items-center mb-[20px];
  }
  .pagination-box {
    @apply my-[20px] flex justify-end;
  }
}
</style>
