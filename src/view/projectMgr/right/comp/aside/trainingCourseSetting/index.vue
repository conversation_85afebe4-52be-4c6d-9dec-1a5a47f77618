<template>
  <div class="training-course-setting">
    <!--详情-->
    <ProjectDetail />
    <!--列表-->
    <div class="mt-[20px]">
      <el-button v-if="!isTC" class="mb-[16px]" type="primary" @click="setCourse">设定培训课程</el-button>
      <div class="process-desc">
        <div class="tips">
          <img src="./assets/icon-tips.png" alt="" />
          流程说明：
        </div>
        <el-steps class="flex-1" :active="4" align-center>
          <el-step :title="item" v-for="(item, i) in stepsList" :key="i" />
        </el-steps>
      </div>
      <!--课程信息-->
      <div class="course-info" v-if="courseInfo">
        <div class="mb-[18px] flex items-center">
          <span class="text-[16px] font-bold mr-[16px]">课程名称：{{ courseInfo?.curriculumName }}</span>
          <span class="status">已发送项目成员学习</span>
        </div>
        <div class="mb-[16px]">适用项目类型：{{ courseInfo?.projectTypeName || courseInfo?.relatedTypeName }}</div>
        <div class="flex">
          培训资料：
          <div class="file">
            <span
              v-for="item in courseInfo?.trainingMaterialFiles"
              :key="item?.id"
              @click="handleFileClick(item.filePath)"
              >{{ item.fileName }}</span
            >
          </div>
        </div>
        <el-button v-if="!isTC" plain class="remove" type="danger" @click="handleRemove">移除</el-button>
      </div>
      <div class="empty" v-else>
        <img class="w-[302px] h-[215px]" src="./assets/no-course.png" alt="" />
        <span>还未设定课程哦，请先设定课程</span>
      </div>
    </div>
    <!--课程设定弹窗-->
    <course-set-dialog
      v-model:show="dialogVisible"
      :project-id="projectData?.projectId"
      @success="courseSetSuccess"
    ></course-set-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ProjectDetail from '~/view/projectMgr/right/comp/aside/ProjectDetail.vue'
import CourseSetDialog from './comp/courseSetDialog.vue'
import $API from '~/common/api'
import config from '~/config'
import { useRouter } from 'vue-router'

const props = defineProps({
  projectData: Object
})
const $emit = defineEmits(['refreshList'])

const router = useRouter()
const stepsList = ['设定进场课程', '自动发送该项目成员移动端', '员工移动端自主学习', '项目管理人员培训跟进']
const dialogVisible = ref(false)
const courseInfo = ref<any>()
const isTC = computed(() => props.projectData?.projectStatus === '4') // 是否已退场

// 查询已设定课程
async function getCourseInfo(trainCurriculumManageId?: string) {
  if (!props.projectData?.trainCurriculumManageId && !trainCurriculumManageId) {
    courseInfo.value = null
    return
  }
  const res: any = await $API.post({
    url: 'edu-inter-server/relateProject/entryManager/queryRelateProjectTrainCurriculumSetting',
    data: {
      projectId: props.projectData?.projectId, // 项目ID
      trainCurriculumManageId: trainCurriculumManageId || props.projectData?.trainCurriculumManageId // 培训课程ID
    }
  })
  if (res.code === 'success') {
    courseInfo.value = res.data
  } else {
    courseInfo.value = null
  }
  console.log(res)
}

function courseSetSuccess(trainCurriculumManageId: string) {
  getCourseInfo(trainCurriculumManageId)
  $emit('refreshList')
}

// 设定培训课程
function setCourse() {
  if (courseInfo.value) {
    return ElMessage.warning('已经设定过培训课程了')
  }
  dialogVisible.value = true
}

// 移除课程
function handleRemove() {
  ElMessageBox.confirm('移除后，已发送课程也会撤回，确定要移除课程吗？', '', {
    customClass: 'custom-message-box',
    confirmButtonText: '移除',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const res: any = await $API.post({
        url: 'edu-inter-server/relateProject/entryManager/removeRelateProjectTrainCurriculumSetting',
        data: {
          projectId: props.projectData?.projectId, // 项目ID
          trainCurriculumManageId: courseInfo.value.id // 培训课程ID
        }
      })
      if (res.code === 'success') {
        courseInfo.value = null
        $emit('refreshList')
        ElMessage.success('课程移除成功')
      }
    })
    .catch(() => {})
}

// 处理文件点击
const handleFileClick = (fileName: string) => {
  if (!fileName) return
  const suffix = fileName.split('.').pop() || ''
  const url = fileName.includes('http') ? fileName : config.downloadFileUrl + fileName
  if (['doc', 'docx'].includes(suffix)) {
    window.open(router.resolve({ path: '/preview', query: { value: url } }).href, '_blank')
  } else {
    window.open(url, '_blank')
  }
}

getCourseInfo()
defineOptions({ name: 'TrainingCourseSetting' }) // 培训课程设定
</script>

<style scoped lang="scss">
.training-course-setting {
  @apply w-full h-full;
  display: grid;
  grid-template-rows: auto 1fr;
  color: #000000;

  .process-desc {
    @apply h-[118px] px-[120px] flex items-center mb-[16px] rounded-[4px] relative;
    background: #edf1ff;
    border: 1px solid #527cff;

    .tips {
      @apply absolute left-[12px] top-[12px] flex items-center text-[14px] text-[#527cff];
      img {
        @apply w-[16px] h-[16px] mr-[10px];
      }
    }

    :deep(.el-step__title.is-finish) {
      @apply text-[12px] text-[#222222] leading-[38px];
    }
  }

  .course-info {
    @apply p-[16px] border border-[#E4E7ED] rounded-[8px] relative text-[#222222] text-[14px];

    .status {
      @apply text-[12px] text-[#527CFF] h-[22px] flex items-center justify-center rounded-[4px] px-[8px];
      background: #edf1ff;
    }

    .file {
      @apply flex flex-col;
      span {
        @apply cursor-pointer text-[#527CFF] mb-[8px];
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .remove {
      @apply absolute right-[16px] bottom-[16px];
    }
  }

  .empty {
    @apply relative flex justify-center items-center;
    span {
      @apply absolute left-[50%] bottom-[14px] text-[#606266] text-[14px];
      transform: translateX(-50%);
    }
  }
}
</style>
