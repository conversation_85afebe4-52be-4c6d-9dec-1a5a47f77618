<template>
  <div class="dailyCheck">
    <div class="header">
      <div class="flex justify-start items-center">
        <div class="trouble-number flex justify-start items-center">
          <div style="z-index: 2">
            <div>已完成检查任务</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic group-separator="," :value="newObj && newObj.completionTaskNum"></el-statistic>
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px">
          <div style="z-index: 2">
            <div>检查出隐患</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.total"></el-statistic>
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>已整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.disposedNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>整改中</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.disposingNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>待整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.unDisposedNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
      </div>
      <div class="projectMgr-table" style="margin-top: 20px">
        <el-table
          ref="multipleTableRef"
          :data="tableData"
          stripe
          :height="tableHeight"
          style="width: 100%"
          row-key="projectId"
        >
          <el-table-column type="index" width="60" label="序号" align="center" />
          <el-table-column prop="planName" label="计划名称" align="center" min-width="110" show-overflow-tooltip />
          <el-table-column prop="planTypeName" label="检查类型" align="center" min-width="110" show-overflow-tooltip />
          <el-table-column prop="unitNames" label="检查对象" align="center" min-width="110" show-overflow-tooltip />
          <el-table-column
            prop="planStartTime"
            label="任务起止时间"
            align="center"
            min-width="110"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ scope.row.planStartTime }}~{{ scope.row.planEndTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taskState" label="任务状态" align="center" min-width="110" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-if="scope.row.taskState == 1" color="#E6A23C" style="color: white">待开始</el-tag>
              <el-tag v-if="scope.row.taskState == 2" color="#527CFF" style="color: white">进行中</el-tag>
              <el-tag v-if="scope.row.taskState == 3" color="#67C23A" style="color: white">已完成</el-tag>
              <el-tag v-if="scope.row.taskState == 4" color="#F56C6C" style="color: white">已关闭</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timeState" label="任务时效" align="center" min-width="110" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.timeState == 1">正常</span>
              <span v-if="scope.row.timeState == 2">逾期</span>
            </template>
          </el-table-column>
          <el-table-column prop="taskHazardNum" label="检查隐患数" align="center" min-width="110" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.taskHazardNum || '0' }} 个 </span>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-box">
          <el-pagination
            v-model:currentPage="pagi.pageNo"
            v-model:page-size="pagi.pageSize"
            :total="pagi.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="getTableData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, Ref, ref } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

console.log(actionData.value, '<<<<<<<<<<<<<<<<<<<')

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const { tableHeight } = mixTableHeight({ subtractHeight: 500 })
const tableData = ref([])
const getTableData = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/coopManageForHazard/hazardPageList',
    data: {
      projectId: actionData.value.projectId,
      pageNo: pagi.value.pageNo,
      pageSize: pagi.value.pageSize
    }
  })
  console.log('res', res)
  if (res.code === 'success') {
    tableData.value = res.data.rows
    pagi.value.total = res.data?.total || 0
  }
}
const newObj = ref()
const getDetail = async () => {
  let res = await $API.post({
    url: `/edu-inter-server/coopManageForHazard/queryCompletionTaskNum?projectId=${actionData.value.projectId}`
  })
  console.log('res', res)
  newObj.value = res.data
}
const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}
onMounted(() => {
  getDetail()
  getTableData()
})

defineOptions({ name: 'HazardMgrComp' })
</script>

<style scoped lang="scss">
::v-deep {
  .el-statistic__content {
    color: white !important;
  }
}

.projectMgr-table {
  display: grid;

  .pagination-box {
    @apply flex justify-end mt-[20px];
  }
}

.trouble-number:nth-child(1) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(2) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(3) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(4) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png4.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(5) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png5.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
