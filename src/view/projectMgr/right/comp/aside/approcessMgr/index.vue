<template>
  <div class="approach-wrap">
    <!--详情-->
    <ProjectDetail />
    <!--列表-->
    <el-tabs v-model="activeTab" class="safety-tabs">
      <el-tab-pane label="隐患排查治理" name="HazardMgr">
        <!-- 隐患排查组件 -->
        <HazardMgr />
      </el-tab-pane>
      <el-tab-pane label="安全作业" name="safe">
        <!-- 危险作业组件 -->
        <WorkRecord />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, Ref } from 'vue'
import ProjectDetail from '~/view/projectMgr/right/comp/aside/ProjectDetail.vue'
import HazardMgr from './HazardMgr.vue'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import WorkRecord from './workRecord.vue'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>

const activeTab = computed(() => (currentAction.value.action === ACTION.RISK ? 'HazardMgr' : 'safe'))

// const activeTab = computed(() => 'HazardMgr')

defineOptions({ name: 'processMgr' }) // 项目进场管理
</script>

<style scoped lang="scss">
.approach-wrap {
  @apply w-full h-full;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 30px;
}
</style>
