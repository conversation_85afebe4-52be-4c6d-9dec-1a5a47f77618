<template>
  <div class="person-list">
    <!--filter-->
    <div class="flex justify-end">
      <el-input
        class="!w-[260px]"
        v-model="query.userName"
        placeholder="请输入姓名搜索"
        :suffix-icon="Search"
        clearable
      />
      <el-button
        type="primary"
        @click="addPerson"
        class="bg-[#2468f2]"
        style="margin-left: 10px"
        v-if="props.obj.projectStatusName !== '已退场'"
        >添加人员</el-button
      >
    </div>

    <el-table :data="tableData" height="100%" stripe>
      <el-table-column type="index" width="60" label="序号" align="center" />
      <el-table-column prop="relateOrgName" label="承包商企业" align="center" width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.relateOrgName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="承包商人员姓名" align="center" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.userName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="post" label="岗位" align="center" min-width="140" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.post || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="insureFiles" label="投保信息（工伤）" align="center" min-width="140" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList v-if="scope.row.insureFiles.length" :img-list="scope.row.insureFiles" />
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column prop="idFiles" label="身份证照片" align="center" width="160" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList v-if="scope.row.idFiles.length" :img-list="scope.row.idFiles || []" />
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="documentFiles" label="证件照（免冠照）" align="center" width="160" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList v-if="scope.row.documentFiles.length" :img-list="scope.row.documentFiles || []" />
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="idNumber" label="身份证号" align="center" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.idNumber || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="idStartPeriod" label="身份证有效期" align="center" min-width="210" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.idStartPeriod && scope.row.idEndPeriod">
            {{ scope.row.idStartPeriod }} ~ {{ scope.row.idEndPeriod }}
          </span>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄（岁）" align="center" width="100" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.age || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="specialOperation"
        label="否是特种作业人员"
        align="center"
        min-width="140"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.specialOperation == 1">是</span>
          <span v-else-if="scope.row.specialOperation == 0">否</span>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="300" fixed="right">
        <template #default="scope">
          <div class="flex items-center">
            <div class="mr-4 flex items-center">
              <span class="text-[#2468f2] mr-2">设为项目现场负责人</span>
              <el-popconfirm
                :title="scope.row.userType === '1' ? '确定设为项目现场负责人吗?' : '确定取消项目现场负责人吗?'"
                confirm-button-text="确定"
                cancel-button-text="取消"
                @confirm="confirmSetLeader(scope.row)"
                width="260"
                :icon="QuestionFilled"
                icon-color="#f56c6c"
              >
                <template #reference>
                  <el-switch
                    v-model="scope.row.userType"
                    inactive-color="#DCDFE6"
                    active-color="#2468f2"
                    inline-prompt
                    active-text="开"
                    inactive-text="关"
                    active-value="2"
                    inactive-value="1"
                    @change="preventSwitchChange($event, scope.row)"
                  />
                </template>
              </el-popconfirm>
            </div>
            <div>
              <el-button type="danger" plain size="small" @click="handleRemove(scope.row)">移除人员</el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="flex justify-end">
      <el-pagination
        v-model:currentPage="query.pageNo"
        v-model:page-size="query.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <AddPerson
    v-model:addPersonVisible="addPersonVisible"
    :selected-persons="selectedPersons"
    @update="handleAddPersons"
  />
</template>
<script setup lang="ts">
import imgViewList from '@/components/imgViewList/index.vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, inject, Ref, ref, watch } from 'vue'
import $API from '~/common/api'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import AddPerson from '~/view/enterprise/projectMgr/comp/aside/approachMgr/addPerson.vue'
import { useRoute } from 'vue-router'
import { QuestionFilled } from '@element-plus/icons-vue'

const props: any = defineProps({ obj: Object, activeTab: String })

const route = useRoute()
const addPersonVisible = ref(false)
const selectedPersons = ref<Partial<any>[]>([])

const clear = () => {
  query.value = {
    pageNo: 1,
    pageSize: 20,
    userName: ''
  }
}

watch(
  () => props.activeTab,
  (val) => {
    if (val === 'personMgr') {
      query.value = {
        pageNo: 1,
        pageSize: 20,
        userName: ''
      }
      total.value = 0
      getDataList()
    }
  }
)

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const total = ref<number>(0)
const query = ref({
  pageNo: 1,
  pageSize: 20,
  userName: ''
})

const handleSizeChange = () => {
  query.value.pageNo = 1
  getDataList()
}
const handleCurrentChange = () => {
  getDataList()
}

const tableData = ref([])

watch(
  () => query.value.userName,
  () => {
    query.value.pageNo = 1
    getDataList()
  },
  { immediate: true }
)

async function getDataList() {
  const params: any = {
    ...query.value,
    projectId: actionData.value.projectId,
    relateOrgCode: route.query.id
  }
  if (props.obj.projectStatusName == '已退场') {
    params.userStatus = 3
  }
  const url = '/atomic-upms-service/relate/project/queryProjectUserList'
  const res: any = await $API.post({ url, data: params })
  if (res.code === 200) {
    total.value = res.data.total
    tableData.value = res.data.rows
  }
}

function handleRemove(row: any) {
  ElMessageBox({
    title: '提示',
    message: '确认移除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        const url = '/atomic-upms-service/relate/project/delProjectUser'
        const params = { userId: row.userId, projectId: actionData.value.projectId }
        let res: any = await $API.post({ url, data: params })
        if (+res.code === 200) {
          ElMessage({ message: '移除成功', type: 'success' })
          await getDataList()
        }
      }
      done()
    }
  })
}

const addPerson = () => {
  addPersonVisible.value = true
}

const handleAddPersons = async (persons) => {
  const arr = persons.map((item) => {
    return {
      ...item,
      projectId: actionData.value.projectId,
      relateOrgCode: route.query.id
    }
  })
  const url = '/atomic-upms-service/relate/project/addProjectUser'
  const res: any = await $API.post({ url, data: arr })
  if (res.code === 200) {
    ElMessage.success('新增成功!')
    await getDataList()
  }
}

const switchPreviousStates = ref({})
const preventSwitchChange = async (val, row) => {
  switchPreviousStates.value[row.userId] = val
  row.userType = val === '2' ? '1' : '2'
}

const confirmSetLeader = async (row) => {
  const newValue = switchPreviousStates.value[row.userId]
  row.userType = newValue
  handleSetLeader(newValue, row)
  const url = '/atomic-upms-service/relate/project/changeProjectUserType'
  const res: any = await $API.post({
    url,
    data: { ...row, projectId: actionData.value.projectId, relateOrgCode: route.query.id }
  })
  if (res.code === 200) {
    await getDataList()
  }
}

const handleSetLeader = (val, row) => {
  if (val === '2') {
    personList.value.forEach((item) => {
      if (item.userId !== row.userId) {
        item.userType = '1'
      }
    })
  }
}
const personList = ref<Partial<any>[]>([])

defineOptions({ name: 'personMgrComp' })
</script>

<style scoped lang="scss">
.person-list {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: 20px;
}
</style>
