<template>
  <div class="w_detail_dialog_box" v-if="showDrawer">
    <el-drawer size="85%" v-model="showDrawer" modal-class="my-drawer">
      <template #header>
        <div class="w_detail_header_box">
          <div class="mr-11px"></div>
          <div>考试试卷</div>
        </div>
      </template>
      <ExamPagers :examData="examData" />
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import ExamPagers from './ExamPapers.vue'
const showDrawer = ref(false)

const examData = ref([])

const showDialog = (row: any) => {
  examData.value = row || {}

  showDrawer.value = true
}

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'trainingTrackCompDetail' })
</script>

<style scoped lang="scss">
.w_detail_dialog_box {
  // :deep(.el-drawer.rtl) {
  //   width: 648px !important;
  // }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
    font-size: 16px;
    font-weight: 700;
    color: #333;
  }

  .w_detail_header_box {
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.my-drawer.el-overlay) {
    background-color: rgba(0, 0, 0, 0);
  }

  :deep(.el-drawer) {
    border-radius: 10px 0 0 10px;
  }
}
</style>
