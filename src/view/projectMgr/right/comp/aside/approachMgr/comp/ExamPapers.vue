<template>
  <div class="container-view">
    <!-- <div class="pdfDom" style="z-index: 1; height: auto" id="pdfDom">
      <div class="text-center">
        <div v-if="isExam == '1'" class="beautiful-score">
          <div>{{ examResultScore }}</div>
          <el-image class="fraction-bottom" :src="fractionBottom"></el-image>
        </div>
        <div class="name">{{ examInfo.examName || '--' }}</div>
        <div class="title">
          <span class="mr-[30px]">总题数：{{ examInfo.count }}题</span>
          <span class="mr-[30px]" v-if="singleList.length">单选题：{{ singleList.length }}题</span>
          <span class="mr-[30px]" v-if="multipleSelectList.length">多选题：{{ multipleSelectList.length }}题</span>
          <span class="mr-[30px]" v-if="judgeList.length">判断题：{{ judgeList.length }}题</span>
          <span class="mr-[30px]" v-if="fillBlankList.length">填空题：{{ fillBlankList.length }}题</span>
          <span>总分：{{ examInfo.totalScore }}分</span>
        </div>
      </div>
      <div class="exam-list-box">
        <div class="single" v-if="singleList.length != 0">
          {{ getNameTitle('单选题') }}、单选题（总共{{ getFindExam('1')?.quesCount }}题， 每题{{
            getFindExam('1')?.quesScore
          }}分， 共{{ getFindExam('1')?.totalScore }}分）
        </div>
        <div v-for="(item, index) in singleList" :key="index" class="exam-list">
          <div>
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex option-p">
                <span
                  :class="
                    item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.RIGHT
                      ? 'right-span size-span'
                      : item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.WRONG
                        ? 'wrong-span size-span'
                        : 'size-span'
                  "
                  >{{ getLetter(sub) }}.</span
                >
                <span v-html="option.optionText"></span>
              </p>
            </div>
          </div>
          <el-image
            v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
            class="right-wrong"
            :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
          ></el-image>
        </div>
        <div class="single" v-if="multipleSelectList.length != 0">
          {{ getNameTitle('多选题') }}、多选题（总共{{ getFindExam('2')?.quesCount }}题， 每题{{
            getFindExam('2')?.quesScore
          }}分， 共{{ getFindExam('2')?.totalScore }}分）
        </div>
        <div v-for="(item, index) in multipleSelectList" :key="index" class="exam-list">
          <div>
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex option-p">
                <span
                  :class="
                    item.myOption.includes(option.id) && item.answerJudge == AnswerJudgeEnum.RIGHT
                      ? 'right-span size-span'
                      : item.myOption.includes(option.id) && item.answerJudge == AnswerJudgeEnum.WRONG
                        ? 'wrong-span size-span'
                        : 'size-span'
                  "
                  >{{ getLetter(sub) }}.</span
                >
                <span v-html="option.optionText"></span>
              </p>
            </div>
          </div>
          <el-image
            v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
            class="right-wrong"
            :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
          ></el-image>
        </div>

        <div class="single" v-if="judgeList.length != 0">
          {{ getNameTitle('判断题') }}、判断题（总共{{ getFindExam('3')?.quesCount }}题， 每题{{
            getFindExam('3')?.quesScore
          }}分， 共{{ getFindExam('3')?.totalScore }}分）
        </div>
        <div v-for="(item, index) in judgeList" :key="index" class="exam-list">
          <div>
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex option-p">
                <span
                  :class="
                    item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.RIGHT
                      ? 'right-span size-span'
                      : item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.WRONG
                        ? 'wrong-span size-span'
                        : 'size-span'
                  "
                  >{{ getLetter(sub) }}.</span
                ><span v-html="option.optionText"></span>
              </p>
            </div>
          </div>
          <el-image
            v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
            class="right-wrong"
            :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
          ></el-image>
        </div>
        <div class="single" v-if="fillBlankList.length != 0">
          {{ getNameTitle('填空题') }}、填空题（总共{{ getFindExam('4')?.quesCount }}题， 每空{{
            getFindExam('4')?.quesScore
          }}分， 共{{ getFindExam('4')?.totalScore }}分）
        </div>
        <div v-for="(item, index) in fillBlankList" :key="index" class="exam-list">
          <div>
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex option-p">
                <span :class="option.getOption == option.optionText ? 'right-span size-span' : 'wrong-span size-span'"
                  >（{{ sub + 1 }}）</span
                >
                <span v-html="option.optionText"></span>
              </p>
            </div>
          </div>
          <el-image
            v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
            class="right-wrong"
            :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
          ></el-image>
        </div>
      </div>
    </div> -->
    <el-card style="z-index: 10" class="examContent">
      <div class="text-center">
        <div v-if="isExam == '1'" class="beautiful-score">
          <div>{{ examResultScore }}</div>
          <el-image class="fraction-bottom" :src="fractionBottom"></el-image>
        </div>
        <div class="name">{{ examInfo.examName || '--' }}</div>
        <div class="title">
          <span class="mr-[30px]">总题数：{{ examInfo.count }}题</span>
          <span class="mr-[30px]" v-if="singleList.length">单选题：{{ singleList.length }}题</span>
          <span class="mr-[30px]" v-if="multipleSelectList.length">多选题：{{ multipleSelectList.length }}题</span>
          <span class="mr-[30px]" v-if="judgeList.length">判断题：{{ judgeList.length }}题</span>
          <span class="mr-[30px]" v-if="fillBlankList.length">填空题：{{ fillBlankList.length }}题</span>
          <span>总分：{{ examInfo.totalScore }}分</span>
        </div>
      </div>
      <el-scrollbar style="margin-top: 43px" :height="windowSize.height.value - 320 + 'px'">
        <div class="exam-list-box">
          <!-- 单选题 -->
          <!-- <div class="single" v-if="examTypeList[0]?.quesCount"> -->
          <div class="single" v-if="singleList.length != 0">
            <!-- <div class="single" v-if="examTypeList[0]?.quesCount"> -->
            {{ getNameTitle('单选题') }}、单选题（总共{{ getFindExam('1')?.quesCount }}题， 每题{{
              getFindExam('1')?.quesScore
            }}分， 共{{ getFindExam('1')?.totalScore }}分）
          </div>
          <div v-for="(item, index) in singleList" :key="index" class="exam-list">
            <div>
              <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
              <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
                <p class="mb-[10px] flex option-p">
                  <span
                    :class="
                      item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.RIGHT
                        ? 'right-span size-span'
                        : item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.WRONG
                          ? 'wrong-span size-span'
                          : 'size-span'
                    "
                    >{{ getLetter(sub) }}.</span
                  >
                  <span v-html="option.optionText"></span>
                </p>
              </div>
            </div>
            <el-image
              v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
              class="right-wrong"
              :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
            ></el-image>
          </div>

          <!-- 多选题 -->
          <!-- <div class="single" v-if="getFindExam('2')?.quesCount"> -->
          <div class="single" v-if="multipleSelectList.length != 0">
            {{ getNameTitle('多选题') }}、多选题（总共{{ getFindExam('2')?.quesCount }}题， 每题{{
              getFindExam('2')?.quesScore
            }}分， 共{{ getFindExam('2')?.totalScore }}分）
          </div>
          <div v-for="(item, index) in multipleSelectList" :key="index" class="exam-list">
            <div>
              <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
              <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
                <p class="mb-[10px] flex option-p">
                  <span
                    :class="
                      item.myOption.includes(option.id) && item.answerJudge == AnswerJudgeEnum.RIGHT
                        ? 'right-span size-span'
                        : item.myOption.includes(option.id) && item.answerJudge == AnswerJudgeEnum.WRONG
                          ? 'wrong-span size-span'
                          : 'size-span'
                    "
                    >{{ getLetter(sub) }}.</span
                  >
                  <span v-html="option.optionText"></span>
                </p>
              </div>
            </div>
            <el-image
              v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
              class="right-wrong"
              :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
            ></el-image>
          </div>

          <!-- 判断题 -->
          <!-- <div class="single" v-if="getFindExam('2')?.quesCount"> -->
          <div class="single" v-if="judgeList.length != 0">
            {{ getNameTitle('判断题') }}、判断题（总共{{ getFindExam('3')?.quesCount }}题， 每题{{
              getFindExam('3')?.quesScore
            }}分， 共{{ getFindExam('3')?.totalScore }}分）
          </div>
          <div v-for="(item, index) in judgeList" :key="index" class="exam-list">
            <div>
              <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
              <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
                <p class="mb-[10px] flex option-p">
                  <span
                    :class="
                      item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.RIGHT
                        ? 'right-span size-span'
                        : item.myOption === option.id && item.answerJudge === AnswerJudgeEnum.WRONG
                          ? 'wrong-span size-span'
                          : 'size-span'
                    "
                    >{{ getLetter(sub) }}.</span
                  ><span v-html="option.optionText"></span>
                </p>
              </div>
            </div>
            <el-image
              v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
              class="right-wrong"
              :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
            ></el-image>
          </div>

          <!-- 填空题 -->
          <!-- <div class="single" v-if="examTypeList[3]?.quesCount"> -->
          <div class="single" v-if="fillBlankList.length != 0">
            {{ getNameTitle('填空题') }}、填空题（总共{{ getFindExam('4')?.quesCount }}题， 每空{{
              getFindExam('4')?.quesScore
            }}分， 共{{ getFindExam('4')?.totalScore }}分）
          </div>
          <div v-for="(item, index) in fillBlankList" :key="index" class="exam-list">
            <div>
              <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
              <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
                <p class="mb-[10px] flex option-p">
                  <span :class="option.getOption == option.optionText ? 'right-span size-span' : 'wrong-span size-span'"
                    >（{{ sub + 1 }}）</span
                  >
                  <span v-html="option.optionText"></span>
                </p>
              </div>
            </div>
            <el-image
              v-if="item.answerJudge === AnswerJudgeEnum.RIGHT || item.answerJudge === AnswerJudgeEnum.WRONG"
              class="right-wrong"
              :src="item.answerJudge === AnswerJudgeEnum.RIGHT ? right : wrong"
            ></el-image>
          </div>
        </div>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import $API from '~/common/api'
import { useWindowSize } from '@vueuse/core'
import fractionBottom from '../assets/fraction-bottom.png'
import wrong from '../assets/wrong.png'
import right from '../assets/right.png'
import { AnswerJudgeEnum } from '../constant'

const props = defineProps({
  examData: {
    type: Object,
    default: () => {}
  }
})

interface IExamInfo {
  count: number
  examName: string
  totalScore: number
}

interface IExamType {
  quesCount: number
  quesScore: number
  totalScore: number
}

const examInfo = ref<IExamInfo>({
  count: 0,
  examName: '',
  totalScore: 0
}) // 试卷头部信息
const examTypeList = ref<IExamType[]>([]) // 单选多选汇总
const singleList = ref<any[]>([])
const judgeList = ref<any[]>([])
const fillBlankList = ref<any[]>([])
const multipleSelectList = ref<any[]>([])
const questions = ref<string[]>([]) // 题目列表
const examResultScore = ref() // 题目列表
const userId = props.examData?.userId || ''
const isExam = props.examData?.isExam || '0'
const examPaperId = props.examData?.examPaperId || ''
const id = props.examData?.id || ''
const windowSize = useWindowSize()

const getLetter = (index: any) => {
  const letters: string[] = []
  for (let i = 0; i < 26; i++) {
    letters.push(String.fromCharCode(65 + i)) //输出A-Z 26个大写字母
  }
  return letters[index] || ''
}

// 获取试卷详情
function getDetailById(userId = '0', examPaperId: string, id: string) {
  let params = {
    userId,
    examPaperId,
    id
  }
  $API
    .get({
      url: 'edu-inter-server/safeTrain/examDetail',
      params
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        examInfo.value = res.data.examPaper
        examResultScore.value = res.data.examResultScore
        examTypeList.value = res.data.examScoreSettings || []

        singleList.value = res.data.singleSelectList || []
        judgeList.value = res.data.judgeList || []
        fillBlankList.value = res.data.fillBlankList || []
        if (fillBlankList.value.length > 0) {
          fillBlankList.value.forEach((item: any) => {
            if (item.myOption) {
              item.myOption = JSON.stringify(item.myOption) || []
            } else {
              item.myOption = []
            }
          })
        }
        multipleSelectList.value = res.data.multipleSelectList || []

        questions.value = res.data.examPaper.questions.split(',')
      }
    })
}

function getFindExam(code: string) {
  let obj = examTypeList.value.find((item: any) => item.quesType === code)
  return obj
}

const arrCom = computed(() => {
  let Arr = [
    {
      name: '单选题',
      num: singleList.value.length
    },
    {
      name: '多选题',
      num: multipleSelectList.value.length
    },
    {
      name: '判断题',
      num: judgeList.value.length
    },
    {
      name: '填空题',
      num: fillBlankList.value.length
    }
  ]

  return Arr.filter((item) => item.num > 0)
})

function getNameTitle(name: string) {
  let arr = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  let index = arrCom.value.findIndex((item) => item.name == name)
  return arr[index]
}

// function exportPdf() {
//   htmlToPdf.getPdf('考试试卷')
// }

getDetailById(userId, examPaperId, id)
</script>

<style lang="scss" scoped>
.container-view {
  height: 100%;
  overflow: hidden;
  padding: 0 0 15px 0;
}

.examContent {
  height: 100%;
  padding: 10px;
  position: relative;
  z-index: 10;
}

.name {
  font-weight: 600;
  font-size: 36px;
  color: #313233;
  margin-bottom: 20px;
}

.title {
  font-weight: 400;
  font-size: 16px;
  color: #313233;
}

.single {
  font-weight: 600;
  font-size: 24px;
  color: #313233;
  margin-bottom: 20px;
}

.exam-list {
  min-height: 110px;
  display: flex;
  position: relative;
  background: #ffffff;
  border: 1px solid #ebeef5;
  padding: 20px 20px 10px 20px;
  margin-bottom: 20px;

  span {
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
  }
}

.isRight {
  font-weight: 500;
  font-size: 16px;
  color: #313233;
}

.option-p {
  font-weight: 400;
  font-size: 14px;
  color: #484a4d;
}

:deep(.el-card .is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

.return-button {
  position: absolute;
  top: 72px;
  right: 24px;
}

.beautiful-score {
  position: absolute;
  right: 67px;
  font-weight: 400;
  font-size: 80px;
  color: #d10000;
  line-height: 106px;
  text-align: center;
  font-style: normal;
  text-transform: none;

  .fraction-bottom {
    position: relative;
    top: -45px;
    left: 10px;
  }
}

.right-wrong {
  position: absolute;
  right: 16px;
  width: 73px;
  height: 65px;
}

.size-span {
  height: 24px;
  width: 24px;
  border-radius: 50%;
}

.right-span {
  background: #21bd3f;
}

.wrong-span {
  background: #d10000;
}

.pdfDom {
  padding: 30px;
  width: 100%;
  position: fixed;
  z-index: 1;
  height: auto;
  overflow: hidden;
}
</style>
