<template>
  <div class="detail-wrap min-w-0">
    <div class="project-name w-full flex overflow-hidden">
      <span>项目名称：</span>
      <span class="flex-1 truncate" :title="detailData.projectName">
        {{ detailData.projectName }}
      </span>
    </div>
    <div class="detail-content">
      <div class="detail-item">
        <span>项目所属单位：</span>
        <span :title="detailData.orgName">{{ detailData.orgName || '--' }}</span>
      </div>
      <div class="detail-item">
        <span>项目类型：</span>
        <span :title="detailData.xmlxMc">{{ detailData.xmlxMc || '--' }}</span>
      </div>
      <div class="detail-item">
        <span>项目安全管理员：</span>
        <span :title="detailData.responsibleUserName">{{ detailData.responsibleUserName || '--' }}</span>
      </div>
      <div class="detail-item">
        <span>项目负责部门：</span>
        <span :title="detailData.xmssbmName">{{ detailData.xmssbmName || '--' }}</span>
      </div>
      <div class="detail-item">
        <span>项目地点：</span>
        <span :title="detailData.sgdd">{{ detailData.sgdd || '--' }}</span>
      </div>
      <div class="detail-item">
        <span>项目成员数：</span>
        <span :title="detailData.projectUserNum">{{ detailData.projectUserNum || '0' }}人</span>
      </div>
      <div class="detail-item">
        <span>项目起止日期：</span>
        <span :title="detailData.startDate + ' ~ ' + detailData.endDate"
        >{{ detailData.startDate }} ~ {{ detailData.endDate }}</span
        >
      </div>
      <div class="detail-item">
        <span>施工现场类型：</span>
        <span :title="detailData.sgxclxMc">{{ detailData.sgxclxMc || '--' }} </span>
      </div>
      <div class="detail-item">
        <span>施工方案：</span>
        <div class="file-list" v-if="detailData.constructSchemeList?.length">
          <span
            class="attachment-item"
            v-for="item in detailData.constructSchemeList"
            :key="item.id"
            @click="handleFileClick(item.filePath)"
            :title="item.fileName"
          >
            {{ item.fileName }}
          </span>
        </div>
        <span v-else> -- </span>
      </div>
      <div class="detail-item">
        <span>应急预案：</span>
        <div class="file-list" v-if="detailData.emergencyPlanList?.length">
          <span
            class="attachment-item"
            v-for="item in detailData.emergencyPlanList"
            :key="item.id"
            @click="handleFileClick(item.filePath)"
            :title="item.fileName"
          >
            {{ item.fileName }}
          </span>
        </div>
        <span v-else> -- </span>
      </div>
      <div class="detail-item">
        <span>安全协议：</span>
        <div class="file-list" v-if="detailData.securityProtocolList?.length">
          <span
            class="attachment-item"
            v-for="item in detailData.securityProtocolList"
            :key="item.id"
            @click="handleFileClick(item.filePath)"
            :title="item.fileName"
          >
            {{ item.fileName }}
          </span>
        </div>
        <span v-else> -- </span>
      </div>
      <div class="detail-item">
        <span>施工现场：</span>
        <span :title="detailData.sgxcName">{{ detailData.sgxcName || '--' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, Ref, ref } from 'vue'
import $API from '~/common/api'
import config from '~/config'
import { useRouter } from 'vue-router'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const router = useRouter()

const detailData = ref<any>({})

const getBaseInfo = () => {
  const url = 'construction-core-service/org/project/detail'
  const params = { projectId: actionData.value.projectId, methodName: 'detail' }

  $API.get({ url, params }).then(({ code, data }: Record<string, any>) => {
    if (+code === 200) {
      detailData.value = { ...data }
    }
  })
}
getBaseInfo()

// 处理文件点击
const handleFileClick = (fileName) => {
  if (!fileName) return
  const suffix = fileName.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + fileName
    window.open(router.resolve({ path: '/preview', query: { value: newUrl } }).href, '_blank')
  } else {
    window.open(config.downloadFileUrl + fileName, '_blank')
  }
}

defineOptions({ name: 'ProjectDetailComp' })
</script>

<style scoped lang="scss">
.detail-wrap {
  background: url('./assets/detail-bg.png') center / 100% 100% no-repeat;
  padding: 15px 25px 25px;

  .project-name {
    @apply text-[18px] font-bold leading-[36px];
    margin-bottom: 10px;
  }

  .detail-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    font-size: 14px;

    .detail-item {
      display: flex;
      overflow: hidden;

      span {
        flex-shrink: 0;
      }

      span:last-child {
        @apply truncate;
        flex: 1;
      }

      .file-list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .attachment-item {
          @apply truncate cursor-pointer;
          text-decoration: underline;
          color: #416fff;
        }
      }
    }
  }
}
</style>
