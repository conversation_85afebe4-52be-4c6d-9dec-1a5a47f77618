<template>
  <div class="aside-modify">
    <div class="modify-item">
      <h3 class="title">项目信息：</h3>
      <el-form label-width="auto" :model="formData">
        <el-form-item label="所属单位" prop="orgName">
          <el-input readonly v-model="formData.orgName" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input readonly v-model="formData.projectName" />
        </el-form-item>
        <el-form-item label="项目负责人" prop="responsibleUserName">
          <el-input readonly v-model="formData.responsibleUserName" />
        </el-form-item>
        <el-form-item label="项目起止时间" prop="dateRange">
          <el-input readonly v-model="dateRange" />
        </el-form-item>
        <el-form-item label="项目描述" prop="projectDesc">
          <el-input readonly v-model="formData.projectDesc" :autosize="{ minRows: 4 }" type="textarea" />
        </el-form-item>
      </el-form>
    </div>
    <div class="modify-item">
      <h3 class="title">关联企业：</h3>
      <RelatedUnit :id="props.id" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import RelatedUnit from './relatedUnit.vue'
import $API from '~/common/api'

defineOptions({ name: 'ProjectMgrDetail' })

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const formData = ref({
  orgName: '',
  projectName: '',
  responsibleUserName: '',
  projectDesc: '',
  startDate: '',
  endDate: ''
})
const dateRange = computed(() => {
  return `${formData.value.startDate} ~ ${formData.value.endDate}`
})

const getBaseInfo = () => {
  $API
    .get({
      url: 'atomic-upms-service/org/project/detail',
      params: {
        projectId: props.id,
        methodName: 'detail'
      }
    })
    .then((res: any) => {
      const { code, data } = res
      if (+code === 200) {
        formData.value.orgName = data.orgName
        formData.value.projectName = data.projectName
        formData.value.responsibleUserName = data.responsibleUserName
        formData.value.projectDesc = data.projectDesc
        formData.value.startDate = data.startDate
        formData.value.endDate = data.endDate
      }
    })
}
getBaseInfo()
</script>

<style scoped lang="scss">
.aside-modify {
  @apply w-full;
  .modify-item {
    @apply mb-[30px] font-bold;
  }

  .title {
    @apply mb-[20px];
  }
}
</style>
