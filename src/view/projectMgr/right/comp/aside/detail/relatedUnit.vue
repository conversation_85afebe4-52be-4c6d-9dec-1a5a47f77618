<template>
  <div class="related-units">
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="relateOrgName" label="企业名称" align="center" show-overflow-tooltip />
      <el-table-column prop="personNum" label="成员数" align="center" how-overflow-tooltip />
      <el-table-column prop="sceneUserName" label="现场负责人" min-width="100px" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.sceneUserName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sceneUserContactNo"
        label="现场负责人手机号"
        min-width="150px"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span>{{ scope.row.sceneUserContactNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="responsibleUserName"
        label="企业负责人"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="responsibleUserContactNo"
        label="企业负责人手机号"
        min-width="150px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="constructScheme" label="施工方案" align="center" show-overflow-tooltip />
      <el-table-column prop="emergencyPlan" label="应急预案" align="center" show-overflow-tooltip />
      <el-table-column prop="securityProtocol" label="安全协议" align="center" show-overflow-tooltip />
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" plain size="small" @click="detailHandle(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-box">
      <el-pagination
        v-model:currentPage="pagi.pageNo"
        v-model:page-size="pagi.pageSize"
        :total="pagi.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="getTableData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '~/common/api'
import { useRouter } from 'vue-router'
import { treeSerivce } from '@/view/projectMgr/left/treeService'

defineOptions({ name: 'ProjectMgrModifyUnits' })

const router = useRouter()

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})
const tableData = ref([])

const getTableData = () => {
  $API
    .post({
      url: 'atomic-upms-service/org/project/queryProjectRelateList',
      data: {
        projectId: props.id,
        pageNo: pagi.value.pageNo,
        pageSize: pagi.value.pageSize
      }
    })
    .then((res: any) => {
      const { code, data } = res
      if (+code === 200) {
        tableData.value = data?.rows || []
        pagi.value.total = data?.total || 0
      }
    })
}

const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

const detailHandle = (row: any) => {
  const href = router.resolve({
    name: 'enterprise',
    query: { id: row.relateOrgCode, projId: row.projectId, orgCode: treeSerivce.curNode.value.orgCode }
  }).href

  window.open(href, '_blank')
}

getTableData()
</script>

<style scoped lang="scss">
.related-units {
  @apply w-full;
  .pagination-box {
    @apply my-[20px] flex justify-end;
  }
}
</style>
