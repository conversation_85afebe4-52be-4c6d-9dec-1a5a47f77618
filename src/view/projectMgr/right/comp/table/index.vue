<template>
  <div class="projectMgr-table">
    <el-table
      ref="multipleTableRef"
      :data="tableData"
      :height="tableHeight"
      stripe
      style="width: 100%"
      row-key="projectId"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" fixed width="45" /> -->
      <el-table-column prop="orgName" label="所属单位" align="center" min-width="110" show-overflow-tooltip />
      <el-table-column prop="projectNo" label="项目编号" align="center" min-width="110" show-overflow-tooltip />
      <el-table-column prop="projectName" label="项目名称" align="center" min-width="110" show-overflow-tooltip />
      <el-table-column
        prop="responsibleUserName"
        label="项目负责人"
        align="center"
        min-width="110"
        show-overflow-tooltip
      />
      <el-table-column prop="dateRange" label="项目起止日期" align="center" min-width="110" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.startDate }} ~ {{ scope.row.endDate }} </span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="项目状态" align="center" min-width="110">
        <template #default="scope">
          <el-tag v-if="scope.row.projectStatus == '1'" type="primary">{{ scope.row.projectStatusName }}</el-tag>
          <el-tag v-else-if="scope.row.projectStatus == '2'" type="success">{{ scope.row.projectStatusName }}</el-tag>
          <el-tag v-else-if="scope.row.projectStatus == '3'" type="danger">{{ scope.row.projectStatusName }}</el-tag>
          <el-tag v-else type="info">{{ scope.row.projectStatusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="projectUnitNum"
        label="项目涉及相关方企业数"
        align="center"
        width="110"
        show-overflow-tooltip
      />
      <el-table-column prop="projectUserNum" label="项目成员数" align="center" width="110" show-overflow-tooltip />
      <el-table-column label="操作" fixed="right" align="center" min-width="200">
        <template #default="scope">
          <el-button type="primary" plain size="small" @click="actionHandle(ACTION.DETAIL, scope.row)">详情</el-button>
          <el-button type="primary" plain size="small" @click="actionHandle(ACTION.APPROACH, scope.row)">
            项目进场管理
          </el-button>
          <el-button type="primary" plain size="small" @click="actionHandle(ACTION.RISK, scope.row)">
            项目过程管理
          </el-button>
          <el-button
            type="primary"
            plain
            size="small"
            :disabled="scope.row.projectStatus > 1"
            @click="actionHandle(ACTION.EDIT, scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            width="220"
            title="你确定要作废这个项目吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            cancel-button-type="default"
            @confirm="invalidatedHandle(scope.row)"
          >
            <template #reference>
              <el-button type="danger" plain size="small" :disabled="scope.row.projectStatus > 1">作废</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-box">
      <el-pagination
        v-model:currentPage="pagi.pageNo"
        v-model:page-size="pagi.pageSize"
        :total="pagi.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="getTableData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import mixTableHeight from '~/common/tableHeight.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ACTION } from '~/view/projectMgr/constant'
import $API from '~/common/api'
import { treeSerivce } from '@/view/projectMgr/left/treeService'

defineOptions({ name: 'projectMgrTableTable' })

let filterData: any = {} // 搜索条件

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const { tableHeight } = mixTableHeight({ subtractHeight: 364 })
const tableData = ref([])

const getTableData = () => {
  const params = {
    pageNo: pagi.value.pageNo,
    pageSize: pagi.value.pageSize,
    orgCode: treeSerivce.curNode.value.orgCode,
    ...filterData
  }
  $API
    .post({
      url: 'atomic-upms-service/org/project/list',
      data: params
    })
    .then((res: any) => {
      if (+res.code === 200) {
        tableData.value = res.data?.rows || []
        pagi.value.total = res.data?.total || 0
      }
    })
}

const resetTableData = (data: any = {}) => {
  filterData = Object.assign({}, data)
  handleSizeChange()
}

const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

const invalidatedHandle = (data: any) => {
  $API
    .get({
      url: 'atomic-upms-service/org/project/cancelProject',
      params: {
        projectId: data.projectId
      }
    })
    .then((res: any) => {
      if (+res.code === 200) {
        emits('action', {
          action: ACTION.UPDATESUCCESS
        })
      }
    })
}

const preDeleteList = ref([])
// const handleSelectionChange = (data) => {
//   preDeleteList.value = data
// }
const deleteHandle = async () => {
  const ids = preDeleteList.value.map((item: any) => item.projectId)
  const res: any = await $API.get({
    url: 'atomic-upms-service/org/project/batchDeleteProject',
    params: {
      projectIds: ids.join(',')
    }
  })
  if (+res.code === 200) {
    emits('action', {
      action: ACTION.UPDATESUCCESS
    })
  } else {
    ElMessage.error('删除失败')
  }
}
const deleteConfirm = () => {
  if (preDeleteList.value.length < 1) return
  ElMessageBox({
    title: '提示',
    message: '确认删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        deleteHandle()
        done()
      } else {
        done()
      }
    }
  })
}

watch(
  () => treeSerivce.curNode.value,
  () => {
    getTableData()
  }
)

const emits = defineEmits(['action'])

type TActions = keyof typeof ACTION
const actionHandle = (actionType: TActions, data) => {
  emits('action', {
    action: actionType,
    data
  })
}

defineExpose({
  resetTableData,
  getTableData,
  deleteConfirm
})
</script>

<style scoped lang="scss">
.projectMgr-table {
  display: grid;

  .pagination-box {
    @apply flex justify-end mt-[20px];
  }
}
</style>
