<template>
  <div class="projectMgr-list-wrapper">
    <el-scrollbar :height="tableHeight">
      <ul class="projectMgr-list" v-loading="loading" v-if="listData.length > 0">
        <li
          class="projectMgr-item"
          :class="`projectMgr-item_${item.projectStatus}`"
          v-for="item in listData"
          :key="item.projectId"
        >
          <div class="item-left-box">
            <BaseInfo :info="item" class="flex-1" />
            <div class="action-wrap">
              <ActionBtns
                v-if="+item.projectStatus !== 4"
                class="mr-[12px]"
                @action="(action: ACTION) => actionHandle(action, item)"
              />
              <el-button type="primary" @click="actionHandle(ACTION.ENTRYTRAININGSET, item)">{{
                ACTION_LABEL.ENTRYTRAININGSET
              }}</el-button>
              <el-button v-if="+item.projectStatus !== 4" type="warning" @click="outHandle(item.projectId)">{{
                ACTION_LABEL.OUT
              }}</el-button>
            </div>
          </div>
          <EntryAction @action="(action: ACTION) => actionHandle(action, item)" />
          <ProcessAction @action="(action: ACTION) => actionHandle(action, item)" />
        </li>
      </ul>
      <Empty v-else />
    </el-scrollbar>

    <div class="pagination-box">
      <el-pagination
        v-model:currentPage="pagi.pageNo"
        v-model:page-size="pagi.pageSize"
        :total="pagi.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="getTableData"
      />
    </div>

    <OutConfirm ref="outRef" :projectId="curId" @confirm="outConfirmHandle" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import BaseInfo from './BaseInfo.vue'
import ActionBtns from './ActionBtnList.vue'
import EntryAction from './ProjectEntry.vue'
import ProcessAction from './ProjectProcess.vue'
import OutConfirm from './OutConfirm.vue'
import Empty from '@/components/public/noData.vue'
import mixTableHeight from '~/common/tableHeight.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ACTION, ACTION_LABEL } from '~/view/projectMgr/constant'
import $API from '~/common/api'
import { treeSerivce } from '@/view/projectMgr/left/treeService'
import { ITableRow } from '@/view/projectMgr/right/type'

defineOptions({ name: 'projectMgrTableTable' })

let filterData: any = {} // 搜索条件

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const loading = ref(false)
const { tableHeight } = mixTableHeight({ subtractHeight: 364 })
const listData = ref<ITableRow[]>([])
const curId = ref('')

const getTableData = () => {
  const params = {
    pageNo: pagi.value.pageNo,
    pageSize: pagi.value.pageSize,
    orgCode: treeSerivce.curNode.value.orgCode,
    ...filterData
  }
  $API
    .post({
      url: 'atomic-upms-service/org/project/list',
      data: params
    })
    .then((res: any) => {
      if (+res.code === 200) {
        listData.value = res.data?.rows || []
        pagi.value.total = res.data?.total || 0
      }
    })
}

const resetTableData = (data: any = {}) => {
  filterData = Object.assign({}, data)
  handleSizeChange()
}

const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

// 批量删除
const preDeleteList = ref<ITableRow[]>([])
const deleteHandle = async () => {
  const ids = preDeleteList.value.map((item: any) => item.projectId)
  const res: any = await $API.get({
    url: 'atomic-upms-service/org/project/batchDeleteProject',
    params: {
      projectIds: ids.join(',')
    }
  })
  if (+res.code === 200) {
    ElMessage.success('删除成功')
    emits('action', {
      action: ACTION.UPDATESUCCESS
    })
  }
}
const deleteConfirm = () => {
  if (preDeleteList.value.length < 1) return
  ElMessageBox({
    title: '提示',
    message: '确定要删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        deleteHandle()
        done()
      } else {
        done()
      }
    }
  })
}

// 退场
const outRef = ref()
const outHandle = async (projectId: string) => {
  curId.value = projectId
  outRef.value?.toggleDialog()
}
const outConfirmHandle = () => {
  emits('action', {
    action: ACTION.UPDATESUCCESS
  })
}

watch(
  () => treeSerivce.curNode.value,
  () => {
    getTableData()
  }
)

const emits = defineEmits(['action'])

type TActions = keyof typeof ACTION
const actionHandle = (actionType: TActions, data) => {
  if (actionType === ACTION.DELETE) {
    preDeleteList.value = [data]
    deleteConfirm()
  } else {
    emits('action', {
      action: actionType,
      data
    })
  }
}

defineExpose({
  resetTableData,
  getTableData
})
</script>

<style scoped lang="scss">
.projectMgr-list-wrapper {
  overflow: hidden;
  display: grid;
  .pagination-box {
    @apply flex justify-end mt-[20px];
  }
}

.projectMgr-list {
  padding-bottom: 20px;
}

.projectMgr-item {
  @apply relative py-[15px] pl-[25px] pr-[84px] mb-[16px] text-[14px] text-[#222] flex flex-row flex-nowrap items-center gap-[20px];
  line-height: 36px;
  background: linear-gradient(204deg, #e3eaff 0%, #fafbff 62%);
  border-radius: 6px;
  border: 1px solid #fff;
  &::before {
    @apply absolute top-0 right-0 w-[80px] h-[70px];
    content: '';
  }
  &::after {
    @apply absolute bottom-[15px] right-0 w-[84px] h-[84px];
    content: '';
    background: url('@/assets/projectMgr/card-bg.png') no-repeat 0 0;
    background-size: 100% 100%;
  }

  &.projectMgr-item_0::before {
    background: url('@/assets/projectMgr/status_1.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  &.projectMgr-item_1::before {
    background: url('@/assets/projectMgr/status_2.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  &.projectMgr-item_4::before {
    background: url('@/assets/projectMgr/status_3.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
}

.item-left-box {
  @apply flex-1 flex flex-col justify-between;
  min-width: 0;
}

.action-wrap {
  @apply flex flex-row items-center;
}
</style>
