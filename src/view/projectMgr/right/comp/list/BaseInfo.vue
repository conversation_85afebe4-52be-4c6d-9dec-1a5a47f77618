<template>
  <div class="project-info">
    <div class="info-header">
      <span class="label">项目名称：</span>
      <span class="value">{{ info.projectName || '--' }}</span>
    </div>
    <div class="info-body">
      <el-row :gutter="16">
        <el-col :span="14">
          <div class="info-item">
            <span class="label">项目所属单位：</span>
            <!-- <el-tooltip
              popper-class="max-w-[400px]"
              effect="dark"
              :content="info.orgName"
              placement="bottom-start"
            >
              <span class="value">{{ info.orgName }}</span>
            </el-tooltip> -->
            <span class="value">{{ info.orgName }}</span>
          </div>
        </el-col>
        <el-col :span="10">
          <div class="info-item">
            <span class="label">项目类型：</span>
            <span class="value">{{ info.xmlxMc }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div class="info-item">
            <span class="label">项目负责人：</span>
            <el-tooltip
              popper-class="max-w-[400px]"
              effect="dark"
              :content="info.responsibleUserName"
              placement="bottom-start"
            >
              <span class="value !flex-none">{{ info.responsibleUserName }}</span>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="14">
          <div class="info-item">
            <span class="label">项目起止日期：</span>
            <span class="value">{{ info.startDate }} ~ {{ info.endDate }}</span>
          </div>
        </el-col>
        <el-col :span="10">
          <div class="info-item">
            <span class="label">项目成员数：</span>
            <span class="value">{{ info.projectUserNum }}人</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ITableRow } from '@/view/projectMgr/right/type'

defineOptions({ name: 'ProjectMgrBaseInfo' })

const props = withDefaults(
  defineProps<{
    info: Partial<ITableRow>
  }>(),
  {
    info: () => ({})
  }
)
</script>

<style scoped lang="scss">
.project-info {
  .info-header {
    @apply text-[16px];
    line-height: 36px;
    font-weight: 700;
  }

  :deep(.info-body .el-row) {
    margin-bottom: 0;
  }

  .info-item {
    @apply flex flex-row flex-nowrap;
    white-space: nowrap;
    .value {
      @apply flex-1 truncate min-w-[130px];
    }
  }
}
</style>
