<template>
  <el-dialog v-model="dialogVisible" title="项目完工" width="650">
    <el-form ref="formRef" label-width="auto" :model="formData" :rules="formRules">
      <el-form-item label="退场结论" prop="sgtcjl">
        <el-select v-model="formData.sgtcjl" placeholder="请选择退场结论" clearable>
          <el-option v-for="item in sgtcjlOpt" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="退场说明" prop="tcsm">
        <el-input
          v-model="formData.tcsm"
          placeholder="请输入退场说明"
          style="width: 100%"
          :autosize="{ minRows: 2, maxRows: 4 }"
          type="textarea"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="验收标准" prop="ysbzFj">
        <el-input v-model="formData.ysbzFj" v-show="false"></el-input>
        <file-uploader
          ref="fileRef"
          v-model="ysbzFileList"
          :accept="accept"
          :maxCount="3"
          @change="handleFileChange($event)"
          @uploading="updateUploadingStatus"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelHandle">取消</el-button>
        <el-button type="primary" :loading="isSubmitting" @click="confirmHandle">{{
          isSubmitting ? '提交中...' : '确定'
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import $API from '~/common/api'

defineOptions({ name: 'ProjectMgrOut' })

const props = defineProps({
  projectId: {
    type: String,
    default: ''
  }
})

const dialogVisible = ref(false)
const toggleDialog = (visible = true) => {
  dialogVisible.value = visible
}

const formRef = ref()

const initForm = () => ({
  sgtcjl: '',
  tcsm: '',
  ysbzFj: ''
})
const formData = ref(initForm())
const formRules = {
  sgtcjl: [{ required: true, message: '请选择退场结论', trigger: ['blur', 'change'] }]
  // ysbzFj: [{ required: true, message: '请上传验收标准文件', trigger: ['blur', 'change'] }]
}

const isSubmitting = ref(false)

const sgtcjlOpt = ref<
  Array<{
    dictLabel: string
    dictValue: string
    [prop: string]: any
  }>
>([])
const getDict = () => {
  $API
    .get({
      url: 'atomic-upms-service//common/v1/queryDictList',
      params: {
        type: 'xmgl_sgtcjl'
      }
    })
    .then((res: any) => {
      if (+res.code === 200) {
        sgtcjlOpt.value = res.data
      }
    })
}
getDict()

const ysbzFileList = ref([])
const accept = '.pdf,.doc,.docx'
// 更新上传状态
const isUploading = ref(false)
const updateUploadingStatus = (status: boolean) => {
  isUploading.value = status
}
// 修改文件变化处理函数
const handleFileChange = (files: any[]) => {
  if (files.length < 1) return
  const fileIds: any[] = []
  files.forEach((file: any) => {
    if (file.status === 'success') {
      const _id = file.response.data.id
      if (!fileIds.includes(_id)) {
        fileIds.push(_id)
      }
    }
  })
  formData.value.ysbzFj = fileIds.join(',')
}

// submit
const emits = defineEmits(['confirm'])
const cancelHandle = () => {
  formRef.value.resetFields()
  ysbzFileList.value = []
  toggleDialog(false)
}
const confirmHandle = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      const params = JSON.parse(JSON.stringify(formData.value))
      isSubmitting.value = true
      $API
        .post({
          url: 'atomic-upms-service/org/project/projTc',
          data: {
            projectId: props.projectId,
            ...params
          }
        })
        .then((res: any) => {
          if (+res.code === 200) {
            ElMessage.success('退场成功')
            cancelHandle()
            emits('confirm')
          }
        })
        .finally(() => {
          isSubmitting.value = false
        })
    }
  })
}

defineExpose({
  toggleDialog
})
</script>

<style scoped lang="scss"></style>
