<template>
  <ul class="projectMgr-bar">
    <li v-for="item in list" :key="item.name" :class="['bar-item', `bar-item_${item.type}`]">
      <span class="name">{{ item.name }}</span>
      <span class="value">{{ item.value }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import { treeSerivce } from '@/view/projectMgr/left/treeService'

defineOptions({ name: 'projectMgrDashBar' })

const list = ref([
  {
    type: 1,
    name: '待开始项目',
    value: 0
  },
  {
    type: 2,
    name: '进行中项目',
    value: 0
  },
  {
    type: 3,
    name: '已退场项目',
    value: 0
  }
  // {
  //   type: 4,
  //   name: '已作废项目',
  //   value: 0
  // }
])

const getData = () => {
  $API
    .get({
      url: 'atomic-upms-service/org/project/statisticsProjectCount',
      params: {
        orgCode: treeSerivce.curNode.value.orgCode
      }
    })
    .then((res: any) => {
      const { code, data } = res
      if (+code === 200) {
        list.value[0].value = data.toStartProjectNum || 0
        list.value[1].value = data.processingProjectNum || 0
        list.value[2].value = data.ytcNum || 0
        // list.value[3].value = data.cancelProjectNum || 0
      }
    })
}

watch(
  () => treeSerivce.curNode.value,
  () => {
    getData()
  }
)

defineExpose({
  getData
})
</script>

<style scoped lang="scss">
.projectMgr-bar {
  @apply flex flex-row flex-nowrap justify-center w-full mb-[20px];

  .bar-item {
    @apply relative flex-1 flex flex-col text-[14px] text-[#333] py-[15px] pl-[86px] pr-[20px] mr-[40px];
    line-height: 20px;
    max-width: 218px;
    background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
    box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
    border-radius: 8px;
    border: 1px solid #e2e8f3;
    &:last-of-type {
      @apply mr-0;
    }

    &::before {
      @apply absolute top-[6px] left-[10px];
      content: '';
      content: '';
      width: 66px;
      height: 66px;
    }

    .value {
      font-size: 20px;
      margin-top: 10px;
    }
  }
  .bar-item_1 {
    &::before {
      background: url('@/assets/projectMgr/icon_1.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #fd8410;
    }
  }
  .bar-item_2 {
    &::before {
      background: url('@/assets/projectMgr/icon_2.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #527cff;
    }
  }
  .bar-item_3 {
    &::before {
      background: url('@/assets/projectMgr/icon_3.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #737373;
    }
  }
  // .bar-item_4 {
  //   &::before {
  //     background: url('@/assets/projectMgr/icon_4.png') no-repeat 0 0;
  //     background-size: 100% 100%;
  //   }
  //   .value {
  //     color: #fd8410;
  //   }
  // }
}
</style>
