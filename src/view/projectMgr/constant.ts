export const enum PROVIDE_KEY {
  currentAction = 'currentAction'
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  DETAIL = 'DETAIL',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  UPDATESUCCESS = 'UPDATESUCCESS',
  INVALIDATED = 'INVALIDATED',
  ENTRYTRAININGSET = 'ENTRYTRAININGSET',
  OUT = 'OUT',
  ENTRYTRAININGTRACK = 'ENTRYTRAININGTRACK',
  PEOPLEMGR = 'PEOPLEMGR',
  RISK = 'RISK',
  DANGERWORK = 'DANGERWORK'
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '查询',
  [ACTION.DETAIL]: '项目详情',
  [ACTION.ADD]: '新增项目',
  [ACTION.EDIT]: '编辑项目',
  [ACTION.DELETE]: '删除',
  [ACTION.UPDATESUCCESS]: '更新成功',
  [ACTION.INVALIDATED]: '作废',
  [ACTION.ENTRYTRAININGSET]: '培训课程设定',
  [ACTION.OUT]: '施工退场',
  [ACTION.ENTRYTRAININGTRACK]: '进场培训跟踪',
  [ACTION.PEOPLEMGR]: '人员管理',
  [ACTION.RISK]: '隐患排查治理',
  [ACTION.DANGERWORK]: '安全作业'
}

export interface IActionData {
  action: ACTION
  data: Record<string, any>
}
