<template>
  <div class="projectMgr">
    <LeftTree v-if="userInfo.unitOrgType !== '1'" />
    <RightComp class="projectMgr-main" />
  </div>
</template>

<script setup lang="ts">
import { nextTick } from 'vue'
import LeftTree from './left/leftTree.vue'
import { treeSerivce } from './left/treeService'
import RightComp from './right/index.vue'
import { useUserInfo } from '~/store'

defineOptions({ name: 'projectMgr' })

const userInfo: any = useUserInfo()

nextTick(() => {
  if (userInfo.value.unitOrgType != '1') {
    treeSerivce.curNode.value = {
      orgCode: userInfo.value.orgCode,
      orgName: userInfo.value.orgName
    }
  } else {
    treeSerivce.curNode.value = {
      orgCode: userInfo.value.unitId,
      orgName: userInfo.value.unitName
    }
  }
})
</script>

<style scoped lang="scss">
.projectMgr {
  @apply w-full flex-1 flex;
  height: calc(100vh - 115px);
  flex-flow: row nowrap;
  align-items: flex-start;
}

.projectMgr-main {
  @apply h-full flex-1 bg-[#fff] p-[15px];
  width: 100%;
  overflow: hidden;
}

.ifm-child {
  .projectMgr {
    height: 100% !important;
    .projectMgr-main {
      @apply flex flex-col;
    }

    :deep(.projectMgr-list-wrapper) {
      @apply flex flex-col h-full;
      .el-scrollbar {
        @apply flex-1 h-0;
        .el-scrollbar__wrap {
          height: 100% !important;
        }
      }
    }
  }
}
</style>
