<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-09 15:37:55
 * @FilePath: /ehs-partner-mgr/src/view/login.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <login></login>
</template>

<script lang="ts">
import login from '@/components/mainPage/login.vue'

export default {
  name: 'login-page',

  components: {
    login,
  },
}
</script>
