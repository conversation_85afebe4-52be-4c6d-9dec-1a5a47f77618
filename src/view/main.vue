<template>
  <div class="main">
    <div class="common-layout">
      <el-container>
        <el-aside width="200px">
          <sideMenu></sideMenu>
        </el-aside>
        <el-container>
          <el-header>
            <headerComponent></headerComponent>
          </el-header>
          <el-main>
            <router-view> </router-view>
          </el-main>
        </el-container>
      </el-container>
    </div>
  </div>
</template>

<script setup lang="ts">
import sideMenu from '@/layout/sideMenu/index.vue'
import headerComponent from '@/layout/headerComponent.vue'

// console.log('🚀 ~ sideMenu:', sideMenu)

// import ''
</script>

<style scoped></style>
