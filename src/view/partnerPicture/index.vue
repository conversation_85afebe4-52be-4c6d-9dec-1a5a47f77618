<template>
  <div class="custom-main">
    <Header></Header>
    <main class="custom-body">
      <div><Left></Left></div>
      <div><Center></Center></div>
      <div><Right></Right></div>
    </main>
  </div>
</template>

<script setup lang="ts">
import Header from './header/index.vue'
import Left from '~/view/partnerPicture/comp/Left.vue'
import Center from '~/view/partnerPicture/comp/Center.vue'
import Right from '~/view/partnerPicture/comp/Right.vue'

defineOptions({ name: 'PartnerPicture' })
</script>

<style lang="scss" scoped>
// 左边‘企业信息跟进’高度
:deep(.el-scrollbar__view) {
  height: calc(100% - 65px);
}

.custom-main {
  background-color: #061425 !important;
  height: 100%;
}

.custom-body {
  color: #fff;
  height: calc(100% - 105px);
  display: grid;
  grid-template-columns: 450px 1fr 450px;
}
</style>
