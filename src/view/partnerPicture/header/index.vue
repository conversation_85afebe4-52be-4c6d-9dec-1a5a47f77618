<template>
  <header class="com-header">
    <div class="header-content">
      <div class="area">
        <!--  left  -->
        <div class="area-l">
          <WeatherComp type="left"></WeatherComp>
        </div>
        <div class="center_title">
          <!--          <img src="./assets/title.png" alt="" />-->
          {{ projectTitle }}
        </div>
        <!--  right  -->
        <div class="area-r">
          <!-- <div class="area-r-weather-comp" v-if="uploadType === 'oss'">
            <img
              @click="openGis"
              @mouseover="handleMouseover"
              @mouseout="handleMouseout"
              class="mr-[18px]"
              :src="gisImg"
              alt=""
            />
          </div> -->
          <div class="right">
            <el-cascader
              v-model="unitForm"
              :options="options"
              :show-all-levels="false"
              popper-class="myClass"
              :props="{ label: 'text', value: 'id' }"
              style="width: 220px"
              @change="selectChange"
            />
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import config from '~/config'
import { useUserInfo } from '~/store'
import gisS from './assets/gis-s.png'
import gis from './assets/gis.png'
import WeatherComp from './comp/Weather/index.vue'

const ui = useUserInfo()
const gisImg = ref(gis)
const unitForm = ref('')
const projectTitle = ref('')
const options = ref([ui.value.unitId])

function getId(arr) {
  if (!arr[0].children.length) {
    return arr[0].id
  } else {
    return getId(arr[0].children)
  }
}
// 获取目录树
function getTree() {
  const params = {
    unitId: ui.value.unitId,
    needCheckJgdw: '1',
    type: '2'
  }
  $API
    .get({
      url: 'edu-inter-server/common/getOrgTree',
      params
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        options.value = res.data
        unitForm.value = getId(res.data)
        ui.value.misUnitId = getId(res.data)
      }
    })
}

function selectChange(val) {
  if (val && val.length) {
    ui.value.misUnitId = val[val.length - 1]
  } else {
    ui.value.misUnitId = ui.value.unitId
  }
  console.log('ui', ui.value.misUnitId)
}
function handleMouseover() {
  gisImg.value = gisS
}

function openGis() {
  window.open(config.openGisUrl)
}

function handleMouseout() {
  gisImg.value = gis
}

onMounted(() => {
  projectTitle.value = ui.value.zhLogo === 'yanchang' ? '承包商管理一张图' : '相关方管理一张图'
  getTree()
})

defineOptions({ name: 'MisHeaderComp' })
</script>

<style scoped lang="scss">
.header-content {
  position: relative;
  overflow: hidden;
  background: url('@/assets/screen/_header.png') center center no-repeat;
  background-size: cover;
  width: 100%;
  height: 105px;
}

.center_title {
  position: absolute;
  left: 50%;
  top: 35%;
  transform: translate(-50%, -50%);
  letter-spacing: 6px;
  font-family: youshe, serif;
  font-weight: bold;
  font-size: 40px;
  color: #ffffff;
  background: linear-gradient(0deg, #31beff 0%, #81c5ff 0%, #effcfe 75.3173828125%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  padding-left: 18px;
  position: absolute;
  width: 584px;
  height: 52px;
  background: url('./assets/area-l.png') center center no-repeat;
  background-size: cover;
  left: 9px;
  top: 19px;
}

.right {
  transform: scaleX(-1);
  margin-left: 30px;
}

.area-r {
  position: absolute;
  display: flex;
  justify-content: start;
  align-items: center;
  width: 584px;
  height: 52px;
  background: url('./assets/area-l.png') center center no-repeat;
  background-size: cover;
  transform: scaleX(-1);
  right: 0;
  top: 19px;
}

.title {
  color: #fff;
}

.area-r-weather-comp {
  margin-left: 10px;
  display: flex;
  transform: scaleX(-1);

  img {
    cursor: pointer;
  }
}

:deep(.el-cascader .el-input .el-input__wrapper) {
  background-color: #04152a !important;
  box-shadow: 0 0 0 1px #066dc4;
}

:deep(.el-cascader .el-input .el-input__inner) {
  color: #8aa2c5;
}

:deep(.el-cascader-node:not(.is-disabled):hover) {
  background: yellow !important;
}
</style>

<style lang="scss">
.myClass {
  background: #04152a !important;
  border: 1px solid #066dc4 !important;

  .el-cascader-node__label {
    color: #fff !important;
  }

  .el-cascader-node:not(.is-disabled):hover {
    background: #066dc4 !important;
  }

  .el-cascader-node:not(.is-disabled):focus {
    background: #066dc4 !important;
  }

  .el-cascader-node__postfix {
    color: #fff;
  }

  .el-popper__arrow:before {
    background: #04152a !important;
    border: 1px solid #066dc4 !important;
  }
}
</style>
