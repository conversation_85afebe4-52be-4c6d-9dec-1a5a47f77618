<template>
  <div class="left-main pl-[10px] pr-[10px] h-full">
    <div class="h-[74px] left-main-1">
      <div>
        <Top />
      </div>
    </div>
    <div class="h-[506px] left-main-2">
      <div>
        <HeadTileB :title="ui.zhLogo === 'yanchang' ? '最近承包商作业情况' : '最近相关方作业情况'"></HeadTileB>
      </div>
      <div>
        <Main />
      </div>
    </div>
    <div class="w-full left-main-3">
      <div>
        <HeadTileB title="在线实时监管"></HeadTileB>
      </div>
      <div>
        <Bottom />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineComponent } from 'vue'
import HeadTileB from '~/components/HeadTitle/HeadTileB2.vue'
import Top from '../components/Center/Top/index.vue'
import Main from '../components/Center/Main/index.vue'
import Bottom from '../components/Center/Bottom/index.vue'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
defineComponent({ name: 'PartnerPictureLeft' })
</script>

<style scoped lang="scss">
.left-main {
  display: grid;
  // margin-top: 10px;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-1 {
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
  // margin-top: -18px;
}

.fwxgf {
  background: url('./assets/fwxgf.png') center center no-repeat;
  background-size: cover;
}

.tyxgf {
  background: url('./assets/tyxgf.png') center center no-repeat;
  background-size: cover;
}

.zyzs {
  background: url('./assets/zyzs.png') center center no-repeat;
  background-size: cover;
}

.hmdry {
  background: url('./assets/hmdry.png') center center no-repeat;
  background-size: cover;
}

.title-1 {
  padding-left: 10px;
  margin-top: 22px;
  text-align: center;
  font-family:
    PingFangSC,
    PingFang SC,
    serif;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  line-height: 25px;
  font-style: normal;
}

.title-2 {
  font-family: DINAlternate, DINAlternate, serif;
  font-weight: bold;
  font-size: 40px;
  color: #00fdff;
  line-height: 47px;
  font-style: normal;
  padding-left: 10px;
  margin-top: 20px;
  text-align: center;
}

.left-main-2 {
  margin-top: 18px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-3 {
  margin-top: 18px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}
</style>
