<template>
  <div class="left-main h-full">
    <div class="h-[294.5px] left-main-1">
      <div>
        <HeadTileB :title="ui.zhLogo === 'yanchang' ? '承包商作业类型占比' : '相关方作业类型占比'"></HeadTileB>
      </div>
      <div class="left_2 relative">
        <Pie3D :extra="extra" :chartData="chartData" />
      </div>
    </div>
    <div class="h-[294.5px] left-main-2">
      <div>
        <HeadTileB :title="ui.zhLogo === 'yanchang' ? '承包商作业合规率' : '相关方作业合规率'"></HeadTileB>
      </div>
      <div class="left_2 relative">
        <HorizontalColumnChart
          :echartsData="echartsData"
          :graphic-color="['rgba(43, 97, 223, 1)', 'rgba(39, 150, 253, 1)']"
        />
      </div>
    </div>
    <div class="w-full left-main-3">
      <div>
        <HeadTileB :title="ui.zhLogo === 'yanchang' ? '承包商员工违规前五名' : '相关方员工违规前五名'"></HeadTileB>
      </div>
      <div>
        <Bottom />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineComponent, ref, onMounted, watch } from 'vue'
import HeadTileB from '~/components/HeadTitle/HeadTileB.vue'
import Bottom from '../components/Right/Bottom/index.vue'
import HorizontalColumnChart from '~/components/charts/horizontalColumnChart.vue'
import Pie3D from '~/components/charts/Pie3D.vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const echartsData = ref<any>([])
const extra = {
  labelLine: true,
  color: ['#2BD9ED', '#0073D9', '#A1D0EF', '#FFD16A', '#FF8C3E'],
  unit: '%',
  opacity: 1,
  legend: {
    right: '5%'
  }
}

const chartData = ref<any>([])

function getMisRight() {
  chartData.value = []
  $API
    .get({
      url: 'edu-inter-server/mis/v1/workTypeRate',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        res.data.forEach((item) => {
          chartData.value.push({
            value: +item.value.replace('%', ''),
            name: item.name
          })
        })
      }
    })
}

function getUnitComplian() {
  echartsData.value = []
  $API
    .post({
      url: 'edu-inter-server/mis/v1/UnitComplianceTop',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        res.data.forEach((item) => {
          echartsData.value.push({ value: item.complianceRate, name: item.unitName })
        })
        console.log('echartsData.value.', echartsData.value)
      }
    })
}
watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getMisRight()
      getUnitComplian()
    }
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  getMisRight()
  getUnitComplian()
})
defineComponent({ name: 'PartnerPictureLeft' })
</script>

<style scoped lang="scss">
.left-main {
  margin-right: 9px;
  display: grid;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-1 {
  margin-top: 18px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
  margin-top: -18px;
}

.fwxgf {
  background: url('./assets/fwxgf.png') center center no-repeat;
  background-size: cover;
}

.tyxgf {
  background: url('./assets/tyxgf.png') center center no-repeat;
  background-size: cover;
}

.zyzs {
  background: url('./assets/zyzs.png') center center no-repeat;
  background-size: cover;
}

.hmdry {
  background: url('./assets/hmdry.png') center center no-repeat;
  background-size: cover;
}

.title-1 {
  padding-left: 10px;
  margin-top: 22px;
  text-align: center;
  font-family:
    PingFangSC,
    PingFang SC,
    serif;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  line-height: 25px;
  font-style: normal;
}

.title-2 {
  font-family: DINAlternate, DINAlternate, serif;
  font-weight: bold;
  font-size: 40px;
  color: #00fdff;
  line-height: 47px;
  font-style: normal;
  padding-left: 10px;
  margin-top: 20px;
  text-align: center;
}

.left-main-2 {
  margin-top: 18px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-3 {
  margin-top: 25px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left_2 {
  // width: 95%;

  // margin-left: 20px;
  background: url('./assets/left_2.png') center center no-repeat;
  background-size: cover;
}
</style>
