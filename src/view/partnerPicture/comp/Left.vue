<template>
  <div class="left-main h-full">
    <div class="h-[308px] left-main-1">
      <div class="fwxgf w-[202px] h-[133px]">
        <div class="title-1">{{ ui.zhLogo === 'yanchang' ? '服务承包商' : '服务相关方' }}</div>
        <div class="title-2">{{ leftInfo.serviceRelatedNum }}</div>
      </div>
      <div class="tyxgf w-[202px] h-[133px]">
        <div class="title-1">{{ ui.zhLogo === 'yanchang' ? '停用承包商' : '停用相关方' }}</div>
        <div class="title-2">{{ leftInfo.stopRelatedNum }}</div>
      </div>
      <div class="zyzs w-[202px] h-[133px]">
        <div class="title-1">作业总数</div>
        <div class="title-2">{{ leftInfo.workCount }}</div>
      </div>
      <div class="hmdry w-[202px] h-[133px]">
        <div class="title-1">黑名单人员</div>
        <div class="title-2">{{ leftInfo.blackListNum }}</div>
      </div>
    </div>
    <div class="h-[314px] left-main-2">
      <div>
        <HeadTileB :title="ui.zhLogo === 'yanchang' ? '承包商企业类型占比' : '相关方企业类型占比'"></HeadTileB>
      </div>
      <div class="left_2 relative">
        <Pie3D :extra="extra" :chartData="chartData" />
      </div>
    </div>
    <div class="w-full left-main-3">
      <div>
        <HeadTileB title="入场培训通过率"></HeadTileB>
      </div>
      <div>
        <Bottom />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue'
import HeadTileB from '~/components/HeadTitle/HeadTileB.vue'
import Bottom from '../components/Right/Bottom/index2.vue'
import Pie3D from '~/components/charts/Pie3D.vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

interface IData {
  serviceRelatedNum: number
  stopRelatedNum: number
  workCount: number
  blackListNum: number
}
const ui = useUserInfo()
const leftInfo = ref<IData>({
  serviceRelatedNum: 0,
  stopRelatedNum: 0,
  workCount: 0,
  blackListNum: 0
})
const extra = {
  labelLine: true,
  color: ['#2BD9ED', '#0073D9', '#A1D0EF', '#FFD16A', '#FF8C3E'],
  unit: '%',
  opacity: 1
}

const chartData = ref<any>([])

function getRelatedRate() {
  chartData.value = []
  $API
    .get({
      url: 'edu-inter-server/mis/v1/relatedTypeRate',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        res.data.forEach((item) => {
          chartData.value.push({
            value: +item.value.replace('%', ''),
            name: item.name
          })
        })
      }
    })
}

function getMisInfo() {
  $API
    .get({
      url: 'edu-inter-server/mis/v1/misLeft',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        leftInfo.value = res.data
      }
    })
}

watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getMisInfo()
      getRelatedRate()
    }
  },
  { immediate: true, deep: true }
)
onMounted(() => {
  getMisInfo()
  getRelatedRate()
})

defineComponent({ name: 'PartnerPictureLeft' })
</script>

<style scoped lang="scss">
.left-main {
  margin-left: 9px;
  display: grid;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-1 {
  margin-top: -18px;
  padding-bottom: 32px;
  padding-left: 10px;
  padding-right: 10px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 8px 0; //行间距 列间距
  height: 100%;
  background: url('./assets/left_1.png') center center no-repeat;
  background-size: cover;
}

.fwxgf {
  background: url('./assets/fwxgf.png') center center no-repeat;
  background-size: cover;
}

.tyxgf {
  background: url('./assets/tyxgf.png') center center no-repeat;
  background-size: cover;
}

.zyzs {
  background: url('./assets/zyzs.png') center center no-repeat;
  background-size: cover;
}

.hmdry {
  background: url('./assets/hmdry.png') center center no-repeat;
  background-size: cover;
}

.title-1 {
  padding-left: 10px;
  margin-top: 22px;
  text-align: center;
  font-family:
    PingFangSC,
    PingFang SC,
    serif;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  line-height: 25px;
  font-style: normal;
}

.title-2 {
  font-family: DINAlternate, DINAlternate, serif;
  font-weight: bold;
  font-size: 40px;
  color: #00fdff;
  line-height: 47px;
  font-style: normal;
  padding-left: 10px;
  margin-top: 20px;
  text-align: center;
}

.left-main-2 {
  margin-top: -5px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left-main-3 {
  margin-top: 18px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.left_2 {
  background: url('./assets/left_2.png') center center no-repeat;
  background-size: cover;
}
</style>
