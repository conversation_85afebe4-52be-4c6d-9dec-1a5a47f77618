<template>
  <div class="w-full h-full">
    <div class="content">
      <div class="gao">
        <div
          class="list_content"
          v-for="(item, index) in arr"
          :key="item.id"
          :class="{ even: index % 2 == 0 }"
        >
          <li>
            <img v-show="index + 1 == 1" src="../../image2/righticon1.png" />
            <img v-show="index + 1 == 2" src="../../image2/righticon2.png" />
            <img v-show="index + 1 == 3" src="../../image2/righticon3.png" />
            <img v-show="index + 1 > 3" src="../../image2/righticon.png" />
            <span>{{ index + 1 }}</span>
          </li>
          <li>{{ item.name }}</li>
          <li></li>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
const arr = ref([
  {
    id: 1,
    name: '智慧云启科技有限公司',
    type: '70%',
    num: 5
  },
  {
    id: 2,
    name: '绿动未来环保有限公司',
    type: '40%',
    num: 6
  },
  {
    id: 3,
    name: '智汇融通信息科技有限公司',
    type: '34%',
    num: 6
  },
  {
    id: 3,
    name: '智汇融通信息科技有限公司',
    type: '34%',
    num: 6
  },
  {
    id: 3,
    name: '智汇融通信息科技有限公司',
    type: '34%',
    num: 6
  },
  {
    id: 3,
    name: '智汇融通信息科技有限公司',
    type: '34%',
    num: 6
  },
  {
    id: 3,
    name: '智汇融通信息科技有限公司',
    type: '34%',
    num: 6
  }
])
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
defineOptions({ name: 'HeadTileB' })
</script>

<style scoped lang="scss">
.content {
  width: 95%;
  margin: 0 auto;
  height: 272px;
  background-image: url('../../image2/juxing.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .gao {
    width: 100%;

    height: 254px;

    overflow: auto;
    font-size: 12px;
    .list_content {
      display: flex;
      justify-content: space-between;
      text-align: center;
      list-style: none;
      height: 36px;
      line-height: 36px;
      li:nth-child(1) {
        width: 10%;

        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        position: relative;
        img {
          display: inline-block;
        }
        span {
          position: absolute;
          left: 19px;
          bottom: 2px;
          font-size: 10px;
        }
      }
      li:nth-child(2) {
        width: 30%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      li:nth-child(3) {
        width: 20%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      li:nth-child(4) {
        width: 15%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
