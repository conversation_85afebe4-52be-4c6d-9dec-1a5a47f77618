<template>
  <div calss="w-full h-full">
    <div class="content">
      <div class="list">
        <li>排行</li>
        <li>姓名</li>
        <li>作业次数</li>
        <li>违规率</li>
      </div>

      <div class="gao pb-[10px]">
        <el-scrollbar :height="customElScrollbarHeight + 'px'">
          <div v-if="arr.length">
            <div class="list_content" v-for="(item, index) in arr" :key="item.id" :class="{ even: index % 2 == 0 }">
              <li>
                <img v-show="index + 1 == 1" src="../../image2/righticon1.png" />
                <img v-show="index + 1 == 2" src="../../image2/righticon2.png" />
                <img v-show="index + 1 == 3" src="../../image2/righticon3.png" />
                <img v-show="index + 1 > 3" src="../../image2/righticon.png" />
                <span>{{ index + 1 }}</span>
              </li>
              <li>{{ item.name }}</li>
              <li>{{ item.num }}</li>
              <li>{{ item.type }}</li>
            </div>
          </div>
          <div v-else class="text-center leading-[100px]">暂无数据</div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useWatchWindowSizeHeight } from '~/hooks/windowSizeHooks'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const arr = ref<any>([])

const customElScrollbarHeight = ref(91)
useWatchWindowSizeHeight((val: any) => {
  if (val > 1000) {
    customElScrollbarHeight.value = 215
  } else {
    customElScrollbarHeight.value = 91
  }
})

function getUserComplian() {
  arr.value = []
  $API
    .post({
      url: 'edu-inter-server/mis/v1/userComplianceTop',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId },
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        res.data.forEach((item, index) => {
          arr.value.push({
            id: index + 1,
            type: item.complianceRate + '%',
            num: item.complianceNum,
            name: item.userName,
          })
        })
      }
    })
}

watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getUserComplian()
    }
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  getUserComplian()
})
</script>

<style scoped lang="scss">
.content {
  width: 95%;
  margin-left: 16px;
  // height: 160px;
  // padding-top: 15px;
  // box-sizing: border-box;
  background-image: url('../../image2/juxing.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .list {
    width: 95%;
    margin: 0 auto;

    list-style: none;
    height: 30px;
    line-height: 30px;
    background: linear-gradient(180deg, #0b63ae 0%, #022e5b 100%);
    opacity: 0.55;

    // margin-top: 5px;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    text-align: center;

    li:nth-child(1) {
      width: 10%;
    }

    li:nth-child(2) {
      width: 30%;
    }

    li:nth-child(3) {
      width: 20%;
    }

    li:nth-child(4) {
      width: 15%;
    }

    li:nth-child(5) {
      width: 25%;
    }
  }

  .gao {
    width: 95%;
    margin: 0 auto;
    // height: 19vh;

    overflow: auto;
    font-size: 12px;

    .list_content {
      display: flex;
      justify-content: space-between;
      text-align: center;
      list-style: none;
      height: 34px;
      line-height: 34px;

      li:nth-child(1) {
        width: 10%;

        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        position: relative;

        img {
          display: inline-block;
        }

        span {
          position: absolute;
          left: 16.5px;
          bottom: 1px;
          font-size: 10px;
        }
      }

      li:nth-child(2) {
        width: 30%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      li:nth-child(3) {
        width: 20%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      li:nth-child(4) {
        width: 15%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
