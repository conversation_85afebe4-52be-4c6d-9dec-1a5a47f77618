<template>
  <div calss="w-full h-full">
    <el-scrollbar class="content-scrollbar" :height="customElScrollbarHeight + 'px'">
      <div class="content">
        <div :style="flag ? 'margin-top: 55px;' : 'margin-top: 0px'" class="list" :key="new Date()">
          <li class="h-[105px] relative">
            <ArtPlayerComp :videoName="'video1'" :url="video.url0" />
            <!--            <span> 焊接工人正在作业中 </span>-->
          </li>
          <li class="h-[105px] relative">
            <ArtPlayerComp :videoName="'video2'" :url="video.url1" />
            <!--            <span> 维保工人正在作业中 </span>-->
          </li>
          <li class="h-[105px] relative">
            <ArtPlayerComp :videoName="'video3'" :url="video.url2" />
            <!--            <span> 用电工人正在作业中 </span>-->
          </li>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { useWatchWindowSizeHeight } from '@/hooks/windowSizeHooks'
import ArtPlayerComp from '~/components/player/artPlayer.vue'
import $API from '~/common/api'
import dayjs from 'dayjs'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const flag = ref(false)
const customElScrollbarHeight = ref(130)
const video = reactive({
  url0: '',
  url1: '',
  url2: '',
})

useWatchWindowSizeHeight((val: any) => {
  if (val > 1000) {
    flag.value = true
    customElScrollbarHeight.value = 253
  } else {
    flag.value = false
    customElScrollbarHeight.value = 130
  }
})
const today = dayjs().format('YYYY-MM-DD')
const getVideo = () => {
  $API
    .post({
      url: `edu-inter-server/mis/v1/queryDeviceVideoInfoByUnitId`,
      data: {
        planStartTime: today + ' 00:00:00',
        planEndTime: today + ' 23:59:59',
        unitId: [ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId],
      },
    })
    .then((res: any) => {
      console.log('res -----> 🚀', res)
      if (res && res.code === 200) {
        if (res.data && res.data.length) {
          res.data.forEach((item, index) => {
            video['url' + index] = item.videoUrl
          })
        }
        console.log('video=', video)
      }
    })
}
watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getVideo()
    }
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  getVideo()
})
</script>

<style scoped lang="scss">
.content-scrollbar {
  background-image: url('../../image2/juxing.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.content {
  width: 95%;
  margin: 0 auto;
  box-sizing: border-box;
  padding-top: 5px;

  list-style: none;

  .list {
    margin: auto;
    width: 100%;
    display: flex;
    justify-content: space-between;

    li {
      margin-left: 4%;
      font-size: 12px;
      width: 33%;

      span {
        margin-left: 18%;
      }
    }
  }
}
</style>
