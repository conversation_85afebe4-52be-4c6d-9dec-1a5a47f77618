<template>
  <div class="top">
    <li>
      <div>
        <img src="../../image2/topicon1.png" />
      </div>

      <div>
        <p>{{ misRight.relatedUnitCount }}</p>
        <p>{{ ui.zhLogo === 'yanchang' ? '承包商企业总数' : '相关方企业总数' }}</p>
      </div>
    </li>
    <li>
      <div>
        <img src="../../image2/topicon2.png" />
      </div>

      <div>
        <p>{{ misRight.relatedPersonCount }}</p>
        <p>{{ ui.zhLogo === 'yanchang' ? '承包商累计人数' : '相关方累计人数' }}</p>
      </div>
    </li>
    <li>
      <div>
        <img src="../../image2/topicon3.png" />
      </div>

      <div>
        <p>{{ misRight.serverProjectCount }}</p>
        <p>服务项目总数</p>
      </div>
    </li>
    <li>
      <div>
        <img src="../../image2/topicon4.png" />
      </div>

      <div>
        <p>{{ misRight.transitProjectCount }}</p>
        <p>在途项目数</p>
      </div>
    </li>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()

interface IData {
  relatedUnitCount: number
  relatedPersonCount: number
  serverProjectCount: number
  transitProjectCount: number
}

const misRight = ref<IData>({
  relatedUnitCount: 0,
  relatedPersonCount: 0,
  serverProjectCount: 0,
  transitProjectCount: 0
})

function getMisCenter() {
  $API
    .get({
      url: 'edu-inter-server/mis/v1/misRight',
      params: { unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        misRight.value = res.data
      }
    })
}

watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getMisCenter()
    }
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  getMisCenter()
})
</script>

<style lang="scss" scoped>
.top {
  width: 100%;
  height: 100%;
  list-style: none;
  display: flex;
  justify-content: space-between;

  li:nth-child(1) {
    // width: 194px;
    width: 24%;
    height: 70px;
    background-image: url('../../image2/topbg1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    // padding-top: 1.5%;
    padding-left: 1%;

    img {
      width: 30px;
      padding-top: 16px;
    }

    div:nth-child(2) {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      p:nth-child(1) {
        font-size: 20px;
      }

      p:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  li:nth-child(2) {
    width: 24%;
    height: 70px;

    background-image: url('../../image2/topbg2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    // padding-top: 1.5%;
    padding-left: 1%;

    img {
      width: 30px;
      padding-top: 16px;
    }

    div:nth-child(2) {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      p:nth-child(1) {
        font-size: 20px;
      }

      p:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  li:nth-child(3) {
    width: 24%;
    height: 70px;
    background-image: url('../../image2/topbg3.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    // padding-top: 1.5%;
    padding-left: 1%;

    img {
      width: 30px;
      padding-top: 16px;
    }

    div:nth-child(2) {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      p:nth-child(1) {
        font-size: 20px;
      }

      p:nth-child(2) {
        font-size: 12px;
      }
    }
  }

  li:nth-child(4) {
    width: 24%;
    height: 70px;
    background-image: url('../../image2/topbg4.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;

    // padding-top: 1.5%;
    padding-left: 1%;

    img {
      width: 30px;
      padding-top: 16px;
    }

    div:nth-child(2) {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      p:nth-child(1) {
        font-size: 20px;
      }

      p:nth-child(2) {
        font-size: 12px;
      }
    }
  }
}
</style>
