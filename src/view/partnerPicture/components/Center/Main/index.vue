<template>
  <div class="w-full h-full">
    <div class="list">
      <li>序号</li>
      <li>{{ ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业' }}</li>
      <li>作业类型</li>
      <li>作业人数</li>
      <li>作业状态</li>
    </div>
    <div class="gao relative">
      <div v-if="arr.length">
        <div class="list_content" v-for="(item, index) in arr" :key="item.id" :class="{ even: index % 2 == 0 }">
          <li>{{ index + 1 }}</li>
          <li v-if="item.operationUnitName.length >= 15">
            <el-tooltip class="box-item" effect="dark" :content="item.operationUnitName" placement="top">
              {{ item.operationUnitName.slice(0, 15) + '...' }}
            </el-tooltip>
          </li>
          <li v-else>{{ item.operationUnitName }}</li>
          <li>{{ item.operationTypeName }}</li>
          <li>{{ item.operationPerNum }}人</li>
          <li>
            <span style="color: #968743" v-show="item.operationStatus == '1'">草稿</span>
            <img src="../../image2/caogao.png" v-show="item.operationStatus == '1'" />
            <span style="color: #968743" v-show="item.operationStatus == '2'">申请中</span>
            <img src="../../image2/centershalou.png" v-show="item.operationStatus == '2'" />
            <span style="color: #40ab45" v-show="item.operationStatus == '3'">作业中</span>
            <img src="../../image2/centertime.png" v-show="item.operationStatus == '3'" />
            <span style="color: #bec3ce" v-show="item.operationStatus == '4'">已完成</span>
            <img src="../../image2/centerdone.png" v-show="item.operationStatus == '4'" />
            <span style="color: #bec3ce" v-show="item.operationStatus == '5'">作废</span>
            <img src="../../image2/zuofei.png" v-show="item.operationStatus == '5'" />
            <div class="inline ml-10px cursor-pointer" @click="openGis(item)">
              <img src="../../image2/centericon.png" />
            </div>
          </li>
        </div>
      </div>
      <no-data v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { BRI_EVENT_TYPE, EVENT_TYPE } from '~/common/utils/types'
import { BridgeRemoteService } from '~/common/utils/BridgeRemoteService'
import NoData from '@/components/public/noData.vue'
import { ElMessage } from 'element-plus'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const arr = ref<any>([])

function getLastOperate() {
  arr.value = []
  $API
    .post({
      url: 'edu-inter-server/mis/v1/lastOperation',
      data: { searchNum: 7, unitId: ui.value.misUnitId ? ui.value.misUnitId : ui.value.unitId }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        arr.value = res.data
      }
    })
}

async function openGis(item) {
  let res = await $API.post({
    url: 'edu-inter-server/mis/v1/getOperationVideoList',
    params: { operationBaseId: item.id }
  })
  if (res.data.length) {
    setTimeout(() => {
      BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
        type: BRI_EVENT_TYPE.SHOW_GIS_VIDEO_DETAIL,
        data: res.data
      })
    }, 3000)
  } else {
    ElMessage.warning('暂无设备')
  }
  // window.open(config.openGisUrl)
}

watch(
  () => ui.value.misUnitId,
  (val) => {
    if (val) {
      getLastOperate()
    }
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  getLastOperate()
})
</script>

<style scoped lang="scss">
.list {
  width: 95%;
  margin: 0 auto;
  list-style: none;
  height: 39px;
  line-height: 39px;
  background: linear-gradient(180deg, #0b63ae 0%, #022e5b 100%);
  opacity: 0.55;

  margin-top: 5px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  text-align: center;

  li:nth-child(1) {
    width: 10%;
  }

  li:nth-child(2) {
    width: 30%;
  }

  li:nth-child(3) {
    width: 20%;
  }

  li:nth-child(4) {
    width: 15%;
  }

  li:nth-child(5) {
    width: 25%;
  }
}

.gao {
  width: 95%;
  margin: 0 auto;
  height: 421px;
  background-image: url('../../image2/juxing.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: auto;
  font-size: 12px;

  .list_content {
    display: flex;
    justify-content: space-between;
    text-align: center;
    list-style: none;

    li:nth-child(1) {
      width: 10%;
      height: 57px;
      line-height: 57px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    li:nth-child(2) {
      width: 30%;
      height: 57px;
      line-height: 57px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    li:nth-child(3) {
      width: 20%;
      height: 57px;
      line-height: 57px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    li:nth-child(4) {
      width: 15%;
      height: 57px;
      line-height: 57px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    li:nth-child(5) {
      width: 25%;
      height: 57px;
      line-height: 57px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      img {
        width: 15px;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
}

.even {
  background: rgba(30, 67, 101, 0.4);
}
</style>
