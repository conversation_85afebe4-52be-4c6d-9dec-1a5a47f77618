<template>
  <div class="w_server_content">
    <div class="w_server_title_box">
      <el-form :inline="true" :model="filterForm" class="demo-form-inline">
        <el-form-item>
          <el-input
            v-model="filterForm.keyWord"
            placeholder="请输入人员姓名或企业名称进行搜索"
            clearable
            :suffix-icon="Search"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="w_server_table">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%" fit>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column
          prop="unitName"
          :label="ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业'"
          align="center"
          show-overflow-tooltip
          width="240"
        >
          <template #default="scope">
            {{ scope.row.unitName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.name || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="center" show-overflow-tooltip width="150">
          <template #default="scope">
            {{ scope.row.phone || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="certificateName" label="证件名称" align="center" show-overflow-tooltip width="150">
          <template #default="scope">
            {{ scope.row.certificateName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="certificateNo" label="证件编号" align="center" show-overflow-tooltip width="120">
          <template #default="scope">
            {{ scope.row.certificateNo || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="280" align="center">
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="toDetail(scope.row)">安全履历</el-button>
            <el-button
              v-auth="['ryllMgrCancelBlacklist']"
              type="danger"
              plain
              size="small"
              @click="noJoinBlacklist(scope.row)"
              >取消黑名单</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_server_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <!-- 停用 -->
  <el-dialog v-model="innerVisible" width="300" title="取消黑名单确认" @close="cancel">
    确认当前用户取消黑名单吗？
    <!--    <el-form ref="ruleDialogRef" style="max-width: 600px" :model="ruleDioData" status-icon label-width="auto"-->
    <!--             class="demo-ruleForm">-->
    <!--      <el-form-item :rules="{ required: true, message: '请输入取消黑名单原因', trigger: ['blur', 'change'] }" prop="tyyy">-->
    <!--        <el-input v-model="ruleDioData.tyyy" style="width: 100%" :rows="5" type="textarea" placeholder="请输入取消黑名单原因"-->
    <!--                  maxlength="100" show-word-limit @change="getInput" />-->
    <!--      </el-form-item>-->
    <!--    </el-form>-->
    <template #footer>
      <div class="dialog-footer pr-[20px]">
        <el-button @click="cancel" plain>取消</el-button>
        <el-button type="primary" @click="submit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import mixTableHeight from '~/common/tableHeight.js'
import { Search } from '@element-plus/icons-vue'
import { useUserInfo } from '~/store'

const ui: any = useUserInfo()
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['action'])
const { tableHeight } = mixTableHeight({ subtractHeight: 365 })
const ruleDialogRef = ref()
const innerVisible = ref(false)
const currentReasonRow = ref<any>({})
const filterForm = ref({
  keyWord: ''
})
const ruleDioData = ref({
  tyyy: ''
})

const tableData = ref<any>([])

const total = ref<number>(0)

const pageNo = ref<number>(1)

const pageSize = ref<number>(20)

const handleSizeChange = () => {
  pageNo.value = 1
  getData()
}
const handleCurrentChange = () => {
  getData()
}

function toDetail(row: any) {
  emits('action', row)
}

function noJoinBlacklist(row: any) {
  innerVisible.value = true
  currentReasonRow.value = { ...row }
}

const cancel = () => {
  innerVisible.value = false
  ruleDioData.value.tyyy = '假数据'
}

async function submit() {
  // ruleDialogRef.value?.validate(async (valid: boolean) => {
  //   if (valid) {
  const params = {
    id: currentReasonRow.value.id,
    unitId: currentReasonRow.value.unitId,
    blockType: '0'
  }
  const res: any = await $API.get({
    url: 'edu-inter-server/perManager/operateBlock',
    params
  })
  if (res.code == 'success' || res.code == 200) {
    ElMessage({
      message: res.msg || '此用户已取消 黑名单 列表中',
      type: 'success'
    })
    cancel()
    await getData()
  } else {
    ElMessage.error(res.msg || '取消黑名单失败')
  }
  //   }
  // })
}

// 获取列表
async function getData() {
  const res: any = await $API.post({
    url: 'edu-inter-server/perManager/pageList',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      type: '1',
      ...filterForm.value,
      unitId: props.id ? props.id : ui.value.unitId
    }
  })

  tableData.value = res.data?.rows
  total.value = res.data?.total
}
function refreshFetch() {
  getData()
}

// getData()

watch(
  () => filterForm.value.keyWord,
  () => {
    getData()
  }
)

watch(
  () => props.id,
  () => {
    getData()
  },
  { immediate: true }
)

watch(
  () => innerVisible.value,
  () => {
    if (!innerVisible.value) {
      ruleDialogRef.value?.resetFields()
    }
  }
)

defineExpose({
  refreshFetch
})
defineOptions({ name: 'PnlReMtStopCom' })
</script>

<style scoped lang="scss">
.w_server_content {
  display: grid;
  width: 100%;
  height: 100%;
  padding: 20px 24px 20px 24px;
  background-color: rgba(255, 255, 255, 1);
  grid-template-columns: 1fr;
  grid-template-rows: 50px 1fr;
  grid-row-gap: 20px;

  .w_server_title_box {
    width: 100%;
    height: 54px;
    background-size: 100% 100%;
    line-height: 54px;
  }

  .w_server_table {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_server_page_num {
    display: flex;
    justify-content: end;
  }
}

.demo-form-inline .el-input {
  --el-input-width: 280px;
}

.demo-form-inline .el-select {
  --el-select-width: 280px;
}
</style>
