<template>
  <div class="relative popup-main" v-loading="false">
    <div class="h-full">
      <el-scrollbar class="w-full !h-[calc(100vh-180px)]">
        <div class="peixun">
          <li>
            <p>作业总次数（次）</p>
            <p>{{ operTotal }}</p>
          </li>
          <li>
            <p>合规次数（次）</p>
            <p>{{ operOk }}</p>
          </li>
          <li>
            <p style="">违规次数（次）</p>
            <p>{{ operUnrule }}</p>
          </li>
        </div>
        <div class="w_dialog_from_box" v-for="(item, index) in baseDataList" :key="index">
          <div class="info-item">
            <span class="info-label">作业时间：</span>
            <span class="info-text">{{ item.planStartTime + ' ~ ' + item.planEndTime || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ ui.zhLogo === 'yanchang' ? '承包商单位' : '相关方单位' }}：</span>
            <span class="info-text">{{ props.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">作业位置：</span>
            <span class="info-text">{{ item.operationLocationPart || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">作业编号：</span>
            <span class="info-text">{{ item.operationNo || '--' }}</span>
          </div>
          <div class="info-item">
            <div class="flex gap-2">
              <div class="w_tag_b" v-if="item.operationStatus == 2">申请中</div>
              <div class="w_tag_l" v-else-if="item.operationStatus == 3">作业中</div>
              <div class="w_tag_s" v-else-if="item.operationStatus == 4" round>已完成</div>
              <div class="w_tag_l">
                {{ item.operationTypeName }}
              </div>
              <div class="w_tag_o">无违规</div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const props = defineProps({
  name: {
    type: String,
    default: ''
  }
})

console.log()
const operTotal = ref<number>(0)
const operOk = ref<number>(0)
const operUnrule = ref<number>(0)
const baseDataList = ref([])

const items = ref([
  { type: 'primary', label: '动火作业' },
  { type: 'success', label: '作业中' },
  { type: 'info', label: '已完成' },
  { type: 'warning', label: '无违规' },
  { type: 'danger', label: '有违规' }
])

async function getJobDetail(id: string) {
  const res: any = await $API.post({
    url: 'edu-inter-server/perManager/queryUserOperBaseStatic',
    data: {
      personnelIds: id,
      projectId: ''
    }
  })
  if (res.code == 'success' || res.code == 200) {
    operTotal.value = res.data.operTotal
    operOk.value = res.data.operOk
    operUnrule.value = res.data.operUnrule
  }
}

async function getJobDetailList(id: string) {
  const res: any = await $API.post({
    url: 'edu-inter-server/perManager/queryUserOperBaseList',
    data: {
      personnelIds: id,
      projectId: '',
      pageNo: 1,
      pageSize: 100
    }
  })
  if (res.code == 'success' || res.code == 200) {
    baseDataList.value = res.data
  }
}

const initData = (id: string) => {
  getJobDetail(id)
  getJobDetailList(id)
}

defineExpose({
  initData
})

defineOptions({ name: 'PnlReMtJobInfoComp' })
</script>

<style scoped lang="scss">
.popup-main {
  height: calc(100% - 70px);

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
    }
  }
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  padding: 20px;
  margin: 24px 14px 0;
}

.info-item {
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.25;

  &:last-of-type {
    margin-bottom: 0;
  }

  .info-text {
    color: #606266;
  }
}

.peixun {
  list-style: none;
  display: flex;
  width: 100%;
  justify-content: space-around;
  color: #fff;
  margin-top: 10px;

  li:nth-child(1) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    box-sizing: border-box;

    p:nth-child(1) {
      font-size: 18px;
    }

    p:nth-child(2) {
      font-size: 34px;
    }
  }

  li:nth-child(2) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    // padding-top: 5px;
    box-sizing: border-box;

    p:nth-child(1) {
      font-size: 18px;
    }

    p:nth-child(2) {
      font-size: 34px;
    }
  }

  li:nth-child(3) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj3.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    // padding-top: 5px;
    box-sizing: border-box;

    p:nth-child(1) {
      font-size: 18px;
    }

    p:nth-child(2) {
      font-size: 34px;
      // padding-bottom: -11px;
    }
  }
}
.w_tag_l {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  background: #527cff;
  border-radius: 4px;
}

.w_tag_b {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  background: rgba(80, 178, 125, 1);
  border-radius: 4px;
}

.w_tag_s {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  background: rgba(145, 148, 151, 1);
  border-radius: 4px;
}

.w_tag_o {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  background: rgba(233, 157, 66, 1);
  border-radius: 4px;
}

.w_tag_r {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  background: rgba(208, 45, 44, 1);
  border-radius: 4px;
}
</style>
