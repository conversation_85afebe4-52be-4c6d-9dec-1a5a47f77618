<template>
  <div class="relative popup-main" v-loading="false">
    <div class="h-full">
      <el-scrollbar class="w-full !h-[calc(100vh-180px)]">
        <div class="w_dialog_from_box">
          <div class="info-item">
            <span class="info-label">姓名：</span>
            <span class="info-text">{{ baseInfo.userName || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">年龄：</span>
            <span class="info-text">{{ baseInfo.age || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">手机号：</span>
            <span class="info-text">{{ baseInfo.phone || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业' }}：</span>
            <span class="info-text">{{ baseInfo.unitName || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">身份证照片：</span>
            <span v-if="idCardFile == ''" class="info-text">--</span>
          </div>
          <div v-if="idCardFile != ''" class="info-item">
            <span v-for="(item, index) in idCardFileList" :key="index" class="info-text">
              <el-image
                style="width: 160px; height: 100px; margin-right: 10px; border-radius: 4px"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="idCardFileList"
                :initial-index="4"
                fit="cover"
              />
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">证书名称：</span>
            <span class="info-text">{{ baseInfo.certificateName || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">证书编号：</span>
            <span class="info-text">{{ baseInfo.certificateNum || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">证书照片：</span>
            <span v-if="certificateFile == ''" class="info-text">--</span>
          </div>
          <div v-if="certificateFile != ''" class="info-item">
            <span v-for="(item, index) in certificateFileList" :key="index" class="info-text">
              <el-image
                style="margin-right: 10px; width: 160px; height: 100px; border-radius: 4px"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="certificateFileList"
                :initial-index="4"
                fit="cover"
              />
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">证书有效期：</span>
            <span class="info-text">{{ baseInfo.validate || '--' }}</span>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import config from '@/config'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const baseInfo = ref({
  userName: '',
  age: '',
  phone: '',
  unitName: '',
  idCardFileId: '',
  idCardFiles: [
    {
      businessData: '',
      businessId: '',
      businessType: '',
      fileName: '',
      filePath: '',
      fileSize: '',
      id: '',
      isDel: '',
      suffix: '',
      uploadDate: ''
    }
  ],
  certificateId: '',
  certificateName: '',
  certificateNum: '',
  certificateFiles: [
    {
      businessData: '',
      businessId: '',
      businessType: '',
      fileName: '',
      filePath: '',
      fileSize: '',
      id: '',
      isDel: '',
      suffix: '',
      uploadDate: ''
    }
  ],
  validate: ''
})

const idCardFile = ref()
const idCardFileList = ref()
const certificateFile = ref()
const certificateFileList = ref()

function handleData(data: any) {
  baseInfo.value = data
  if (baseInfo.value.idCardFiles && baseInfo.value.idCardFiles.length > 0) {
    idCardFile.value = config.downloadFileUrl + baseInfo.value.idCardFiles[0].filePath
    idCardFileList.value = baseInfo.value.idCardFiles.map((item: any) => config.downloadFileUrl + item.filePath)
  } else {
    idCardFile.value = ''
    idCardFileList.value = []
  }
  if (baseInfo.value.certificateFiles && baseInfo.value.certificateFiles.length > 0) {
    certificateFile.value = config.downloadFileUrl + baseInfo.value.certificateFiles[0].filePath
    certificateFileList.value = baseInfo.value.certificateFiles.map(
      (item: any) => config.downloadFileUrl + item.filePath
    )
  } else {
    certificateFile.value = ''
    certificateFileList.value = []
  }
}

async function getDetail(id: string) {
  const res: any = await $API.get({
    url: 'edu-inter-server/perManager/baseInfo',
    params: {
      id: id
    }
      id: id
  }
  })
if (res.code == 'success' || res.code == 200) {
  handleData(res?.data)
}
}

const initData = (id: string) => {
  getDetail(id)
}

defineExpose({
  initData
})

defineOptions({ name: 'PnlReMtDetailCom' })
</script>

<style scoped lang="scss">
.popup-main {
  height: calc(100% - 70px);

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
    }
  }
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  padding: 20px;
  margin: 24px 24px 0;
}

.info-item {
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.25;

  &:last-of-type {
    margin-bottom: 0;
  }

  .info-text {
    color: #606266;
  }
}
</style>
