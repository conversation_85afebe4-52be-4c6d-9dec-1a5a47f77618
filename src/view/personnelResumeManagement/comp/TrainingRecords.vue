<template>
  <div class="relative popup-main" v-loading="false">
    <div class="h-full">
      <el-scrollbar class="w-full !h-[calc(100vh-180px)]">
        <div class="peixun">
          <li>
            <p>培训总数（次）</p>
            <p>{{ trainTotal }}</p>
          </li>
          <li>
            <p>培训通过（次）</p>
            <p>{{ trainPass }}</p>
          </li>
          <li>
            <p style="">未通过（次）</p>
            <p>{{ unTrainPass }}</p>
          </li>
        </div>
        <div class="w_dialog_from_box" v-for="(items, index) in baseDataList" :key="index">
          <div class="info-item">
            <span class="info-label">培训时间：</span>
            <span class="info-text">{{ items.taskStartTime + ' ~ ' + items.taskEndTime || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">培训主题：</span>
            <span class="info-text">{{ items.trainTopic || '--' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">培训讲师：</span>
            <span class="info-text">{{ items.userName || '--' }} {{ items.deptName }} {{ items.positionName }}</span>
          </div>
          <div class="info-item flex items-center">
            <span class="info-label">考核分值：</span>
            <span class="info-text">{{ items.score || '--' }}<span v-if="items.score">分</span></span>
          </div>
          <div class="info-item">
            <div class="flex gap-2">
              <div v-if="items.trainResults == 1" class="w_res_b">{{ items.trainResultsName }}</div>
              <div v-else class="w_res_r">未通过</div>
              <!--              <el-tag type="success" effect="dark" round v-if="items.trainResults == 1">-->
              <!--                {{ items.trainResultsName }}-->
              <!--              </el-tag>-->
              <!--              <el-tag type="error" effect="dark" round v-else> 未通过 </el-tag>-->
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '~/common/api.ts'

const trainPass = ref<number>(12)
const trainTotal = ref<number>(2)
const unTrainPass = ref<number>(3)

const baseDataList = ref([])

// const itemsType = ref([{ type: 'success', label: '培训通过' }])

async function getTrainingRecords(id: string) {
  const res: any = await $API.post({
    url: 'edu-inter-server/perManager/queryUserTrainBaseStatic',
    params: {
      userId: id,
      // userId: '1817383425474494466',
    },
  })
  if (res.code == 'success' || res.code == 200) {
    trainPass.value = res.data.trainPass
    trainTotal.value = res.data.trainTotal
    unTrainPass.value = res.data.unTrainPass
  }
}

async function getTrainingRecordsList(id: string) {
  const res: any = await $API.post({
    url: 'edu-inter-server/perManager/queryUserTrainBaseList',
    params: {
      userId: id,
      // userId: '1817383425474494466',
      pageNo: 1,
      pageSize: 100,
    },
  })
  if (res.code == 'success' || res.code == 200) {
    baseDataList.value = res.data.rows || []
  }
}

const initData = (id: string) => {
  getTrainingRecords(id)
  getTrainingRecordsList(id)
}

defineExpose({
  initData,
})

defineOptions({ name: 'PnlReMtTrainingRecordsComp' })
</script>

<style scoped lang="scss">
.popup-main {
  height: calc(100% - 70px);

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
    }
  }
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  padding: 20px;
  margin: 24px 14px 0;
}

.info-item {
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.25;
  &:last-of-type {
    margin-bottom: 0;
  }

  .info-text {
    color: #606266;
  }
}
.peixun {
  list-style: none;
  display: flex;
  width: 100%;
  justify-content: space-around;
  color: #fff;
  margin-top: 10px;
  li:nth-child(1) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj4.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    box-sizing: border-box;
    p:nth-child(1) {
      font-size: 18px;
    }
    p:nth-child(2) {
      font-size: 34px;
    }
  }
  li:nth-child(2) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj5.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    // padding-top: 5px;
    box-sizing: border-box;
    p:nth-child(1) {
      font-size: 18px;
    }
    p:nth-child(2) {
      font-size: 34px;
    }
  }
  li:nth-child(3) {
    width: 188px;
    height: 97px;
    background-image: url('../../../assets/image/bj6.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding-left: 15px;
    padding-top: 16px;
    // padding-top: 5px;
    box-sizing: border-box;
    p:nth-child(1) {
      font-size: 18px;
    }
    p:nth-child(2) {
      font-size: 34px;
      // padding-bottom: -11px;
    }
  }
}
.w_res_b {
  width: 70px;
  height: 30px;
  background: #00b578;
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  color: white;
  line-height: 30px;
}

.w_res_r {
  width: 70px;
  height: 30px;
  background: rgba(221, 34, 34, 1);
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  color: white;
  line-height: 30px;
}
</style>
