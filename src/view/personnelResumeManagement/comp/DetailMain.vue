<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>详情</div>
        </div>
      </template>
      <el-tabs type="card" v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="基本信息" name="first"> </el-tab-pane>
        <el-tab-pane label="作业信息" name="second"> </el-tab-pane>
        <el-tab-pane label="培训记录" name="third"> </el-tab-pane>
      </el-tabs>
      <Detail v-show="activeName === 'first'" ref="detailRef" />
      <JobInfo v-show="activeName === 'second'" ref="jobInfoRef" :name="unitName"/>
      <TrainingRecords v-show="activeName === 'third'" ref="trainingRecordsRef" />
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { nextTick, ref } from 'vue'
import $API from '~/common/api'
import Detail from './Detail.vue'
import JobInfo from './JobInfo.vue'
import TrainingRecords from './TrainingRecords.vue'
const activeName = ref('first')
const detailId = ref('')
const unitName = ref('')
const handleClick = (tab: any) => {
  if (tab.props.name == 'first') {
    nextTick(() => {
      detailRef.value.initData(detailId.value)
    })
  } else if (tab.props.name == 'second') {
    jobInfoRef.value.initData(detailId.value)
  } else {
    trainingRecordsRef.value.initData(detailId.value)
  }
}

const drawer = ref(false)
const detailRef = ref()
const jobInfoRef = ref()
const trainingRecordsRef = ref()

const showDialog = (row: any) => {
  activeName.value = 'first'
  drawer.value = true
  if (row.id) {
    detailId.value = row.id
    unitName.value = row.unitName

    nextTick(() => {
      detailRef.value.initData(detailId.value)
    })

  }

}

defineExpose({
  showDialog,
})

defineOptions({ name: 'PnlReMtDetailMain' })
</script>

<style scoped lang="scss">
.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
    overflow: auto !important;
  }

  :deep(.el-tab-pane) {
    height: 100% !important;
    overflow: auto !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  :deep(.el-drawer__close-btn) {
    position: fixed !important;
    top: 1% !important;
    right: 2% !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-tabs--card > .el-tabs__header) {
  border: none;
}

:deep(.el-tabs__nav-scroll) {
  // padding: 1000px;
  margin-left: 25%;
  // color: #6b778c;
}

:deep(.el-tabs__item) {
  border-bottom: 1px solid rgb(237, 231, 231) !important;
}

.w_add_plan_header {
  font-weight: 700;

  color: #333;
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: start;
  align-items: center;

  div:first-of-type {
    /* 样式规则 */
    width: 18px;
    height: 12px;
    //background: #527cff;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    border-radius: 2px 2px 2px 2px;
  }
}

:deep(.el-tabs--top) {
  // flex-direction: column !important;
}
</style>
