<template>
  <div class="w-full h-full flex">
    <el-card
      v-if="ui.unitOrgType !== '1'"
      class="mr-[20px] org-tree"
      :class="!collapsed ? '' : 'p-20px'"
      :style="{ width: collapsed ? '323px' : '0' }"
    >
      <train-tree :collapsed="collapsed" @serach="searchData" />
    </el-card>
    <div class="w_main_box relative">
      <div class="head_box">
        <HeadTab :changeTabs="changeTabs" @changeTab="changeTab"></HeadTab>
      </div>
      <div class="w_c_tab_box">
        <el-scrollbar>
          <component :is="tabs[current]" @action="action" ref="tableRef" :id="orgCode"></component>
        </el-scrollbar>
      </div>
      <div class="expand" @click="collapsedTree" v-if="ui.unitOrgType !== '1'"></div>
      <!-- <DetailMain ref="dialogRef" @action="handleFun"></DetailMain> -->
      <SafetyRecord v-model="safetyRecordVisible" :option="currentRecord" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import serverCom from './comp/serverCom.vue'
import stopCom from './comp/stopCom.vue'
// import DetailMain from './comp/DetailMain.vue'
import HeadTab from '~/components/HeadTab/index.vue'
import { useUserInfo } from '~/store'
import TrainTree from '@/components/tree/index.vue'
import SafetyRecord from '@/view/enterprise/personnel/comp/SafetyRecord.vue'

const ui: any = useUserInfo()
const tabs = {
  serverCom,
  stopCom
}
const collapsed = ref(true)
const dialogRef = ref()
const orgCode = ref<string>(ui.value.unitId)
const tableRef = ref()
const changeTabs = [
  { label: '服务中', value: '1', width: '100' },
  { label: '黑名单', value: '2', width: '100' }
]
const current = ref('serverCom')

const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

// 接受树结构选中数据
const searchData = (obj) => {
  orgCode.value = obj.orgCode
}
function changeTab(item: any) {
  let num = item.value
  switch (num) {
    case '1':
      current.value = 'serverCom'
      break
    case '2':
      current.value = 'stopCom'
      break
    default:
      break
  }
}

function handleFun() {
  tableRef.value.refreshFetch()
}

const safetyRecordVisible = ref(false)
const currentRecord = ref()
function action(obj: any) {
  console.log(obj, '>>>>>>>>>>>')
  // dialogRef.value.showDialog(obj)
  currentRecord.value = obj
  safetyRecordVisible.value = true
}
onMounted(() => {})
defineOptions({ name: 'PnlReMtIndex' }) // 人员履历
</script>

<style scoped lang="scss">
.org-tree {
  // min-width: 310px;
  background: #eef7ff;
  height: calc(100vh - 138px);
  overflow: hidden;

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.w_main_box {
  flex: 1;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: 50px 1fr;

  .crumbs_box {
    background-color: white;
  }

  .head_box {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .head_tab {
      width: 120px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background: url('@/assets/image/tab_untive.png') no-repeat;
      background-size: 100% 100%;
      margin-left: -10px;

      &:first-child {
        text-align: start;
        padding-left: 20px;
        background: url('@/assets/image/first_active.png') no-repeat;
        background-size: 100% 100%;
        margin-left: 0;
      }
    }

    .w_active {
      width: 120px;
      height: 40px;
      line-height: 40px;
      background: url('@/assets/image/active.png') no-repeat;
      background-size: 100% 100%;
      color: #ffffff;
      z-index: 99;

      &:first-child {
        background: url('@/assets/image/tab_active.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .w_c_tab_box {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    height: 100%;
    overflow: hidden;
    margin-top: -10px;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }

  .expand {
    background: url('@/assets/expand.png') no-repeat;
    width: 34px;
    height: 36px;
    background-size: 100% 100%;
    position: absolute;
    top: 45%;
    left: -17px;
    cursor: pointer;
  }
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

.ifm-child {
  .w_c_tab_box {
    :deep(.w_server_table) {
      @apply flex flex-col overflow-hidden;
      .el-table {
        height: 100% !important;
      }
    }
  }
}
</style>
