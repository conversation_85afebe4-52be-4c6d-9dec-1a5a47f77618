<template>
  <div>
    <el-loading v-if="loading" text="文件加载中..." />
    <Suspense>
      <template #default>
        <div class="preview-container">
          <component :is="currentComponent" v-if="currentComponent" :src="filePathSrc" @rendered="onRendered" />
        </div>
      </template>
      <template #fallback>
        <div class="loading-placeholder">文件加载中...</div>
      </template>
    </Suspense>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef } from 'vue'
import { useRoute } from 'vue-router'

const loading = ref(true)
const filePathSrc = ref('')
const route = useRoute()

// 使用 shallowRef 优化性能
const currentComponent = shallowRef(null)

// 根据文件后缀判断文件类型并加载对应组件
const loadComponent = async () => {
  const url = filePathSrc.value.toLowerCase()
  let componentKey = ''

  if (url.includes('doc') || url.includes('docx')) componentKey = 'doc'
  else if (url.includes('ppt') || url.includes('pptx')) componentKey = 'ppt'

  if (componentKey) {
    try {
      // 动态导入对应组件
      const module = await components[componentKey]()
      currentComponent.value = module.default
    } catch (error) {
      console.error('组件加载失败:', error)
    }
  }
}

const onRendered = () => {
  loading.value = false
}

// 动态导入组件配置
const components = {
  doc: () => import('@vue-office/docx'),
  ppt: () => import('@vue-office/pptx')
}

onMounted(async () => {
  filePathSrc.value = route.query.value as string
  await loadComponent()
})
</script>

<style scoped lang="scss">
div {
  background-color: #fff !important;
}
</style>
