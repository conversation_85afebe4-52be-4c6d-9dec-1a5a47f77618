<template>
  <div class="train-course-management">
    <HeadTab :changeTabs="tabList" @changeTab="changeTab"></HeadTab>
    <component :is="component" />
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import Xmjckck from './xmjckck.vue'
import Xrbxkck from './xrbxkck.vue'
import HeadTab from '@/components/HeadTab/index.vue'

const activeTab = ref('1')
const tabList = [
  { label: '项目进场课程库', value: '1', width: '170', comp: Xmjckck },
  { label: '新人必学课程库', value: '2', width: '170', comp: Xrbxkck }
]
const component = computed(() => tabList.find((item) => item.value === activeTab.value)?.comp)

const changeTab = (item: any) => {
  activeTab.value = item.value
}
</script>

<style lang="scss" scoped>
.train-course-management {
  @apply h-full w-full;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.ifm-child {
  .train-course-management {
    :deep(.exam-bg) {
      @apply overflow-hidden h-full;
      .trainCourse {
        @apply flex flex-col pb-[10px];
        height: 100% !important;
        width: 100% !important;
        .train-content {
          @apply flex-1 h-0;
        }
      }
    }
  }
}
</style>
