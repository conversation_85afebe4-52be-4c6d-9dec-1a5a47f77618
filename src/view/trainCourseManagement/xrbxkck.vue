<template>
  <div class="exam-bg" :style="{ width: collapsed ? '100%' : 'calc(100vw - 280px)' }">
    <div class="first mb-[10px] flex items-center">
      <div class="w-[59px] h-[66px] kindReminder">
        <div class="w-[70px] fontStyle">温馨</div>
        <div class="w-[70px] mt-[-16px] fontStyle">提示</div>
      </div>
      <div class="ml-[82px] mt-[10px] remind-text">
        <div class="h-[27px]">1、系统负责人新建必学课程和考试内容，自定义设置通过线；</div>
        <div class="h-[27px]">
          2、{{ ui.zhLogo === 'yanchang' ? '根据承包商类型' : '根据相关方类型' }}系统自动发送对应课程到员工端；
        </div>
        <div class="h-[27px]">3、员工通过移动端随时随地在线学习并在线考试；</div>
        <div class="h-[27px]">4、企业端和系统负责人实时对学习数据和考试数据进行跟踪，培训结果自动留档；</div>
      </div>
    </div>
    <div class="w-full h-0 flex-1 flex">
      <el-card
        v-if="ui.unitOrgType !== '1'"
        class="org-tree"
        :class="!collapsed ? '' : 'p-20px'"
        :style="{ width: collapsed ? '323px' : '0', marginRight: collapsed ? '20px' : '0' }"
      >
        <train-tree :collapsed="collapsed" @serach="searchData" type="train" />
      </el-card>
      <el-card class="right relative w-full flex-1">
        <div
          class="trainCourse"
          :style="{ width: collapsed && ui.unitOrgType !== '1' ? 'calc(100vw - 630px)' : '100%' }"
        >
          <div class="flex justify-between mb-[20px] mx-[24px]">
            <el-input
              v-model="curriculumName"
              clearable
              class="!w-[280px]"
              placeholder="请输入课程名称关键词搜索"
              @input="getTrainCourseList"
            ></el-input>
            <el-button type="primary" @click="addTrainCourse" class="add-btn" :icon="Plus" color="#527CFF"
              >新增</el-button
            >
          </div>
          <div class="train-content" v-loading="loading">
            <el-scrollbar>
              <div v-for="(item, index) in trainCourseList" :key="index" class="train-content-box mr-[24px]">
                <div class="top relative" :style="{ background: `url(${getBackground(index)}) no-repeat` }">
                  <div class="mb-[15px] title" v-if="item.curriculumName.length < 30">{{ item.curriculumName }}</div>
                  <div class="mb-[15px] title" v-else>
                    <el-tooltip effect="dark" :content="item.curriculumName" placement="top-start">
                      {{ item.curriculumName.slice(0, 19) + '...' }}
                    </el-tooltip>
                  </div>
                  <div class="train-type" v-if="item.relatedTypeName.length < 20">{{ item.relatedTypeName }}</div>
                  <div class="train-type" v-else>
                    <el-tooltip effect="dark" :content="item.relatedTypeName" placement="top-start">
                      {{ item.relatedTypeName.slice(0, 10) + '...' }}
                    </el-tooltip>
                  </div>
                </div>
                <div class="bottom">
                  <div class="flex justify-between items-center mt-[14px] mb-[13px]">
                    <div class="createTime">创建时间: {{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
                    <div class="createTime">
                      已通过<span class="learnNum ml-[5px] mr-[5px]">{{ item.studyNum }}</span
                      >人次
                    </div>
                  </div>
                  <div class="flex justify-end mb-[17px]">
                    <el-button type="primary" plain size="small" class="span_cursor" @click="checkCourse(item.id)"
                      >查看</el-button
                    >
                    <el-button type="primary" plain size="small" class="span_cursor" @click="editCourse(item.id)"
                      >编辑</el-button
                    >
                    <el-button type="primary" plain size="small" class="span_cursor" @click="deleteCourse(item)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </div>
              <no-data v-if="!trainCourseList.length"></no-data>
            </el-scrollbar>
          </div>
          <div class="btn-page" v-if="total">
            <el-pagination
              layout=" total, sizes, prev, pager, next, jumper"
              :page-size="pageSize"
              :page-sizes="[8, 12, 24, 36]"
              :total="total"
              v-model:current-page="curPage"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="expand" v-if="ui.unitOrgType !== '1'" @click="collapsedTree"></div>
      </el-card>
    </div>
    <!-- 删除弹框 -->
    <el-dialog v-model="deleteVisible" width="488" height="290" @close="deleteClose">
      <template #header>
        <titleTag title="删除课程" />
      </template>
      <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
      <div class="text-center text-info">确定要删除吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteClose" plain>取消</el-button>
          <el-button type="primary" @click="deleteSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <!-- 新增 编辑-->
  <popup-side v-model="addlVisible" width="648px" :popupTitle="operationTitle">
    <AddOrEdit
      :operate="operate"
      @close="closeFn"
      :data="trainData"
      :id="trainId"
      :orgCode="orgCode"
      :name="orgName"
      :train-type="trainType"
    />
  </popup-side>
  <!-- 查看 -->
  <popup-side v-model="checkVisible" width="648px" popupTitle="查看详情">
    <CheckCourse :data="trainData" :train-type="trainType" />
  </popup-side>
</template>
<script setup lang="ts">
import listBg1 from '@/assets/image/top-bj1.png'
import listBg2 from '@/assets/image/top-bj2.png'
import listBg3 from '@/assets/image/top-bj3.png'
import listBg4 from '@/assets/image/top-bj4.png'
import TrainTree from '@/components/tree/index.vue'
import { Plus } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage, ElTree } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import AddOrEdit from './comp/AddOrEdit.vue'
import CheckCourse from './comp/CheckCourse.vue'
import titleTag from './comp/titleTag.vue'

const filterText = ref('')
const collapsed = ref(true)
const treeRef = ref<InstanceType<typeof ElTree>>()
const ui: any = useUserInfo()
const trainCourseList = ref<any>([])
const boxColor = ref([listBg1, listBg2, listBg3, listBg4])
const addlVisible = ref(false)
const checkVisible = ref(false)
const deleteVisible = ref(false)
const operationTitle = ref('')
const deleteId = ref('')
const loading = ref(false)
const trainId = ref('') // 编辑课程id
const operate = ref('')
const trainData = ref<any>({}) // 课程详情
const total = ref()
const curPage = ref(1)
const pageSize = ref(12)
const pageNo = ref(1)
const studyNum = ref(0) // 课程学习人数
const isAdd = ref<boolean>(false) // 是否新增
const orgCode = ref<string>(ui.value.unitId)
const orgName = ref<string>(ui.value.unitName)
const curriculumName = ref('')
const trainType = ref('1') // 培训类型 1: 新人必学课程 2： 项目进场课程

const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

// 接受树结构选中数据
const searchData = (obj) => {
  orgCode.value = obj.orgCode
  orgName.value = obj.orgName
  isAdd.value = obj.isAdd
  getTrainCourseList()
}
const getBackground = (index) => {
  return boxColor.value[index % boxColor.value.length]
}
// 获取课程列表
const getTrainCourseList = () => {
  loading.value = true
  $API
    .post({
      url: 'edu-inter-server/train/curriculum/manage/getCurriculumPage',
      data: {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        unitId: orgCode.value ? orgCode.value : ui.value.unitId,
        curriculumName: curriculumName.value,
        trainType: trainType.value
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        loading.value = false
        trainCourseList.value = res.data.rows
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
        total.value = res.data.total
      }
    })
}
// 详情接口
const getTrainDetail = (id) => {
  const params = { id }
  return new Promise((resolve, reject) => {
    $API
      .get({
        url: 'edu-inter-server/train/curriculum/manage/getCurriculumById',
        params
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          trainData.value = res.data
          resolve(trainData.value)
        } else {
          reject()
        }
      })
  })
}
// 查看详情
const checkCourse = (id) => {
  checkVisible.value = true
  getTrainDetail(id)
}
// 新增弹框
const addTrainCourse = () => {
  operationTitle.value = '新增课程'
  addlVisible.value = true
  operate.value = 'add'
}
// 编辑弹框
const editCourse = async (id) => {
  operationTitle.value = '编辑培训课程'
  trainId.value = id
  await getTrainDetail(id)
  addlVisible.value = true
  operate.value = 'edit'
}
// 删除课程
const deleteCourse = (item) => {
  deleteId.value = item.id
  studyNum.value = item.studyNum
  deleteVisible.value = true
}
// 删除确认
const deleteSubmit = () => {
  if (studyNum.value) {
    ElMessage.warning('已有学习数据，暂时不支持删除!')
    deleteVisible.value = false
    getTrainCourseList()
    return false
  }
  $API
    .get({
      url: 'edu-inter-server/train/curriculum/manage/deleteCurriculumById',
      params: {
        id: deleteId.value
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('操作成功!')
        deleteVisible.value = false
        getTrainCourseList()
      }
    })
}
// 关闭删除弹框
const deleteClose = () => {
  deleteVisible.value = false
  // ElMessage.success('操作成功!')
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNo.value = 1
  getTrainCourseList()
}
const handleCurrentChange = (val: number) => {
  pageNo.value = val
  getTrainCourseList()
}
// 关闭弹框
const closeFn = () => {
  addlVisible.value = false
  checkVisible.value = false
  getTrainCourseList()
}

watch(filterText, (val) => {
  treeRef.value!.filter(val)
})
onMounted(() => {
  getTrainCourseList()
})
</script>

<style lang="scss" scoped>
.exam-bg {
  height: calc(100% - 36px);
  display: flex;
  flex-direction: column;
}
.org-tree {
  // min-width: 310px;
  background: #eef7ff;
  /* height: calc(100vh - 265px); */
  overflow: hidden;
  transition: all 0.5s ease-in-out;

  .tree-w {
    height: calc(100vh - 400px) !important;
  }

  :deep(.el-tree) {
    .el-tree-node__content {
      //   height: 40px;
      margin-top: 4px;
      border-radius: 6px;
      /* height: auto; */
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.first {
  height: 124px;
  background: url(../../assets/image/remind-bj.png) no-repeat center;
  background-size: 100% 100%;

  .kindReminder {
    font-weight: 400;
    font-size: 32px;
    color: #527cff;
    margin-left: 96px;
    margin-bottom: 8px;

    .fontStyle {
      font-family: MyCustomFont, sans-serif;
    }
  }

  .remind-text {
    font-weight: 400;
    font-size: 14px;
    color: #061032;
  }
}

.add-btn {
  width: 80px;
  height: 32px;
}

.right {
  .trainCourse {
    height: calc(100vh - 275px);
    margin-bottom: 10px;
    padding-top: 20px;
    overflow: hidden;
    overflow-x: auto;

    .train-content {
      height: calc(100vh - 430px);
      background: #fff;
      border-bottom: 1px solid #ebeef5;
      padding-left: 24px;

      .train-content-box {
        margin-bottom: 20px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;

        .top {
          height: 126px;
          border-bottom: 1px solid #ccc;
          // padding: 0 10px;
          padding-left: 17px;
          padding-top: 20px;
          background-size: 100% 100% !important;

          .title {
            font-weight: 600;
            font-size: 18px;
            color: #ffffff;
          }

          .train-type {
            display: inline-block;
            white-space: nowrap;
            padding: 0 10px;
            height: 24px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 3px 3px 3px 3px;
            color: #fff;
            text-align: center;
            font-size: 14px;
            line-height: 24px;
          }

          .el-icon {
            position: absolute;
            right: 20px;
            bottom: 35px;
          }
        }

        .bottom {
          height: 98px;
          padding: 0 17px;
          display: flex;
          flex-direction: column;
          border: 1px solid #ccc;
          border-top: none;

          .createTime {
            font-weight: 400;
            font-size: 14px;
            color: #2f2f2f;

            .learnNum {
              font-weight: 600;
              font-size: 16px;
              color: rgba(82, 124, 255, 1);
            }
          }
        }
      }

      :deep(.el-scrollbar__view) {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        row-gap: 10px;
      }
    }

    .btn-page {
      width: 100%;
      margin-top: 13px;
      padding-right: 20px;
      text-align: right;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.expand {
  background: url('@/assets/expand.png') no-repeat;
  width: 34px;
  height: 36px;
  background-size: 100% 100%;
  position: absolute;
  top: 45%;
  left: -17px;
  cursor: pointer;
  z-index: 2;
}

.span_cursor {
  color: #527cff;
  cursor: pointer;

  &:last-child {
    // color: #d43c31 !important;
    margin-left: 20px;
  }
}

.delete {
  width: 72px;
  height: 72px;
  background: #d9dde8;
  margin: 0 auto;
  background: url(../../assets/image/exam-delete.png) no-repeat center;
  background-size: 100% 100%;
}

.text-info {
  margin-top: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #484a4d;
}

:deep(.el-card) {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

:deep(.el-button--primary.is-plain) {
  margin-left: 0;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.el-button--primary.is-plain) {
  margin-left: 20px;
}
</style>
