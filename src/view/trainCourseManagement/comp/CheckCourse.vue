<template>
  <div class="relative popup-main text-[#222]" v-loading="loading">
    <div class="h-full">
      <el-scrollbar>
        <div class="w_dialog_from_box">
          <div class="mb-[22px] bg_title">培训课程信息</div>
          <el-form label-width="130px">
            <el-form-item label="培训课程名称：">
              <span class="text">{{ data.curriculumName }}</span>
            </el-form-item>
            <el-form-item v-if="trainType === '1'" :label="ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型'">
              <span class="text">{{ data.relatedTypeName }}</span>
            </el-form-item>
            <el-form-item v-if="trainType === '2'" label="适用项目类型">
              <span class="text">{{ data.projectTypeName }}</span>
            </el-form-item>
            <el-form-item label="培训资料：">
              <div class="w_content_file">
                <div v-if="data.trainingMaterialFiles">
                  <div class="w_text_file_box" v-for="(item, index) in data.trainingMaterialFiles" :key="index">
                    <div class="mr-5 w-50 s_file" v-if="item.fileName.length > 30">
                      <el-tooltip effect="dark" :content="item.fileName" placement="top-start">
                        {{ item.fileName.slice(0, 19) + '...pdf' }}
                      </el-tooltip>
                    </div>
                    <div class="mr-5 w-50 s_file" v-else>{{ item.fileName }}</div>
                    <div class="mr-5 text_color" @click="fileUpdown(item.filePath)">预览</div>
                  </div>
                </div>
                <div v-else class="text">暂无数据</div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="w_dialog_from_box">
          <div class="mb-[22px] bg_title">培训考试</div>
          <el-form label-width="130px">
            <el-form-item label="是否考试：">
              <span class="text">{{ data.isExam === '1' ? '是' : '否' }}</span>
            </el-form-item>
            <div v-if="data.isExam === '1'">
              <el-form-item label="考试试卷：">
                <div class="w_text_div">
                  <div class="w_text_left">{{ data.examPaperName }}</div>
                  <div class="mr-5 text_color" @click="previewTestPaper(data.examPaperId)">预览</div>
                </div>
              </el-form-item>
              <el-form-item label="考试时长：">
                <span class="text">{{ data.examTime }}分钟</span>
              </el-form-item>
              <el-form-item label="最多考试次数：">
                <span class="text">{{ data.examCount }}次</span>
              </el-form-item>
              <el-form-item label="评估方式：">
                <span class="text">达到{{ data.passScore }}分通过</span>
              </el-form-item>
              <!-- <el-form-item label="考试设定：">
                <span class="text">{{ taskModuleType(data.taskModule) }}</span>
              </el-form-item> -->
            </div>
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
  <!-- 预览试卷弹框 -->
  <popup-side v-model="perviewVisible" width="920px" popupTitle="试卷预览" @close="closeFn">
    <TestPaperPreview :id="examID" />
  </popup-side>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import config from '~/config'
import TestPaperPreview from './TestPaperPreview.vue'
import { useUserInfo } from '~/store'
const ui = useUserInfo()

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  trainType: {
    type: String,
    default: '' // 培训类型 1: 新人必学课程 2： 项目进场课程
  }
})
const loading = ref(true)
const perviewVisible = ref(false) // 预览试卷
const examID = ref('') // 选取的试卷id

// 预览试卷
const previewTestPaper = (id) => {
  examID.value = id
  perviewVisible.value = true
}
const closeFn = () => {
  perviewVisible.value = false
}
const fileUpdown = (path: string) => {
  // console.log(path, '下载id')
  const fileUrl = path.includes('http') ? path : config.downloadFileUrl + '/' + path
  window.open(fileUrl)
}

// const taskModuleType = (taskModule: string) => {
//   return taskModule == '0' ? '学习进度不到100%也能考试' : '学习进度必须100%才能考试'
// }

onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<style lang="scss" scoped>
.popup-main {
  height: calc(100% - 70px);

  .form_wrap {
    padding: 20px;
  }

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #303133;
    }
  }

  .text_color {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #527cff;
    width: 70px;
    height: 30px;
    border: 1px solid rgb(82, 124, 255);
    border-radius: 5px;
    text-align: center;
    line-height: 30px;
  }
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  padding: 20px;
  margin: 24px;
  margin-bottom: 0;

  &:nth-child(2) {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  .w_file_btn {
    width: 136px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    color: #527cff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
  }

  //background: red;
  .w_file_t {
    color: #a8abb2;
  }

  .w_text_file_box {
    display: flex;
    justify-content: start;
    align-items: baseline;
    // height: 20px;
    // line-height: 20px;
    color: rgba(82, 124, 255, 1) !important;
    margin-bottom: 12px;

    .s_file {
      height: 30px;
      line-height: 20px;
    }
  }

  .w_text_div {
    display: flex;
    color: rgba(82, 124, 255, 1);
    align-items: center;

    .w_text_left {
      margin-right: 20px;
    }
  }

  :deep(.el-form-item__label::before) {
    color: #f56c6c;
    content: '*';
    margin-right: 4px;
  }
}

.exam-list-box {
  height: calc(100vh - 300px);
  overflow: hidden;
  // overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-top: 15px;
  margin-bottom: 10px;

  .exam-list {
    @apply ml-[30px] mb-[15px] pl-[16px] pb-[10px] pt-[10px] flex w-full;
    background: #ffffff;
    border: 1px solid #ebeef5;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .exam-type {
      font-weight: 500;
      font-size: 16px;
      color: #313233;
      margin-bottom: 10px;
    }

    .exam-time {
      font-weight: 400;
      font-size: 14px;
      color: #484a4d;
      margin-right: 40px;
    }
  }

  .btn {
    margin-right: 10px;
  }

  :deep(.el-select__placeholder.is-transparent) {
    color: #1e1e1e;
  }

  :deep(.el-radio) {
    margin-right: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-67%);
  }
}

.bottom_wrap {
  border-top: 1px solid #ebeef5;
  box-sizing: border-box;
  padding: 20px 20px 0;
  text-align: right;

  .el-button--primary {
    background-color: #527cff;
    border-color: #527cff;
  }
}

.btn-page {
  width: 100%;
  margin-top: 20px;
  padding-right: 20px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px 4px 4px 4px;
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  border: 1px #335cff solid;
  border-radius: 4px;
}

:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .btn-next) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.popup-container) {
  z-index: 2013;
}

:deep(.el-radio-group) {
  display: block;
}
</style>
