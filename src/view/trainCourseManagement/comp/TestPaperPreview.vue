<template>
  <div class="container-view" v-loading="loading">
    <div class="overflow-hidden examContent">
      <el-scrollbar>
        <div class="text-center">
          <div class="name">试卷名称: {{ examInfo.examName }}</div>
          <div class="title">
            <span class="mr-[30px]">总题数：{{ examInfo.count }}题</span>
            <span class="mr-[30px]" v-if="singleList.length">单选题：{{ singleList.length }}题</span>
            <span class="mr-[30px]" v-if="multipleSelectList.length">多选题：{{ multipleSelectList.length }}题</span>
            <span class="mr-[30px]" v-if="judgeList.length">判断题：{{ judgeList.length }}题</span>
            <span class="mr-[30px]" v-if="fillBlankList.length">填空题：{{ fillBlankList.length }}题</span>
            <span>总分：{{ examInfo.totalScore }}分</span>
          </div>
        </div>
        <div class="exam-list-box">
          <!-- 单选题 -->
          <div class="single" v-if="examTypeList[0]?.quesCount">
            {{ getNameTitle('单选题') }}、单选题（总共{{ examTypeList[0]?.quesCount }}题， 每题{{
              examTypeList[0]?.quesScore
            }}分， 共{{ examTypeList[0]?.totalScore }}分）
          </div>
          <div v-for="(item, index) in singleList" :key="index" class="exam-list">
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex">{{ getLetter(sub) }}、 <span v-html="option.optionText"></span></p>
            </div>
          </div>
          <!-- 多选题 -->
          <div class="single" v-if="multipleSelectList.length != 0">
            {{ getNameTitle('多选题') }}、多选题（总共{{ examTypeList[1]?.quesCount }}题， 每题{{
              examTypeList[1]?.quesScore
            }}分， 共{{ examTypeList[1]?.totalScore }}分）
          </div>
          <div v-for="(item, index) in multipleSelectList" :key="index" class="exam-list">
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex">{{ getLetter(sub) }}、 <span v-html="option.optionText"></span></p>
            </div>
          </div>
          <!-- 判断题 -->
          <div class="single" v-if="examTypeList[1]?.quesCount">
            {{ getNameTitle('判断题') }}、判断题（总共{{ examTypeList[1]?.quesCount }}题， 每题{{
              examTypeList[1]?.quesScore
            }}分， 共{{ examTypeList[1]?.totalScore }}分）
          </div>
          <div v-for="(item, index) in judgeList" :key="index" class="exam-list">
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
            <div v-for="(option, sub) in item.answerOptionList" :key="option.id">
              <p class="mb-[10px] flex">{{ getLetter(sub) }}、<span v-html="option.optionText"></span></p>
            </div>
          </div>
          <!-- 填空题 -->
          <div class="single" v-if="fillBlankList.length != 0">
            {{ getNameTitle('填空题') }}、填空题（总共{{ examTypeList[3]?.quesCount }}题， 每空{{
              examTypeList[3]?.quesScore
            }}分， 共{{ examTypeList[3]?.totalScore }}分）
          </div>
          <div v-for="(item, index) in fillBlankList" :key="index" class="exam-list">
            <div class="isRight mb-[10px] flex">{{ index + 1 }}、<span v-html="item.title"></span></div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import $API from '~/common/api'

interface optionList {
  id: string
  isRight: number
  optionText: string
}
interface list {
  examType: string
  createTime: string
  title: string
  answerOptionList: [optionList]
  id: string
}
interface examType {
  quesCount: number
  quesScore: number
  totalScore: number
}
const props = defineProps({
  type: {
    type: Number,
    default: 1,
  },
  id: {
    type: String,
    default: '',
  },
})
const examInfo = ref({
  count: 0,
  examName: '',
  totalScore: 0,
}) // 试卷头部信息
const loading = ref(false)
const examTypeList = ref<examType[]>([]) // 单选多选汇总
const singleList = ref<list[]>([])
const judgeList = ref<list[]>([])
const fillBlankList = ref<list[]>([])
const multipleSelectList = ref<list[]>([])
const questions = ref<string[]>([]) // 题目列表

const getLetter = (index) => {
  const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G']
  return letters[index] || ''
}

// 获取试卷详情
const getDetailById = (id = '06fe55e1c59c4fdeb2133f6441586c2f') => {
  loading.value = true
  let params = {
    id,
  }
  $API
    .get({
      url: 'train-server/examPaper/queryExamPaperById',
      params,
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        // const { examPaper, examScoreSettings, singleSelectList, judgeList } = res.data
        loading.value = false
        examInfo.value = res.data.examPaper
        examTypeList.value = res.data.examScoreSettings
        singleList.value = res.data.singleSelectList
        judgeList.value = res.data.judgeList
        fillBlankList.value = res.data.fillBlankList
        multipleSelectList.value = res.data.multipleSelectList
        questions.value = res.data.examPaper.questions.split(',')
      }
    })
}

const arrCom = computed(() => {
  let Arr = [
    {
      name: '单选题',
      num: singleList.value.length,
    },
    {
      name: '多选题',
      num: multipleSelectList.value.length,
    },
    {
      name: '判断题',
      num: judgeList.value.length,
    },
    {
      name: '填空题',
      num: fillBlankList.value.length,
    },
  ]

  return Arr.filter((item) => item.num > 0)
})

function getNameTitle(name: string) {
  let arr = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  let index = arrCom.value.findIndex((item) => item.name == name)
  return arr[index]
}

watch(
  () => props.id,
  (val) => {
    if (val) {
      getDetailById(val)
    }
  },
  { immediate: true, deep: true }
)
onMounted(() => {
  // getDetailById()
})
</script>

<style lang="scss" scoped>
.container-view {
  width: 100% !important;
  height: 100%;
  overflow: hidden;
  z-index: 99;
}

.examContent {
  height: calc(100% - 15px);
  padding-top: 10px;
  padding-left: 30px;
}

.name {
  font-weight: 600;
  font-size: 36px;
  color: #313233;
}

.title {
  font-weight: 400;
  font-size: 16px;
  color: #313233;
}

.single {
  margin-top: 40px;
  font-weight: 600;
  font-size: 24px;
  color: #313233;
  margin-bottom: 20px;
}

.exam-list {
  position: relative;
  background: #ffffff;
  border: 1px solid #ebeef5;
  padding-left: 10px;
  padding-top: 10px;
  margin-bottom: 20px;
  margin-right: 20px;

  .changeBtn {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
  }
}

.isRight {
  font-weight: 500;
  font-size: 16px;
  color: #313233;
}
</style>
