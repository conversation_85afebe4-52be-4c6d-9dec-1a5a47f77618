<template>
  <div class="relative popup-main" v-loading="loading">
    <div class="h-full">
      <el-scrollbar>
        <div class="w_dialog_from_box">
          <div class="mb-[22px] bg_title">培训课程信息</div>
          <el-form :model="formInline" class="" ref="ruleFormRef" :rules="baseRule" label-width="130px">
            <el-form-item label="培训课程名称：" prop="curriculumName">
              <el-input
                v-model="formInline.curriculumName"
                placeholder="请输入培训课程名称"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>

            <el-form-item
              v-if="trainType === '1'"
              :label="ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型'"
              prop="relatedType"
            >
              <el-radio-group v-model="formInline.relatedType">
                <el-radio
                  v-for="item in relevantTypeList"
                  :key="item.id"
                  :value="item.id"
                  :disabled="props.operate !== 'add'"
                  @change="changeRadio(item.name)"
                >
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="trainType === '2'" label="适用项目类型" prop="projectType">
              <el-radio-group v-model="formInline.projectType">
                <el-radio
                  v-for="item in syxmlxOpt"
                  :key="item.dictValue"
                  :value="item.dictValue"
                  :disabled="props.operate !== 'add'"
                  @change="changeProjectType(item.dictLabel)"
                >
                  {{ item.dictLabel }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="培训资料：" prop="fileVoList">
              <el-input v-model="formInline.fileName" v-show="false"></el-input>
              <UploadFiles
                ref="uploadFilesRef"
                v-model="formInline.fileVoList"
                @change="handleFileChange"
                :isShow="false"
                @uploading="updateUploadingStatus"
              />
            </el-form-item>
            <div class="w-full mt-[16px] h-[360px]">
              <el-scrollbar max-height="360px">
                <el-table :data="fileTableData" stripe heigth="360px" v-loading="isLoading">
                  <el-table-column type="index" width="60" label="序号" align="center" />
                  <el-table-column
                    v-for="(item, index) in columns"
                    :prop="item.key"
                    :label="item.title"
                    align="center"
                    :width="item.width"
                    :key="index"
                    :show-overflow-tooltip="item.showOverflowTooltip"
                  >
                    <template #default="scope" v-if="item.key === 'videoTime'">
                      <div v-if="scope.row.fileType.toLowerCase().includes('mp4')">
                        {{ scope.row.videoTimeMp4 || '--' }}
                      </div>
                      <div class="w-full" v-else>
                        <div class="flex items-center justify-center">
                          <el-input-number
                            style="width: 80px !important"
                            v-model="scope.row.videoTimePdf"
                            :min="1"
                            :max="9999"
                            :controls="false"
                            :precision="0"
                            placeholder="请输入"
                            @change="handlePdfTimeChange(scope.row)"
                          />
                          分钟
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="action" label="操作" width="240" align="center">
                    <template #default="scope">
                      <el-button type="primary" @click="deleteRow(scope.$index, scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-scrollbar>
            </div>
          </el-form>
        </div>
        <div class="w_dialog_from_box">
          <div class="mb-[22px] bg_title">培训考试</div>
          <el-form :model="formInline2" ref="ruleFormRef2" :rules="rules2" label-width="130px">
            <el-form-item label="是否考试：" prop="isExam">
              <el-radio-group
                v-model="formInline2.isExam"
                placeholder="请选择是否考试"
                prop="trainType"
                :disabled="props.operate !== 'add'"
              >
                <el-radio value="1">是</el-radio>
                <el-radio value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="formInline2.isExam === '1'">
              <div v-if="props.operate === 'add'">
                <el-form-item label="考试试卷：" prop="examPaperName">
                  <div class="exam_paper">
                    <el-input
                      v-model="formInline2.examPaperName"
                      placeholder="请选择考试试卷"
                      @click="chooseExam"
                      readonly
                    >
                    </el-input>
                    <el-button type="primary" plain @click="chooseExam">选择</el-button>
                  </div>
                </el-form-item>
              </div>
              <div v-else>
                <el-form-item label="考试试卷：" prop="examPaperName">
                  <div class="w_text_div">
                    <div class="w_text_left" v-if="formInline2.examPaperName.length > 30">
                      <el-tooltip effect="dark" :content="formInline2.examPaperName" placement="top-start">
                        {{ formInline2.examPaperName.slice(0, 10) + '...' }}
                      </el-tooltip>
                    </div>
                    <div class="w_text_left" v-else>
                      {{ formInline2.examPaperName }}
                    </div>
                    <div class="mr-5 text_color" @click="previewTestPaper(formInline2.examPaperId)">预览</div>
                    <div class="mr-5 text_color" @click="fileDown(formInline2.examPaperId)">下载</div>
                  </div>
                </el-form-item>
              </div>
              <el-form-item label="考试时长：" prop="examTime">
                <div class="time_input">
                  <el-input v-model="formInline2.examTime" @input="handleInput('examTime')" placeholder="请输入">
                  </el-input>
                  <span>分钟</span>
                </div>
              </el-form-item>
              <el-form-item label="最多考试次数：" prop="examCount">
                <div class="time_input">
                  <el-input v-model="formInline2.examCount" @input="handleInput('examCount')" placeholder="请输入">
                  </el-input>
                  <span>次</span>
                </div>
              </el-form-item>
              <div v-if="props.operate === 'add'" class="text-[#222]">
                <el-form-item label="评估方式：" prop="passScore">
                  <span class="mr-[10px]">达到</span>
                  <div class="score_input mr-[10px] w-[110px]">
                    <el-input v-model="formInline2.passScore" @input="handleInput('passScore')" placeholder="请输入">
                    </el-input>
                  </div>
                  <span>分，通过</span>
                </el-form-item>
              </div>
              <div v-else>
                <el-form-item label="评估方式：" prop="passScore">
                  <span class="text">达到{{ data.passScore }}分通过</span>
                </el-form-item>
              </div>
              <!-- <el-form-item label="考试设定：" prop="taskModule">
                <el-radio-group v-model="formInline2.taskModule">
                  <el-radio value="1" :disabled="props.operate === 'edit'">学习进度必须100%才能考试</el-radio>
                  <el-radio value="0" :disabled="props.operate === 'edit'">学习进度不到100%也能考试</el-radio>
                </el-radio-group>
              </el-form-item> -->
            </div>
          </el-form>
        </div>
      </el-scrollbar>
      <div class="bottom_wrap">
        <el-button @click="close" plain :disabled="loading">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading" :disabled="isLoading">
          {{ submitLoading ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </div>

    <ExamPaper v-model:dialogVisible="dialogVisible" @submit="submitId" :id="formInline2.examPaperId" />
    <!-- 预览试卷弹框 -->
    <popup-side v-model="perviewVisible" width="920px" popupTitle="试卷预览" @close="closeFn">
      <TestPaperPreview :id="examID" />
    </popup-side>
  </div>
</template>

<script setup lang="ts">
import ExamPaper from '@/components/myPaperBank/index.vue'
import UploadFiles from '@/components/uploadFiles/index2.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import $API from '~/common/api'
import config from '~/config'
import { useUserInfo } from '~/store'
import TestPaperPreview from './TestPaperPreview.vue'
import { columns } from './colums'
import { formatFileList } from './method'

const ui = useUserInfo()
const props = defineProps({
  operate: {
    type: String,
    default: 'add'
  },
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  id: {
    type: String,
    default: ''
  },
  orgCode: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  trainType: {
    type: String,
    default: '' // 培训类型 1: 新人必学课程 2： 项目进场课程
  }
})
const loading = ref(false)
const perviewVisible = ref(false) // 预览试卷
const examID = ref('') // 选取的试卷id
const emit = defineEmits(['close'])
const dialogVisible = ref(false) // 试卷库
const formInline = ref({
  curriculumName: '', // 培训课程名称
  relatedType: '',
  relatedTypeName: '',
  projectType: '',
  projectTypeName: '',
  fileName: '',
  trainingMaterials: '',
  fileVoList: [] as any
})
const formInline2 = ref({
  isExam: '1', // 请选择是否考试s
  examTime: '', // 考试时长
  examCount: '', // 考试次数
  passScore: '', // 分
  examPaperId: '', //试卷id
  examPaperName: ''
  // taskModule: '0'
})
const relevantTypeList = ref<any>([])
const syxmlxOpt = ref<any>([]) // 适用项目类型
const ruleFormRef = ref<FormInstance>()
const ruleFormRef2 = ref<FormInstance>()
// 文件上传列表
const fileList = ref<any>([])
const validateFile = (rule: any, value: any, callback: any) => {
  if (formInline.value.fileVoList[0]?.id && formInline.value.fileVoList.length > 0) {
    // 检查每个文件对象是否有url属性
    const isValid = formInline.value.fileVoList.every((file: any) => file.courseFileUrl || file.url)
    if (isValid) {
      callback()
    } else {
      callback('请完成文件上传')
      formInline.value.fileVoList = []
    }
  } else {
    callback('请上传培训资料')
  }
}
const baseRule = reactive<FormRules>({
  curriculumName: [
    {
      required: true,
      message: '请输入培训课程名称',
      trigger: ['blur', 'change']
    }
    // {
    //   min: 3,
    //   max: 20,
    //   message: '请输入名字数大于3小于20',
    //   trigger: ['blur', 'change'],
    //   informType: 'warning',
    // },
  ],
  relatedType: [
    {
      required: true,
      message: ui.value.zhLogo === 'yanchang' ? '请选择承包商类型' : '请选择相关方类型',
      trigger: 'change'
    }
  ],
  projectType: [
    {
      required: true,
      message: '请选择适用项目类型',
      trigger: 'change'
    }
  ],
  fileName: [
    {
      required: true,
      message: '请上传培训资料',
      trigger: 'change'
    }
  ],
  fileVoList: [{ required: true, validator: validateFile, trigger: ['change'] }]
})
// 自定义校验次数
const validateNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('输入不能为空'))
  } else {
    if (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 <= 9999) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('请输入大于0的正整数')
    }
  }
}
const validateNum1 = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入分数'))
  } else {
    if (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 <= 9999) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('请输入大于0的正整数')
    }
  }
}
const rules2 = reactive<FormRules>({
  isExam: [
    {
      required: true,
      message: '请选择是否考试',
      trigger: 'change'
    }
  ],
  examPaperName: [
    {
      required: true,
      message: '请选择试卷',
      trigger: ['blur', 'change']
    }
  ],
  examTime: [
    {
      required: true,
      message: '请输入考试时长',
      trigger: ['blur', 'change']
    },
    { validator: validateNum, trigger: ['blur', 'change'] }
  ],
  examCount: [
    {
      required: true,
      message: '请输入最多考试次数',
      trigger: ['blur', 'change']
    },
    { validator: validateNum, trigger: ['blur', 'change'] }
  ],
  passScore: [
    {
      required: true,
      message: '请输入分数',
      trigger: ['blur', 'change']
    },
    { validator: validateNum1, trigger: ['blur', 'change'] }
  ]
  // taskModule: [
  //   {
  //     required: true,
  //     message: '请选择考试设定',
  //     trigger: 'change'
  //   }
  // ]
})
const isLoading = ref(false)
// 更新上传状态
const updateUploadingStatus = (status: boolean) => {
  isLoading.value = status
}
const uploadFilesRef = ref()
const fileTableData = ref<any>([])
// 使用Map集合存储PDF文件时长，key为filePath，value为时长
const pdfFileDurationMap = new Map<string, number>()
// 处理PDF文件时长变化
const handlePdfTimeChange = (row: any) => {
  console.log('PDF时长发生变化:', row)
  // 如果不是PDF文件，直接返回
  if (!row.fileType || row.fileType.toLowerCase().includes('mp4')) {
    return
  }
  // 更新Map中的时长
  if (row.filePath && row.videoTimePdf) {
    pdfFileDurationMap.set(row.filePath, row.videoTimePdf)
    console.log('更新PDF时长Map:', pdfFileDurationMap)
  }
}
const handleFileChange = (files: any[]) => {
  console.log('files', files)
  formInline.value.fileVoList = files as any
  // 格式化文件列表
  const formattedFiles = formatFileList(files)

  // 处理每个文件的时长
  formattedFiles.forEach((file: any) => {
    if (file.fileType && file.fileType.toLowerCase().includes('mp4')) {
      // MP4文件保持原有时长
      file.videoTime = file.videoTimes
    } else {
      // PDF文件从Map中获取之前保存的时长，如果没有则设为默认2分钟
      const savedDuration = pdfFileDurationMap.get(file.filePath)
      file.videoTimePdf = savedDuration || 2
      // 如果是新文件，将其添加到Map中
      if (!savedDuration) {
        pdfFileDurationMap.set(file.filePath, 2)
      }
    }
  })

  fileTableData.value = formattedFiles
  console.log('当前PDF时长Map:', pdfFileDurationMap)
  console.log('选择的文件==》', fileTableData.value)
}
// 删除
const deleteRow = (index: any, item?: any) => {
  console.log('index', index, item)
  pdfFileDurationMap.delete(item.filePath)
  uploadFilesRef.value?.handleRemove(item, index)
}
// 弹出选择试卷
const chooseExam = () => {
  dialogVisible.value = true
}
// 输入正整数校验
const handleInput = (field) => {
  // 使用正则表达式过滤非正整数的输入
  if (!/^[1-9]\d*$/.test(formInline2.value[field]) && formInline2.value[field] !== '') {
    formInline2.value[field] = formInline2.value[field].replace(/[^0-9]/g, '')
  }
}
// 获取选择试卷的所有信息
const submitId = (obj: any) => {
  formInline2.value.examPaperName = obj.examName
  formInline2.value.examPaperId = obj.id
}

// 添加上传类型的引用
const uploadType = window.$SYS_CFG.uploadType
const isLocalUpload = computed(() => {
  return uploadType === 'wych'
})
const isOssUpload = computed(() => {
  return uploadType === 'oss'
})

const changeRadio = (name) => {
  formInline.value.relatedTypeName = name
}
const changeProjectType = (name) => {
  formInline.value.projectTypeName = name
}
// 关闭新增或删除
const close = () => {
  if (props.operate === 'add') {
    ruleFormRef.value?.resetFields()
    ruleFormRef2.value?.resetFields()
  }
  emit('close')
}
// 新增/编辑 提交
const add = async () => {
  try {
    if (fileList.value && isLocalUpload) {
      let fileIdArr: string[] = []
      fileList.value.forEach((item: any) => {
        fileIdArr.push(item.response.data.id as string)
      })
      formInline.value.trainingMaterials = fileIdArr.join(',')
    }
    if (formInline.value.fileVoList && isOssUpload.value) {
      let fileIdArr: string[] = []
      formInline.value.fileVoList.forEach((item: any) => {
        fileIdArr.push(item.id as string)
        fileTableData.value.forEach((t: any) => {
          if (t.filePath === item.url) {
            if (t.fileType === '.pdf') {
              t.videoTime = t.videoTimePdf * 60
              item.videoTimePdf = t.videoTimePdf
            } else {
              item.videoTimeMp4 = t.videoTimeMp4
            }
          }
        })
      })
      let hasVideoTime = false
      const missingVideoTimeFiles: number[] = []
      fileTableData.value.forEach((t: any, index: number) => {
        hasVideoTime = false
        const isMp4 = t.fileType.toLowerCase().includes('mp4')
        const isPdf = t.fileType.toLowerCase().includes('pdf')
        // 检查PDF文件是否缺少学习时长
        if (isMp4 || (isPdf && t.videoTime > 0)) {
          hasVideoTime = true
        }
        // 如果没有找到对应的学习时长，记录文件序号
        if (!hasVideoTime) {
          missingVideoTimeFiles.push(index + 1)
        }
      })
      // 如果有文件缺少学习时长，提示用户
      if (missingVideoTimeFiles.length > 0) {
        const fileNumbers = missingVideoTimeFiles.join('、')
        ElMessage.error(`第${fileNumbers}个文件没有填写学习时长，请补充后再提交`)
        return
      }
      formInline.value.trainingMaterials = fileIdArr.join(',')
    }
    console.log('formInline.value', formInline.value, fileTableData.value)
    let addData = {
      ...formInline.value,
      ...formInline2.value,
      unitId: props.orgCode,
      unitName: props.name,
      trainType: props.trainType,
      realFileList: fileTableData.value
    }
    let updateData = {
      ...formInline.value,
      ...formInline2.value,
      id: props.id,
      trainType: props.trainType,
      realFileList: fileTableData.value
    }

    let res: any = await $API.post({
      method: 'post',
      url:
        props.operate === 'add'
          ? 'edu-inter-server/train/curriculum/manage/saveCurriculum'
          : 'edu-inter-server/train/curriculum/manage/updateCurriculum',
      data: props.operate === 'add' ? addData : updateData
    })

    if (res.code == 'success') {
      props.operate === 'add' ? ElMessage.success('新增成功') : ElMessage.success('编辑成功')
      emit('close')
    }
  } catch (error) {
    console.error('操作失败:', error)
  }
}
const submitLoading = ref(false) // 按钮loading
const submitForm = async () => {
  if (submitLoading.value) return
  try {
    await ruleFormRef.value?.validate()
    if (!formInline.value.fileVoList.length && isOssUpload.value) {
      ElMessage.error('培训资料不能为空！')
      return
    }
    await ruleFormRef2.value?.validate()
    submitLoading.value = true
    await add()
  } catch (error) {
    console.error('提交失败:', error)
    submitLoading.value = false
  } finally {
    submitLoading.value = false
  }
}
// 获取相关方类型
const getRelatedType = async () => {
  loading.value = true
  let params = {
    pageNo: 1,
    pageSize: -1
  }
  const res: any = await $API.get({
    url: 'edu-inter-server/coop/getCoopTypeList',
    params
  })
  if (res.code == 'success') {
    loading.value = false
    relevantTypeList.value = res.data.rows
  }
}
// 获取适用项目类型
const getDict = () => {
  if (props.trainType !== '2') return
  $API
    .get({
      url: 'atomic-upms-service/common/v1/queryDictList',
      params: {
        type: 'xmsg_xmlx'
      }
    })
    .then((res: any) => {
      if (+res.code === 200) {
        syxmlxOpt.value = res.data
      }
    })
}

// 下载
const fileDown = (id) => {
  const url = config.base_url + 'train-server/examPaper/viewExam?examId=' + id
  const link = document.createElement('a')
  link.href = url
  link.download = ''
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
// 预览
const previewTestPaper = (id) => {
  examID.value = id
  perviewVisible.value = true
}
const closeFn = () => {
  perviewVisible.value = false
}
// 编辑更新属性
const updateObj = (formObj, data) => {
  Object.keys(formObj).forEach((key) => {
    // eslint-disable-next-line no-prototype-builtins
    if (data.hasOwnProperty(key)) {
      formObj[key] = data[key]
    }
  })
}

watch(
  () => props.data,
  (val) => {
    if (val && props.operate === 'edit') {
      updateObj(formInline.value, val)
      updateObj(formInline2.value, val)
      formInline.value.fileVoList = val.fileArrList
      const formattedFiles = formatFileList(val.fileArrList)
      // 将文件映射到pdfFileDurationMap中
      formattedFiles.forEach((file: any) => {
        if (file.fileType && !file.fileType.toLowerCase().includes('mp4')) {
          pdfFileDurationMap.set(file.filePath, file.videoTimePdf || 2)
        }
      })
      fileTableData.value = formattedFiles
      console.log(fileTableData.value)
    }
  },
  { immediate: true }
)
watch(
  () => fileList.value.length,
  (val) => {
    if (val > 0) {
      formInline.value.fileName = val + ''
    } else {
      formInline.value.fileName = ''
    }
  },
  { immediate: true }
)
getDict()
onMounted(() => {
  getRelatedType()
})
</script>

<style lang="scss" scoped>
.popup-main {
  height: calc(100% - 60px);

  .form_wrap {
    padding: 20px;
  }
}

.error-message {
  color: red;
  font-size: 12px;
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  // border-top: none;
  padding: 20px;
  margin: 24px;
  margin-bottom: 0;

  &:nth-child(2) {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;
  }

  .w_file_btn {
    width: 136px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    color: #527cff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
  }

  //background: red;
  .w_file_t {
    color: #a8abb2;
  }

  .w_text_div {
    display: flex;
    align-items: center;

    .w_text_left {
      margin-right: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
    }

    .text_color {
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
      width: 70px;
      height: 30px;
      border: 1px solid rgb(82, 124, 255);
      border-radius: 5px;
      text-align: center;
      line-height: 30px;
    }
  }

  .exam_paper {
    width: 100%;
    display: flex;
    justify-content: space-between;

    :deep(.el-input) {
      // width: 79%;
    }
  }

  .time_input {
    :deep(.el-input) {
      display: inline;
      margin-right: 10px;
    }
  }

  .text {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
  }

  .upload_file {
    :deep(.el-form-item__label::before) {
      color: #f56c6c;
      content: '*';
      margin-right: 4px;
    }
  }

  :deep(.el-button--primary.is-plain) {
    margin-left: 10px;
  }
}

.exam-list-box {
  height: calc(100vh - 300px);
  overflow: hidden;
  // overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-top: 15px;
  margin-bottom: 10px;

  .exam-list {
    @apply ml-[30px] mb-[15px] pl-[16px] pb-[10px] pt-[10px] flex w-full;
    background: #ffffff;
    border: 1px solid #ebeef5;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .exam-type {
      font-weight: 500;
      font-size: 16px;
      color: #313233;
      margin-bottom: 10px;
    }

    .exam-time {
      font-weight: 400;
      font-size: 14px;
      color: #484a4d;
      margin-right: 40px;
    }
  }

  .btn {
    margin-right: 10px;
  }

  :deep(.el-select__placeholder.is-transparent) {
    color: #1e1e1e;
  }

  :deep(.el-radio) {
    margin-right: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-67%);
  }
}

.bottom_wrap {
  border-top: 1px solid #ebeef5;
  box-sizing: border-box;
  padding: 10px 20px;
  text-align: right;

  .el-button--primary {
    background-color: #527cff;
    border-color: #527cff;
  }
}

.btn-page {
  width: 100%;
  margin-top: 20px;
  padding-right: 20px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px 4px 4px 4px;
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  border: 1px #335cff solid;
  border-radius: 4px;
}

:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .btn-next) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.el-overlay) {
  z-index: 2013 !important;
}

:deep(.popup-container) {
  z-index: 2099 !important;
}

:deep(.el-radio-group) {
  display: block;
}

:deep(.el-upload-list.el-upload-list--text) {
  max-width: 330px;
}

.upload_file {
  :deep(.el-form-item__content) {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
