import { ElMessage, FormRules, UploadProps } from 'element-plus'
import { reactive, ref } from 'vue'

export const isDelete = ref(false)

/**
 * 上传文件之前的校验函数
 *
 * @param rawFile 待上传的文件对象
 * @returns 校验结果，通过返回 true，不通过返回 false
 */
export const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png']
  const MAX_SIZE_MB = 10
  const MB_TO_BYTES = 1024 * 1024

  isDelete.value = false

  if (!rawFile.type || !allowedTypes.includes(rawFile.type)) {
    isDelete.value = true
    ElMessage.error('仅png，jpg图片格式文件')
    return false
  } else if (rawFile.size > MAX_SIZE_MB * MB_TO_BYTES) {
    isDelete.value = true
    ElMessage.error('图片大小不能大于10MB!')
    return false
  }
  return true
}

// 将格式化的时长字符串解析为秒数
export const parseDurationToSeconds = (durationStr: string): number => {
  console.log('durationStr', durationStr)
  const dur = String(durationStr)
  if (!dur) return 120 // 默认2分钟

  // 处理没有单位的情况
  if (!dur.includes('分') && !dur.includes('秒')) {
    return parseInt(dur) * 60 || 0
  }

  // 处理只有秒的情况，如"30秒"
  if (dur.endsWith('秒') && !dur.includes('分')) {
    return parseInt(dur.replace('秒', '')) || 0
  }

  // 处理只有分钟的情况，如"2分钟"
  if (dur.endsWith('分钟') && !dur.includes('秒')) {
    return parseInt(dur.replace('分钟', '')) * 60 || 0
  }

  // 处理分钟和秒的情况，如"1分30秒"
  let minutes = 0
  let seconds = 0

  const minutesMatch = dur.match(/(\d+)分/)
  if (minutesMatch) {
    minutes = parseInt(minutesMatch[1]) || 0
  }

  const secondsMatch = dur.match(/(\d+)秒/)
  if (secondsMatch) {
    seconds = parseInt(secondsMatch[1]) || 0
  }

  return minutes * 60 + seconds
}

/**
 * 处理文件上传数量超过限制的情况
 *
 * @returns 返回 false 以阻止文件上传
 */
export function handleExceed() {
  ElMessage.error('课程封面只允许上传一张图片!')
  return false
}

export const validateNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('输入不能为空'))
  } else {
    if (
      (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 < 9999) ||
      (value && !isNaN(value) && value - 0 == 9999)
    ) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('请输入大于0的正整数')
    }
  }
}

export const validateNum1 = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入分数'))
  } else {
    if (
      (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 < 9999) ||
      (value && !isNaN(value) && value - 0 == 9999)
    ) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('请输入大于0的正整数')
    }
  }
}

export const validateNum2 = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请输入最多考试次数'))
  } else {
    if (
      (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 < 9999) ||
      (value && !isNaN(value) && value - 0 == 9999)
    ) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('最多考试次数需填写大于等于1的整数')
    }
  }
}

export const validateNum3 = (rule: any, value: any, callback: any) => {
  console.log(rule)

  if (!value) {
    callback(new Error('请输入培训任务时长'))
  } else {
    if (
      (value && !isNaN(value) && value > 0 && Number.isInteger(value - 0) && value - 0 < 9999) ||
      (value && !isNaN(value) && value - 0 == 9999)
    ) {
      callback()
    } else if (value > 0 && value - 0 > 9999) {
      callback('输入值不能大于9999')
    } else {
      callback('请输入大于0的正整数')
    }
  }
}

export const rules2 = reactive<FormRules>({
  isExam: [
    {
      required: true,
      message: '请选择是否考试',
      trigger: 'change'
    }
  ],
  examPaperName: [
    {
      required: true,
      message: '请选择试卷',
      trigger: ['blur', 'change']
    }
  ],
  examTime: [
    {
      required: true,
      message: '请输入考试时长',
      trigger: ['blur', 'change']
    },
    { validator: validateNum, trigger: ['blur', 'change'] }
  ],
  examCount: [
    {
      required: true,
      message: '请输入考试次数',
      trigger: ['blur', 'change']
    },
    { validator: validateNum2, trigger: ['blur', 'change'] }
  ],
  passScore: [
    {
      required: true,
      message: '请输入分数',
      trigger: ['blur', 'change']
    },
    { validator: validateNum1, trigger: ['blur', 'change'] }
  ],
  taskModule: [
    {
      required: true,
      message: '请选择考试设定',
      trigger: 'change'
    }
  ]
})

export const formatFileList = (fileArrList: any[]) => {
  return fileArrList.flatMap((item) => {
    if (item.businessType === 'courseware') {
      return [
        {
          id: item.fileId,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
          businessType: item.businessType,
          videoTime: item.studyDuration || 120,
          videoTimes: item.studyDuration ? formatSeconds(+item.studyDuration) : '2分'
        }
      ]
    } else if (item.businessType === 'law' && item.documentList) {
      return item.documentList.map((doc) => ({
        id: doc.id,
        fileName: doc.documentName,
        filePath: doc.documentAddr,
        fileSize: doc.documentSize,
        fileType: doc.documentAddr.split('.').pop(),
        videoTime: parseDurationToSeconds(item.videoTimePdf)
      }))
    } else if (item.businessType === 'emergency') {
      return [
        {
          id: item.id,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
          videoTime: parseDurationToSeconds(item.videoTimePdf)
        }
      ]
    } else if (!item.businessType) {
      return [
        {
          id: item.url || item.fileId || item.id,
          fileName: item.fileName,
          filePath: item.url,
          fileSize: item.fileSize,
          fileType: item.isPdf ? '.pdf' : '.mp4',
          videoTimeMp4: item.duration ? formatSeconds(+item.duration) : 0,
          videoTimePdf: item.videoTimePdf || 2,
          videoTime: item.duration || 0
        }
      ]
    }
    return []
  })
}
// 处理提交数据
export const formatFileList2 = (fileArrList: any[]) => {
  return fileArrList.flatMap((item) => {
    if (item.businessType === 'courseware') {
      return [
        {
          id: item.fileId,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
          businessType: item.businessType,
          videoTime: item.fileType.toLowerCase().includes('mp4')
            ? item.videoTime
            : parseDurationToSeconds(item.videoTimePdf.toString())
        }
      ]
    } else if (item.businessType === 'law' && item.documentList) {
      return item.documentList.map((doc) => ({
        id: doc.id,
        fileName: doc.documentName,
        filePath: doc.documentAddr,
        fileSize: doc.documentSize,
        fileType: doc.documentAddr.split('.').pop(),
        videoTime: parseDurationToSeconds(item.videoTimePdf)
      }))
    } else if (item.businessType === 'emergency') {
      return [
        {
          id: item.id,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
          videoTime: parseDurationToSeconds(item.videoTimePdf)
        }
      ]
    } else if (!item.businessType) {
      return [
        {
          id: item.url || item.fileId || item.id,
          fileName: item.fileName,
          filePath: item.url,
          fileSize: item.fileSize,
          fileType: item.isPdf ? '.pdf' : '.mp4',
          videoTime: parseDurationToSeconds(item.videoTimePdf)
        }
      ]
    }
    return []
  })
}

// 回显表格
export const formatFileList1 = (fileArrList: any[]) => {
  return fileArrList.flatMap((item) => {
    if (item.businessType === 'courseware') {
      return [
        {
          fileId: item.fileId,
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
          businessType: item.businessType,
          videoTime: item.studyDuration || 120,
          videoTimes: item.studyDuration ? formatSeconds(+item.studyDuration) : '2分',
          videoTimePdf: item.videoTimePdf ? item.videoTimePdf : formatSeconds(+item.studyDuration).split('分')[0]
        }
      ]
    } else if (item.businessType === 'law' && item.documentList) {
      return [
        {
          id: item.id,
          fileType: 'pdf',
          documentList: item.documentList,
          fileName: item.lawsRegulationsName || item.fileName,
          businessType: item.businessType,
          videoTimePdf: item.videoTimePdf || 2
        }
      ]
    } else if (item.businessType === 'emergency') {
      return [
        {
          id: item.id,
          fileName: item.planName,
          filePath: item.planFile,
          fileSize: item.documentSize,
          fileType: item?.planFile.split('.').pop(),
          businessType: item.businessType,
          videoTimePdf: item.videoTimePdf || 2
        }
      ]
    } else if (!item.businessType) {
      return [
        {
          id: item.url || item.fileId,
          fileName: item.fileName,
          url: item.url,
          fileSize: item.fileSize,
          fileType: item.isPdf ? '.pdf' : '.mp4',
          isPdf: item.isPdf,
          videoTimeMp4: item.duration ? formatSeconds(+item.duration) : 0,
          videoTimePdf: item.videoTimePdf || 2,
          videoTime: item.duration || 0
        }
      ]
    }
    return []
  })
}

/**
 * 将秒数转换为可读的时间格式
 * @param {number} seconds - 要转换的秒数
 * @returns {string} 格式化后的时间字符串
 */
function formatSeconds(seconds: any) {
  // 验证输入是否为有效数字
  if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
    return '0秒'
  }

  // 四舍五入到整数
  seconds = Math.round(seconds)

  if (seconds < 60) {
    // 小于60秒，直接显示秒数
    return `${seconds}秒`
  } else {
    // 大于等于60秒，计算分钟和秒
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60

    // 如果剩余秒数为0，只显示分钟
    if (remainingSeconds === 0) {
      return `${minutes}分`
    } else {
      return `${minutes}分${remainingSeconds}秒`
    }
  }
}
