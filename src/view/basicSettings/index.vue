<template>
  <div class="exam-bg">
    <div class="head_box">
      <HeadTab :changeTabs="changeTabs" @changeTab="changeTab"></HeadTab>
      <div class="flex">
        <el-button type="primary" @click="add" class="add-btn" :icon="Plus" color="#527CFF" v-if="clickNum == 1"
          >新增</el-button
        >
      </div>
    </div>
    <!-- <el-card class="w-full h-[56px] leading-[50px] mb-[8px]"> </el-card> -->
    <!-- 动态组件切换 -->
    <el-card class="flex-1 h-0 overflow-hidden" style="background: #fff">
      <component ref="currentRef" :is="currentComponent"></component>
    </el-card>
    <!-- 新增 -->
    <AddOrEdit :operate="operate" :data="typeData" :id="typeId" ref="dialogRef" @close="closeFn" />
  </div>
</template>
<script setup lang="ts">
import { ref, shallowRef, onMounted } from 'vue'
import relatedTypeSetting from './comp/relatedTypeSetting.vue'
import { Plus } from '@element-plus/icons-vue'
import AddOrEdit from './comp/AddOrEdit.vue'
import safetyScoreSetting from './comp/safetyScoreSetting.vue'
import HeadTab from '@/components/HeadTab/index.vue'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const currentRef = ref()
const clickNum = ref(1)
const currentComponent = shallowRef(relatedTypeSetting)
const operate = ref<string>('')
const typeData = ref<any>({})
const dialogRef = ref()
const typeId = ref<string>('')

// 添加tab配置
const changeTabs = [
  { label: ui.value.zhLogo === 'yanchang' ? '承包商类型设置' : '相关方类型设置', value: '1', width: '170' },
  { label: '安全积分设置', value: '2', width: '170' }
]

// 修改切换tab的方法
const changeTab = (item: any) => {
  clickNum.value = Number(item.value)
  clickNum.value == 1 ? (currentComponent.value = relatedTypeSetting) : (currentComponent.value = safetyScoreSetting)
}

const add = () => {
  operate.value = 'add'
  dialogRef.value.showDialog({})
}
const closeFn = () => {
  currentRef.value?.getData()
}

onMounted(() => {})
</script>

<style lang="scss" scoped>
.exam-bg {
  @apply h-full w-full flex flex-col;
  // background-color: #f5f7ff;

  .active {
    color: #527cff;
    border-bottom: 3px solid #527cff;
  }

  .add-btn {
    width: 80px;
    height: 32px;
  }

  .button-bg {
    @apply w-[110px] h-[32px];
    background-color: #527cff;
  }

  .button-cr {
    @apply w-[150px] h-[32px];
    background-color: #527cff;
  }

  .upload-demo {
    @apply w-full pl-[10px];

    .colorBlue {
      color: #527cff;
    }
  }

  .downTemplate {
    @apply ml-[78px] text-center cursor-pointer;
    height: 36px;
    line-height: 36px;
    background: rgba(82, 124, 255, 0.06);
    border: 1px solid #527cff;
    font-weight: 400;
    font-size: 14px;
    color: #527cff;
  }

  .head_box {
    //background-color: #0081ff;
    // background-color: white;
    height: 40px;
    display: flex;
    // padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .w_active {
    width: 160px;
    height: 40px;
    line-height: 40px;
    background: url('@/assets/image/active.png') no-repeat;
    background-size: 100% 100%;
    color: #ffffff;
    z-index: 99;

    // border-bottom: 3px solid #4070ff;
    &:first-child {
      text-align: start;
      padding-left: 15px;
      background: url('@/assets/image/tab_active.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

:deep(.el-card__body) {
  @apply h-full;
  padding: 0;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}
</style>
