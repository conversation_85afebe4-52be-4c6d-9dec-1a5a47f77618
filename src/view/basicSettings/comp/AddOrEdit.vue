<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" @closed="close">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>
            {{
              props.operate === 'add'
                ? `${ui.zhLogo === 'yanchang' ? '新增承包商类型' : '新增相关方类型'}`
                : `${ui.zhLogo === 'yanchang' ? '编辑承包商类型' : '编辑相关方类型'}`
            }}
          </div>
        </div>
      </template>
      <div class="w_dialog_from_box">
        <el-form :model="formInline" ref="ruleFormRef" :rules="baseRule" label-width="130px">
          <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商类型：' : '相关方类型：'" prop="relatedTypeName">
            <el-input
              v-model="formInline.relatedTypeName"
              :placeholder="ui.zhLogo === 'yanchang' ? '请输入承包商类型' : '请输入相关方类型'"
            />
          </el-form-item>
          <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商管理人：' : '相关方管理人：'" prop="relatedManager">
            <div class="exam_paper">
              <el-input
                v-model="formInline.relatedManager"
                :placeholder="ui.zhLogo === 'yanchang' ? '请选择承包商管理人' : '请选择相关方管理人'"
                @click="showPreson"
                readonly
              />
              <el-button type="primary" plain @click="showPreson">选择</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="bottom_wrap">
          <el-button @click="close" plain>取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </template>
    </el-drawer>
    <SelectUsers ref="selectUsersRef" @selectedUser="selectedUser"></SelectUsers>
  </div>
</template>

<script setup lang="ts">
import SelectUsers from '@/view/components/selectUsers/index.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()

const props = defineProps({
  operate: {
    type: String,
    default: 'add'
  },
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  id: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['close'])
const ruleFormRef = ref<FormInstance>()
const selectUsersRef = ref()
const drawer = ref(false)
const selectArr = ref<any>([])
const editId = ref('') // 编辑id
const formInline = ref({
  relatedTypeName: '',
  relatedManager: '',
  relatedManagerIds: ''
})
const baseRule = reactive<FormRules>({
  relatedTypeName: [
    {
      required: true,
      message: ui.value.zhLogo === 'yanchang' ? '请输入承包商类型' : '请输入相关方类型',
      trigger: ['blur', 'change']
    }
  ],
  relatedManager: [
    {
      required: true,
      message: ui.value.zhLogo === 'yanchang' ? '请选择承包商管理人' : '请选择相关方管理人',
      trigger: 'change'
    }
  ]
})
const submitForm = () => {
  let addParams = { name: formInline.value.relatedTypeName }
  let editParams = { id: editId.value, name: formInline.value.relatedTypeName }
  ruleFormRef.value?.validate((valid) => {
    if (valid) {
      $API
        .post({
          url: 'edu-inter-server/coop/addOrUpdateCoopTypeInfo',
          params: props.operate === 'add' ? addParams : editParams,
          data: selectArr.value
        })
        .then((res: any) => {
          if (res && res.code === 'success') {
            props.operate === 'add' ? ElMessage.success('新增成功!') : ElMessage.success('编辑成功!')
            drawer.value = false
            emit('close')
          }
        })
    }
  })
}
function selectedUser(list: any) {
  console.log('list', list)
  formInline.value.relatedManager = list.map((item: any) => item.userName).join(',')
  formInline.value.relatedManagerIds = list.map((item: any) => item.id).join(',')
  let idList = list.map((item: any) => item.id)
  let nameList = list.map((item: any) => item.userName)
  selectArr.value = nameList.map((item: string, index: number) => {
    return {
      id: idList[index],
      userName: item
    }
  })
}
const showDialog = (item) => {
  if (props.operate === 'edit') {
    formInline.value.relatedTypeName = item.name
    formInline.value.relatedManager = item.managerStr
    formInline.value.relatedManagerIds = JSON.parse(item.manager)
      .map((user) => user.userId)
      .join(',')
    editId.value = item.id
    let idList = formInline.value.relatedManagerIds.split(',')
    let nameList = formInline.value.relatedManager.split(',')
    selectArr.value = nameList.map((item: string, index: number) => {
      return {
        userId: idList[index],
        userName: item
      }
    })
  } else {
    formInline.value = {
      relatedTypeName: '',
      relatedManager: '',
      relatedManagerIds: ''
    }
    editId.value = ''
  }
  drawer.value = true
}
// 显示人员弹框
const showPreson = () => {
  selectUsersRef.value.outerVisible = true
  let arr: any = []
  if (formInline.value.relatedManagerIds && formInline.value.relatedManager) {
    arr = selectArr.value.map((item: any) => {
      return {
        id: item.userId,
        userName: item.userName
      }
    })
    nextTick(() => {
      selectUsersRef.value!.toggleSelection(arr)
    })
  }
}
// 取消
const close = () => {
  if (props.operate === 'add') ruleFormRef.value?.resetFields()
  drawer.value = false
}

defineExpose({
  showDialog
  // close,
})

watch(
  () => props.data.id,
  () => {
    if (props.data.manager) {
      // formInline.value.relatedTypeName = props.data.name
      // formInline.value.relatedManager = props.data.managerStr
      // formInline.value.relatedManagerIds = JSON.parse(props.data.manager)
      //   .map((user) => user.userId)
      //   .join(',')
      // editId.value = props.data.id
      // let idList = formInline.value.relatedManagerIds.split(',')
      // let nameList = formInline.value.relatedManager.split(',')
      // selectArr.value = nameList.map((item: string, index: number) => {
      //   return {
      //     userId: idList[index],
      //     userName: item,
      //   }
      // })
      showDialog(props.data)
    } else {
      formInline.value = {
        relatedTypeName: '',
        relatedManager: '',
        relatedManagerIds: ''
      }
      editId.value = ''
    }
  },
  { immediate: true }
)
onMounted(() => {})
</script>

<style lang="scss" scoped>
.add_plan_dialog_box {
  .w_dialog_from_box {
    border-radius: 3px 3px 3px 3px;
    // border: 1px solid #e4e5eb;
    // border-top: none;
    // padding: 20px;
    margin: 24px;
    margin-bottom: 0;
  }

  .exam_paper {
    width: 100%;
    display: flex;
    justify-content: space-between;

    :deep(.el-input) {
      // width: 79%;
    }

    :deep(.el-button--primary.is-plain) {
      margin-left: 10px;
      border-radius: 4px;
      background: #eaefff;
      border: 1px solid #527cff;
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
    }
  }

  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
    height: 50px;
  }

  .w_add_plan_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  :deep(.el-drawer__footer) {
    // padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
