<template>
  <div class="w_server_content" v-loading="loading">
    <div class="w_server_table">
      <el-scrollbar max-height="100%">
        <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column
            prop="name"
            :label="ui.zhLogo === 'yanchang' ? '承包商类型名称' : '相关方类型名称'"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="managerStr"
            :label="ui.zhLogo === 'yanchang' ? '承包商管理负责人' : '相关方管理负责人'"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip />
          <el-table-column prop="createName" label="创建人" align="center" show-overflow-tooltip />
          <el-table-column label="操作" fixed="right" width="160" align="center">
            <template #default="scope">
              <el-button type="primary" plain size="small" class="span_cursor mr-1" @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button type="danger" plain size="small" class="span_cursor" @click="deleteType(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <no-data v-if="!tableData.length"></no-data>
      </el-scrollbar>
    </div>
    <div class="w_server_page_num" v-if="total">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 删除弹框 -->
    <el-dialog v-model="deleteVisible" width="488" height="290">
      <template #header>
        <HeadTitle :title="ui.zhLogo === 'yanchang' ? '删除承包商类型' : '删除相关方类型'" />
      </template>
      <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
      <div class="text-center text-info">确定执行该操作吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="deleteSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 编辑 -->
    <AddOrEdit :operate="operate" :data="typeData" :id="typeId" ref="dialogRef" @close="closeFn" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import mixTableHeight from '~/common/tableHeight.js'
import AddOrEdit from './AddOrEdit.vue'
import HeadTitle from '@/components/HeadTitle/index.vue'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const { tableHeight } = mixTableHeight({ subtractHeight: 282 })
const tableData = ref<any>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const loading = ref<boolean>(false)
const deleteVisible = ref<boolean>(false)
const deleteId = ref('')
const operate = ref<string>('')
const typeData = ref<any>({})
const dialogRef = ref()
const typeId = ref<string>('')
// 获取列表
const getData = async () => {
  loading.value = true
  let params = {
    pageNo: pageNo.value,
    pageSize: pageSize.value
  }
  const res: any = await $API.get({
    url: 'edu-inter-server/coop/getCoopTypeList',
    params
  })
  if (res.code == 'success') {
    loading.value = false
    tableData.value = res.data.rows
    pageNo.value = res.data.pageNo
    pageSize.value = res.data.pageSize
    total.value = res.data.total
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNo.value = 1
  getData()
}
const handleCurrentChange = (val: number) => {
  pageNo.value = val
  getData()
}

const closeFn = () => {
  getData()
}

// 编辑弹框处理方法
const handleEdit = (row) => {
  operate.value = 'edit'
  typeData.value = row
  dialogRef.value.showDialog(row)
}
// 删除类型
const deleteType = (item) => {
  deleteId.value = item.id
  deleteVisible.value = true
}
// 删除确认
const deleteSubmit = () => {
  $API
    .post({
      url: 'edu-inter-server/coop/deleteCoopTypeById',
      params: {
        id: deleteId.value
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('操作成功!')
        deleteVisible.value = false
        getData()
      }
    })
}
defineExpose({
  getData
})
onMounted(() => {
  getData()
})

// 实施任务
defineOptions({ name: 'serverComIndex' })
</script>

<style scoped lang="scss">
.w_server_content {
  @apply flex flex-col;
  width: 100%;
  height: 100%;
  padding: 20px 24px 20px 24px;
  // background-color: rgba(255, 255, 255, 1);

  .span_cursor {
    color: #527cff;
    cursor: pointer;

    &:last-child {
      color: #d43c31 !important;
    }
  }

  .w_server_title_box {
    width: 100%;
  }

  .w_server_table {
    @apply flex-1 h-0;
    //height: calc(100vh - 364px);
    width: 100%;

    //background-color: #28a458;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_server_page_num {
    display: flex;
    justify-content: end;
    margin-top: 10px;
    //background-color: orange;
  }

  .delete {
    width: 72px;
    height: 72px;
    background: #d9dde8;
    margin: 0 auto;
    background: url(@/assets/image/exam-delete.png) no-repeat center;
    background-size: 100% 100%;
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }

  :deep(.el-table--fit) {
    height: 100% !important;
  }

  :deep(.el-table__inner-wrapper) {
    height: 100% !important;
  }

  :deep(.el-table__empty-text) {
    display: none;
  }
}
</style>
