<template>
  <div class="w_server_content">
    <div class="mb-[24px]">
      <div class="mb-[16px]">
        <div class="flex justify-between items-center mb-[8px]">
          <span class="font-bold text-[18px]">总分数</span>
          <el-input-number v-model="totalScore" :min="0" :step="100" :controls="false" readonly />
        </div>
        <div class="text-[#999]">
          {{ `${ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业'}` }}评价采用扣分制，总分为每个{{
            `${ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业'}`
          }}的基础总分
        </div>
      </div>
      <div>
        <div class="flex justify-between items-center mb-[8px]">
          <span class="font-bold text-[18px]">停工分数</span>
          <el-input-number v-model="stopWorkScore" :min="0" :step="10" :controls="false" readonly />
        </div>
        <div class="text-[#999]">
          当企业得分小于停工分的时候，企业将无法进行作业，并且将短信通知企业负责人、{{
            ui.zhLogo === 'yanchang' ? '承包商监管负责人' : '相关方监管负责人'
          }}
        </div>
      </div>
    </div>
    <div class="text-[18px] font-bold mb-[16px]">扣分细则</div>
    <div class="w_server_table">
      <el-scrollbar max-height="100%">
        <el-table :data="tableData" border :span-method="objectSpanMethod">
          <el-table-column type="index" label="序号" width="150" align="center" />
          <el-table-column prop="category" label="扣分项目" align="center" width="300" />
          <el-table-column prop="penalty" label="处罚事项" align="center" />
          <el-table-column prop="score" label="扣分标准" width="120" align="center" />
        </el-table>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserInfo } from '~/store'
import $API from '~/common/api'

const ui = useUserInfo()
const totalScore = ref(100)
const stopWorkScore = ref(70)
const tableData = ref([])

const getSafetyScoreSetting = async () => {
  const res: any = await $API.post({
    url: 'edu-inter-server/score/queryScoreSetting'
  })
  if (res && res.code === 200) {
    // 设置总分和停工分数
    totalScore.value = res.data.tatalScore || 100
    stopWorkScore.value = res.data.stopScore || 70

    // 处理表格数据
    if (res.data.safetyScoreSettings && res.data.safetyScoreSettings.length > 0) {
      // 按照扣分项目(category)对数据进行分组排序
      const groupedData = {}
      res.data.safetyScoreSettings.forEach((item) => {
        const category = item.deductionItemName
        if (!groupedData[category]) {
          groupedData[category] = []
        }
        groupedData[category].push({
          category: category,
          penalty: item.punishmentMatters,
          score: item.score
        })
      })

      // 将分组后的数据转换为数组
      const formattedData = []
      Object.keys(groupedData).forEach((category) => {
        formattedData.push(...groupedData[category])
      })

      tableData.value = formattedData
    }
  }
}

function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (columnIndex === 1) {
    const currentCategory = tableData.value[rowIndex].category

    let prevSpan = 0
    for (let i = rowIndex - 1; i >= 0; i--) {
      if (tableData.value[i].category === currentCategory) {
        prevSpan++
      } else {
        break
      }
    }

    if (prevSpan > 0) {
      return {
        rowspan: 0,
        colspan: 0
      }
    }

    let nextSpan = 1
    for (let i = rowIndex + 1; i < tableData.value.length; i++) {
      if (tableData.value[i].category === currentCategory) {
        nextSpan++
      } else {
        break
      }
    }

    return {
      rowspan: nextSpan,
      colspan: 1
    }
  }

  return {
    rowspan: 1,
    colspan: 1
  }
}

onMounted(() => {
  getSafetyScoreSetting()
})
</script>

<style scoped lang="scss">
.w_server_content {
  width: 100%;
  height: 100%;
  padding: 20px 24px 20px 24px;
  .w_server_table {
    height: calc(100vh - 464px);
    width: 100%;

    //background-color: #28a458;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }

    // 添加以下样式来加重表格分割线颜色
    :deep(.el-table__border) {
      border-color: #dcdfe6;
    }

    :deep(.el-table__cell) {
      border-color: #dcdfe6 !important;
    }
  }
}
</style>
