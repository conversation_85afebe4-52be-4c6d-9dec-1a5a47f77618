<template>
  <div></div>
</template>
<script setup lang="ts">
import { setFavicon } from '@/hooks/updIcon'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import router from '~/router'
import { useUserInfo } from '~/store'

const routes = useRoute()
const ui: any = useUserInfo()

function getUserInfo() {
  // ui.value.token = routes.query.id
  unitSubmit(routes.query.sysCode)
}

async function unitSubmit(code) {
  $API
    .post({
      url: 'edu-inter-server/login/queryToken',
      params: {
        sysCode: code,
        token: routes.query.token
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const d = res.data
        d.resourceVoList = res?.data?.resourceVoList.map((item) => {
          return {
            ...item,
            children: item.childrens
          }
        })
        ui.value = d
        ui.value.coopResourceList = ui.value.resourceVoList || []
        ui.value.systemName = d?.systemName || '相关方管理系统'
        ui.value.resourceList = d?.coopResourceList || []

        // 获取安全积分按钮权限
        ui.value.buttonPermission = []
        ui.value.resourceList.forEach((item: any) => {
          item.childrens.forEach((sItem: any) => {
            const arr = sItem.childrens || []
            ui.value.buttonPermission = ui.value.buttonPermission.concat(arr.map((i: any) => i.resAlias))
          })
        })

        sessionStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
        localStorage.setItem('@@web_userInfo_partner', JSON.stringify(ui.value))
        setFavicon(ui.value)
        goRouter(ui.value.resourceVoList)
      }
    })
}

function goRouter(routerList) {
  if (routerList.length == 0) {
    return router.push('/login')
  }
  let goRouterObj = routerList[0]
  let goRouterUrlName = ''
  if (goRouterObj.childrens.length > 0) {
    goRouterUrlName = goRouterObj.childrens[0].resUrl
  } else {
    goRouterUrlName = goRouterObj.resUrl
  }
  router.push(goRouterUrlName)
  // setTimeout(() => {
  //   window.location.reload()
  // }, 400)
}

getUserInfo()
</script>

<style scoped lang="scss"></style>
