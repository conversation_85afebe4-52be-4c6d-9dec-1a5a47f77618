<template>
  <div class="w_server_content">
    <div class="w_server_title_box pl-[120px]">
      <span class="text-[#527CFF] font-bold italic">温馨提示：</span
      >资质或合同距离到期一个月的时间将会提醒，对应企业将显示"即将到期"，以便及时通知企业进行复审或重新签订合同。
    </div>

    <!-- 添加搜索框 -->
    <div class="w_server_search mb-[10px] flex justify-between">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入企业名称进行搜索"
            class="!w-[400px]"
            clearable
            @input="handleSearch"
            :suffix-icon="Search"
          />
        </el-form-item>
      </el-form>

      <div class="flex">
        <el-button v-auth="['qyllMgrBatchDel']" type="primary" @click="bulkDel" color="#527CFF">批量删除</el-button>
        <el-button type="primary" @click="handleExport" color="#527CFF">导出</el-button>
      </div>
    </div>

    <div class="w_server_table">
      <el-table
        :data="tableData"
        stripe
        style="width: 100%; height: 100%"
        fit
        row-key="id"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="deptName" label="企业名称" align="center" show-overflow-tooltip width="240" />
        <el-table-column prop="score" label="安全积分" align="center" show-overflow-tooltip width="120">
          <template #default="scope">
            <div class="flex items-center justify-center">
              <div
                class="px-2 py-1 text-[#fff] text-xs rounded w-[60px]"
                :class="scope.row.unitStatus === '1' ? 'bg-[#28a458]' : 'bg-[#f56c6c]'"
              >
                {{ scope.row.score }}分
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createOrgName" label="所属单位" align="center" show-overflow-tooltip width="150">
          <template #default="scope">
            <span v-if="scope.row.createOrgName">{{ scope.row.createOrgName }}</span>
            <span v-else>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="systemAccount" label="系统账号" align="center" width="110" show-overflow-tooltip />
        <el-table-column
          prop="longTimeObj"
          :label="ui.zhLogo === 'yanchang' ? '承包商种类' : '相关方种类'"
          align="center"
          show-overflow-tooltip
          width="110"
        >
          <template #default="scope">
            <span v-if="scope.row.longTimeObj">{{
              scope.row.longTimeObj === '1'
                ? '长期' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
                : '短期' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
            }}</span>
            <span v-else>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="relatedTypeName"
          :label="ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型'"
          align="center"
          show-overflow-tooltip
          width="110"
        />
        <el-table-column prop="unitManager" label="企业负责人" align="center" show-overflow-tooltip width="120" />
        <el-table-column prop="phone" label="企业负责人手机号" align="center" show-overflow-tooltip width="120" />
        <el-table-column
          prop="relatedManager"
          :label="ui.zhLogo === 'yanchang' ? '承包商监管负责人' : '相关方监管负责人'"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default="scope">
            <span>{{ scope.row.relatedManager || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="blStatusName" label="营业执照" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.blStatus == 0 ? 'text-[red]' : ''">{{ scope.row.blStatusName }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="spStatusName" label="安全协议" align="center" show-overflow-tooltip width="120">
          <template #default="scope">
            <span :class="scope.row.spStatus == 0 ? 'text-[red]' : ''">{{ scope.row.spStatusName }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="qcStatusName" label="资质证书" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.qcStatus == 0 ? 'text-[red]' : ''">{{ scope.row.qcStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="qcExpireStatusName"
          label="资质到期预警"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default="scope">
            <span :class="scope.row.qcExpireStatus != 2 ? 'text-[red]' : ''">{{
              scope.row.qcExpireStatusName || '未上传'
            }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="contractNum" label="合同数" align="center" show-overflow-tooltip>
          <template #default>
            <!-- <span :class="scope.row.contractExpireStatus == 0 ? 'text-[red]' : ''">{{ -->
            <span>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="contractExpireStatusName"
          label="合同到期预警"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default>
            <span>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="创建人" align="center" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip width="220" />
        <el-table-column label="操作" fixed="right" width="280" align="center">
          <!-- v-if="scope.row.createOrgCode === ui.unitId" -->
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="goSpace(scope.row)"
              >{{ ui.zhLogo === 'yanchang' ? '承包商履历' : '相关方履历' }}
            </el-button>
            <el-button v-auth="['qyllMgrUpd']" type="primary" plain size="small" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button v-auth="['qyllMgrStop']" type="danger" plain size="small" @click="handleStop(scope.row)"
              >停用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_server_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <EditCom ref="dialogRef" @action="handleFun" :isEdit="true"></EditCom>
  <!-- 停用 -->
  <el-dialog v-model="innerVisible" width="500" title="停用确认" @close="cancle">
    <el-form
      ref="ruleDialogRef"
      style="max-width: 600px"
      :model="ruleDioData"
      status-icon
      label-width="auto"
      class="demo-ruleForm"
    >
      <el-form-item :rules="{ required: true, message: '请输入停用原因', trigger: ['blur', 'change'] }" prop="tyyy">
        <el-input
          v-model="ruleDioData.tyyy"
          style="width: 100%"
          :rows="5"
          type="textarea"
          placeholder="请输入"
          maxlength="100"
          show-word-limit
          @change="getResonInput"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer pr-[20px]">
        <el-button @click="cancle" plain>取消</el-button>
        <el-button type="primary" @click="tyyySubmit"> 确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import EditCom from './edit.vue'
import { useUserInfo } from '~/store'
import { debounce } from 'lodash-es'
import { Search } from '@element-plus/icons-vue'
import { exportFile } from '~/common/index'
import dayjs from 'dayjs'

const ui: any = useUserInfo()
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
const router = useRouter()
const dialogRef = ref()
const ruleDialogRef = ref()
const innerVisible = ref(false)
const currentReasonRow = ref<any>({})
const ruleDioData = ref({
  tyyy: ''
})

const tableData = ref<any>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const searchForm = ref({
  name: ''
})

const handleSizeChange = () => {
  pageNo.value = 1
  getData()
}
const handleCurrentChange = () => {
  getData()
}

// 编辑弹框处理方法
const handleEdit = (row) => {
  dialogRef.value.showDialog(row)
}

const handleFun = () => {
  getData()
}

const goSpace = (row: any) => {
  const href = router.resolve({
    name: 'enterprise',
    query: { id: row.id, orgCode: row.createOrgCode }
  }).href

  window.open(href, '_blank')
}

function getResonInput(v) {
  ruleDioData.value.tyyy = v
}

const handleStop = (row: any) => {
  innerVisible.value = true
  currentReasonRow.value = { ...row }
}

const cancle = () => {
  innerVisible.value = false
  ruleDioData.value.tyyy = ''
  ruleDialogRef.value?.resetFields()
}

async function tyyySubmit() {
  ruleDialogRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      let res: any = await $API.post({
        url: 'edu-inter-server/relatedPartner/stopRelated',
        data: {
          orgCode: currentReasonRow.value.id,
          enableReason: ruleDioData.value.tyyy,
          serverStatus: '0'
        }
      })
      if (res.code == 'success') {
        ElMessage({
          message: res.msg || '此企业归档至 已停用 列表中',
          type: 'success'
        })
        cancle()
        getData()
      } else {
        // ElMessage.error(res.msg || '停用失败')
      }
    }
  })
}

// 创建防抖后的getData方法
const debouncedGetData = debounce(() => {
  getData()
}, 500)

// 修改搜索框相关事件
const handleSearch = () => {
  debouncedGetData()
}

// 获取列表
const getData = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/relatedPartner/relatedPageList',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      serverStatus: '1',
      unitId: props.id ? props.id : ui.value.unitId,
      name: searchForm.value.name
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}
// 导出
const handleExport = async () => {
  const fileName =
    ui.value.zhLogo === 'yanchang'
      ? '承包商企业列表_' + dayjs(new Date()).format('YYYYMMDDHHmmss')
      : '相关方企业列表_' + dayjs(new Date()).format('YYYYMMDDHHmmss')
  exportFile(
    {
      url: 'edu-inter-server/relatedPartner/exportRelatedPageList',
      data: {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        serverStatus: '1',
        unitId: props.id ? props.id : ui.value.unitId,
        name: searchForm.value.name
      },
      responseType: 'flow'
    },
    fileName
  )
}

/**
 * 批量删除
 */
const multipleSelection = ref([])

function selectionChange(e: any) {
  multipleSelection.value = e
}

function bulkDel() {
  if (!multipleSelection.value || multipleSelection.value.length == 0) {
    return ElMessage.warning('请选择要删除的企业！')
  }
  const arr = multipleSelection.value.map((item: any) => {
    return {
      id: item.id,
      createOrgCode: item.createOrgCode
    }
  })
  const url = 'edu-inter-server/relatedPartner/delRelatedInfo'

  $API.post({ url, data: arr }).then((res: any) => {
    if (res.code == 'success') {
      ElMessage.success('删除成功！')
      handleSearch()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const refreshFetch = () => {
  getData()
}
watch(
  () => props.id,
  () => {
    getData()
  },
  { immediate: true }
)

onMounted(() => {
  // getData()
})
defineExpose({
  refreshFetch,
  getData
})
// 实施任务
defineOptions({ name: 'serverComIndex' })
</script>

<style scoped lang="scss">
.w_server_content {
  //display: grid;
  width: 100%;
  // height: 100%;
  padding: 20px 24px 20px 24px;
  background-color: rgba(255, 255, 255, 1);

  .w_server_title_box {
    width: 100%;
    height: 54px;
    margin-bottom: 20px;
    background: url('./img/bj.png') no-repeat;
    background-size: 100% 100%;
    line-height: 54px;
  }

  .w_server_table {
    width: 100%;
    height: calc(100vh - 320px);

    //background-color: #28a458;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_server_page_num {
    display: flex;
    justify-content: end;
    margin-top: 10px;
  }
}
</style>
