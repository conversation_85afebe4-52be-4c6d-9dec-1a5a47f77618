<template>
  <div class="w_stop_content">
    <div class="w_stop_title_box pl-[120px]">
      <span class="text-[#527CFF] font-bold italic">温馨提示：</span>下方列表的停用企业账号将无法登录相关系统，请知悉。
    </div>
    <!-- 添加搜索框 -->
    <div class="w_server_search mb-[10px] flex justify-between">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入企业名称进行搜索"
            class="!w-[400px]"
            clearable
            @input="handleSearch"
            :suffix-icon="Search"
          />
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="handleExport" color="#527CFF">导出</el-button>
    </div>
    <div class="w_stop_table">
      <el-table :data="tableData" stripe style="width: 100%; height: 100%" :fit="true">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="deptName" label="企业名称" align="center" show-overflow-tooltip width="240" />
        <el-table-column prop="score" label="安全积分" align="center" show-overflow-tooltip width="120">
          <template #default="scope">
            <div class="flex items-center justify-center">
              <div
                class="px-2 py-1 text-[#fff] text-xs rounded w-[60px]"
                :class="scope.row.unitStatus === '1' ? 'bg-[#28a458]' : 'bg-[#f56c6c]'"
              >
                {{ scope.row.score }}分
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createOrgName" label="所属单位" align="center" show-overflow-tooltip width="150">
          <template #default="scope">
            <span v-if="scope.row.createOrgName">{{ scope.row.createOrgName }}</span>
            <span v-else>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="systemAccount" label="系统账号" align="center" show-overflow-tooltip />
        <el-table-column
          prop="longTimeObj"
          :label="ui.zhLogo === 'yanchang' ? '承包商种类' : '相关方种类'"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default="scope">
            <span v-if="scope.row.longTimeObj">{{
              scope.row.longTimeObj === '1'
                ? '长期' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
                : '短期' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
            }}</span>
            <span v-else>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="relatedTypeName"
          :label="ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型'"
          align="center"
          show-overflow-tooltip
          width="150"
        />
        <el-table-column prop="unitManager" label="企业负责人" align="center" show-overflow-tooltip width="150" />
        <el-table-column prop="phone" label="企业负责人手机号" align="center" show-overflow-tooltip width="120" />
        <el-table-column
          prop="relatedManager"
          :label="ui.zhLogo === 'yanchang' ? '承包商监管负责人' : '相关方监管负责人'"
          align="center"
          show-overflow-tooltip
          width="150"
        />
        <el-table-column prop="blStatusName" label="营业执照" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.blStatus == 0 ? 'text-[red]' : ''">{{ scope.row.blStatusName || '未上传' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="spStatusName" label="安全协议" align="center" show-overflow-tooltip width="120">
          <template #default="scope">
            <span :class="scope.row.spStatus == 0 ? 'text-[red]' : ''">{{ scope.row.spStatusName || '未上传' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="qcStatusName" label="资质证书" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :class="scope.row.qcStatus == 0 ? 'text-[red]' : ''">{{ scope.row.qcStatusName || '未上传' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="qcExpireStatusName"
          label="资质到期预警"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default="scope">
            <span :class="scope.row.qcExpireStatus != 2 ? 'text-[red]' : ''">{{
              scope.row.qcExpireStatusName || '未上传'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="contractNum" label="合同数" align="center" show-overflow-tooltip>
          <template #default="scope">
            <!-- <span :class="scope.row.contractExpireStatus == 0 ? 'text-[red]' : ''">{{ -->
            <span>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="contractExpireStatusName"
          label="合同到期预警"
          align="center"
          show-overflow-tooltip
          width="150"
        >
          <template #default="scope">
            <span>{{ '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="创建人" align="center" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip width="220" />

        <el-table-column label="操作" fixed="right" width="260" align="center">
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="goSpace(scope.row)">{{
              ui.zhLogo === 'yanchang' ? '承包商履历' : '相关方履历'
            }}</el-button>
            <el-button v-auth="['qyllMgrEnabled']" type="primary" plain size="small" @click="handleStop(scope.row)"
              >重新启用</el-button
            >
            <el-button v-auth="['qyllMgrDel']" type="danger" plain size="small" @click="deleteStop(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="w_stop_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 启用 -->
    <el-dialog v-model="innerVisible" width="500" title="确认重新启用？" @close="cancle">
      <el-input
        v-model="reason"
        style="width: 100%"
        :rows="5"
        type="textarea"
        maxlength="100"
        disabled
        show-word-limit
      />
      <template #footer>
        <div class="dialog-footer pr-[20px]">
          <el-button @click="cancle" plain>取消</el-button>
          <el-button type="primary" @click="reasonSubmit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 删除弹框 -->
    <el-dialog v-model="deleteVisible" width="488" height="290" @close="deleteClose" class="delete-dialog">
      <template #header>
        <titleTag title="删除" />
      </template>
      <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
      <div class="text-center text-info">确定要删除吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteClose" plain>取消</el-button>
          <el-button type="primary" @click="deleteSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserInfo } from '~/store'
import titleTag from '~/view/trainCourseManagement/comp/titleTag.vue'
import { exportFile } from '~/common/index'
import { Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { debounce } from 'lodash-es'

const ui: any = useUserInfo()
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})
// const dialogRef = ref()
const router = useRouter()
const innerVisible = ref(false)
const deleteVisible = ref(false)
const tableData = ref<any>([])
const total = ref<number>(0)
const searchForm = ref({
  name: ''
})
const reason = ref('')
const deleteId = ref('')
const createOrgCode = ref('')
const currentReasonRow = ref<any>({})
const pageNo = ref<number>(1)

const pageSize = ref<number>(20)

const handleSizeChange = () => {
  pageNo.value = 1
  getData()
}
const handleCurrentChange = () => {
  getData()
}
// 关闭删除弹框
const deleteClose = () => {
  deleteVisible.value = false
}
const deleteStop = (v: any) => {
  deleteId.value = v.id
  createOrgCode.value = v.createOrgCode
  deleteVisible.value = true
}
// 删除确认
const deleteSubmit = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/relatedPartner/delRelatedInfo',
    data: [
      {
        id: deleteId.value,
        createOrgCode: createOrgCode.value
      }
    ]
  })
  if (res.code == 'success') {
    deleteVisible.value = false
    ElMessage.success(res.msg || '删除成功')
    getData()
  } else {
    ElMessage.warning(res.msg)
  }
}
const goSpace = (row: any) => {
  const href = router.resolve({
    name: 'enterprise',
    query: { id: row.id, orgCode: row.createOrgCode }
  }).href

  window.open(href, '_blank')
}
// 创建防抖后的getData方法
const debouncedGetData = debounce(() => {
  getData()
}, 500)

// 修改搜索框相关事件
const handleSearch = () => {
  debouncedGetData()
}
// 获取我的实施列表
const getData = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/relatedPartner/relatedPageList',
    method: 'post',
    data: {
      serverStatus: 0,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      unitId: props.id ? props.id : ui.value.unitId,
      name: searchForm.value.name
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

// 重新启用
const handleStop = async (row: any) => {
  innerVisible.value = true
  currentReasonRow.value = { ...row }
  reason.value = currentReasonRow.value.stopReason
}

const cancle = () => {
  innerVisible.value = false
}

async function reasonSubmit() {
  let res: any = await $API.post({
    url: 'edu-inter-server/relatedPartner/stopRelated',
    data: {
      orgCode: currentReasonRow.value.id,
      enableReason: '',
      serverStatus: '1'
    }
  })
  if (res.code == 'success') {
    ElMessage({
      message: res.msg || '启用成功',
      type: 'success'
    })
    cancle()
    getData()
  } else {
    ElMessage.error(res.msg || '启用失败')
  }
}

// 导出
const handleExport = async () => {
  const fileName = '承包商企业列表_' + dayjs(new Date()).format('YYYYMMDDHHmmss')
  exportFile(
    {
      url: 'edu-inter-server/relatedPartner/exportRelatedPageList',
      data: {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        serverStatus: 0,
        unitId: props.id ? props.id : ui.value.unitId,
        name: searchForm.value.name
      },
      responseType: 'flow'
    },
    fileName
  )
}
watch(
  () => props.id,
  () => {
    getData()
  },
  { immediate: true }
)

onMounted(() => {
  // getData()
})

// 实施任务
defineOptions({ name: 'implementationTasksIndex' })
</script>

<style scoped lang="scss">
.w_stop_content {
  //display: grid;
  width: 100%;
  height: 100%;
  padding: 20px 24px 20px 24px;
  background-color: rgba(255, 255, 255, 1);
  //grid-template-columns: 1fr;

  .w_stop_title_box {
    margin-bottom: 20px;
    width: 100%;
    height: 54px;
    background: url('./img/bj.png') no-repeat;
    background-size: 100% 100%;
    line-height: 54px;
  }

  .w_stop_table {
    width: 100%;
    height: calc(100vh - 375px);
    //display: grid;
    //grid-template-columns: 1fr;
    //grid-template-rows: 1fr;

    //background-color: orange;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_stop_page_num {
    display: flex;
    justify-content: end;
    margin-top: 10px;
  }
}

.delete {
  width: 72px;
  height: 72px;
  background: #d9dde8;
  margin: 0 auto;
  background: url(@/assets/image/exam-delete.png) no-repeat center;
  background-size: 100% 100%;
}

:deep(.delete-dialog) {
  padding: 0 !important;
}

:deep(.delete-dialog .el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.delete-dialog .el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}
</style>
