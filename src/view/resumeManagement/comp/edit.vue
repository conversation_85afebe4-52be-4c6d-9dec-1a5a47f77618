<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" @closed="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>
            {{
              isEdit
                ? '编辑' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
                : '新增' + (ui.zhLogo === 'yanchang' ? '承包商' : '相关方')
            }}
          </div>
        </div>
      </template>
      <div class="w_plan_con_box">
        <div class="w_content_box">
          <el-scrollbar>
            <div class="mb-3 w_dialog_from_box">
              <el-form :model="formInline" class="" ref="ruleFormRef" :rules="rules" label-width="150px">
                <el-form-item label="单位名称：" prop="deptName">
                  <el-input
                    v-model="formInline.deptName"
                    placeholder="请输入单位名称"
                    maxlength="200"
                    :disabled="isEdit || disabled"
                  />
                </el-form-item>
                <el-form-item label="统一社会信用代码：" prop="uscc">
                  <el-input
                    v-model="formInline.uscc"
                    placeholder="请输入统一社会信用代码"
                    maxlength="200"
                    :disabled="isEdit || disabled"
                    @input="debouncedSearchUnit"
                  />
                </el-form-item>
                <el-form-item label="注册资金：" prop="registeredCapital">
                  <el-input
                    v-model="formInline.registeredCapital"
                    @input="handleInput3"
                    placeholder="请输入注册资金（万元）"
                    maxlength="200"
                    :disabled="disabled"
                  >
                    <template #append>
                      <el-button type="primary" plain size="small">万元</el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="企业负责人：" prop="unitManager">
                  <el-input
                    :disabled="disabled"
                    v-model="formInline.unitManager"
                    placeholder="请输入企业负责人"
                    maxlength="200"
                  />
                </el-form-item>

                <el-form-item label="企业负责人手机号：" prop="phone">
                  <el-input
                    :disabled="disabled"
                    v-model="formInline.phone"
                    placeholder="请输入企业负责人手机号"
                    maxlength="200"
                    @input="handleInput"
                  />
                </el-form-item>

                <el-form-item label="详细地址：" prop="detailAddress">
                  <el-input
                    :disabled="disabled"
                    v-model="formInline.detailAddress"
                    placeholder="请输入详细地址"
                    maxlength="200"
                  />
                </el-form-item>
                <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商种类' : '相关方种类'" prop="detailAddress">
                  <el-radio-group v-model="formInline.longTimeObj">
                    <el-radio value="1">{{ ui.zhLogo === 'yanchang' ? '长期承包商' : '长期相关方' }}</el-radio>
                    <el-radio value="2">{{ ui.zhLogo === 'yanchang' ? '短期承包商' : '短期相关方' }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型'" prop="relatedType">
                  <el-select
                    v-model="formInline.relatedType"
                    :placeholder="ui.zhLogo === 'yanchang' ? '请选择承包商类型' : '请选择相关方类型'"
                    style="width: 100%"
                    @change="selectChange"
                    :disabled="isEdit || disabled"
                    clearable
                  >
                    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商监管人' : '相关方监管人'">
                  <el-input
                    v-model="formInline.relatedManager"
                    :placeholder="ui.zhLogo === 'yanchang' ? '请选择承包商监管人' : '请选择相关方监管人'"
                    @click="showPreson"
                  >
                    <template #append>
                      <el-button type="primary" plain size="small" @click="showPreson">选择</el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <!--<el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商现场负责人' : '相关方现场负责人'">
                  <el-input
                    v-model="formInline.relatedSceneManager"
                    :placeholder="ui.zhLogo === 'yanchang' ? '请输入承包商现场负责人' : '请输入相关方现场负责人'"
                    maxlength="200"
                  />
                </el-form-item>

                <el-form-item label="现场负责人手机号：" prop="relatedSceneTel">
                  <el-input
                    v-model="formInline.relatedSceneTel"
                    :placeholder="
                      ui.zhLogo === 'yanchang' ? '请输入承包商现场负责人手机号' : '请输入相关方现场负责人手机号'
                    "
                    maxlength="200"
                  />
                </el-form-item>-->

                <el-form-item label="营业执照：" prop="blFileId">
                  <UploadImg @getImgList="getYYZZ" :img-list="YYZZList" :disabled="disabled" />
                </el-form-item>

                <el-form-item label="营业执照有效期：" prop="blExpireTime">
                  <el-date-picker
                    :disabled="disabled"
                    v-model="formInline.blExpireTime"
                    type="date"
                    placeholder="请选择营业执照有效期"
                    value-format="YYYY-MM-DD"
                    :size="size"
                    style="width: 100%"
                  />
                </el-form-item>

                <!--<el-form-item label="安全协议：" prop="spFileId">
                      <UploadImg @getImgList="getAQXY" :img-list="AQXYList" />
                    </el-form-item>-->
              </el-form>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()">保存</el-button>
        </div>
      </template>
    </el-drawer>
    <SelectUsers ref="selectUsersRef" @selectedUser="selectedUser"></SelectUsers>
  </div>
</template>
<script setup lang="ts">
import UploadImg from '@/components/uploadImg/index.vue'
import config from '@/config'
import SelectUsers from '@/view/components/selectUsers/index.vue'
import { useDebounceFn } from '@vueuse/core'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { nextTick, reactive, ref, watch } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['action', 'submit'])
const selectUsersRef = ref()

const drawer = ref(false)
const options = ref<Record<string, any>[]>([])
// 试卷列表
// 日期
// const dataValue = ref('')
// const url = ref(config.update_file + '/file/uploadfile')

let YYZZList = ref<Record<string, any>[]>([])
let AQXYList = ref<Record<string, any>[]>([])

// 选中人员数据

const ruleFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  deptName: [
    {
      required: true,
      message: '请输入单位名称',
      trigger: ['blur', 'change']
    }
  ],
  uscc: [
    {
      required: true,
      message: '请输入统一社会信用代码',
      trigger: ['blur', 'change']
    }
  ],
  unitManager: [
    {
      required: true,
      message: '请输入企业负责人',
      trigger: ['blur', 'change']
    }
  ],
  phone: [
    {
      required: true,
      validator: (rule, value, callback) => {
        const regex = /^1[3-9]\d{9}$/
        if (!regex.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  relatedSceneTel: [
    {
      validator: (rule, value, callback) => {
        const regex = /^1[3-9]\d{9}$/
        if (value) {
          if (!regex.test(value)) {
            callback(new Error('请输入正确的手机号'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  relatedType: [
    {
      required: true,
      message: ui.value.zhLogo === 'yanchang' ? '请选择承包商类型' : '请选择相关方类型',
      trigger: ['blur', 'change']
    }
  ]
})

const size = ref<'' | 'large' | 'small'>('')

const formInline = ref<any>({
  deptName: '', // 培训任务名称
  uscc: '', //统一社会信用代码
  registeredCapital: '', // 培训结束时间
  unitManager: '', // 培训开始时间
  phone: '', //
  detailAddress: '', //
  relatedType: '', //
  relatedTypeName: '', //
  relatedManagerIds: '', //
  relatedManager: '', //
  relatedSceneManager: '', //
  relatedSceneTel: '', //
  blFileId: '', //
  blExpireTime: null, //
  spFileId: '', //
  trainId: '', //
  spFiles: [], //
  blFiles: [], //
  otherFileId: '', //
  longTimeObj: ''
})

// 显示人员弹框
const showPreson = () => {
  selectUsersRef.value.outerVisible = true
  let arr = []
  if (formInline.value.relatedManagerIds && formInline.value.relatedManager) {
    let idList = formInline.value.relatedManagerIds.split(',')
    let nameList = formInline.value.relatedManager.split(',')
    arr = nameList.map((item: string, index: number) => {
      return {
        id: idList[index],
        userName: item
      }
    })
    console.log(arr, '给的arr')
    nextTick(() => {
      selectUsersRef.value.toggleSelection(arr)
    })
  }
}

const handleClose = () => {
  drawer.value = false
  AQXYList.value = []
  YYZZList.value = []
  ruleFormRef.value?.resetFields()
}

async function getDeatail(id: string) {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/unitInfo',
    params: {
      unitId: id
    }
  })
  formInline.value = { ...res.data }

  if (!formInline.value.blExpireTime) {
    formInline.value.blExpireTime = null
  }
  if (res.data.blFiles) {
    YYZZList.value = res.data.blFiles.map((item: any) => {
      return {
        name: item.fileName,
        url: config.downloadFileUrl + item.filePath,
        id: item.id
      }
    })
  }

  if (res.data.spFiles) {
    AQXYList.value = res.data.spFiles.map((item: any) => {
      return {
        name: item.fileName,
        url: config.downloadFileUrl + item.filePath,
        id: item.id
      }
    })
  }
}

const showDialog = (row: any) => {
  // editId.value = row.id
  disabled.value = false
  if (row.id) {
    getDeatail(row.id)
  } else {
    formInline.value = {
      deptName: '', // 培训任务名称
      uscc: '', //统一社会信用代码
      registeredCapital: '', // 培训结束时间
      unitManager: '', // 培训开始时间
      phone: '', //
      detailAddress: '', //
      relatedType: '', //
      relatedTypeName: '', //
      relatedManagerIds: '', //
      relatedManager: '', //
      relatedSceneManager: '', //
      relatedSceneTel: '', //
      blFileId: '', //
      blExpireTime: null, //
      spFileId: '', //
      trainId: '', //
      spFiles: [], //
      blFiles: [], //
      otherFileId: '', //
      longTimeObj: '',
      createOrgCode: props.id,
      createOrgName: props.name
    }
  }
  drawer.value = true
}

function selectedUser(list: any) {
  console.log(list, '拿到的组件')

  formInline.value.relatedManager = list.map((item: any) => item.userName).join(',')
  formInline.value.relatedManagerIds = list.map((item: any) => item.id).join(',')
}

const onSubmit = useDebounceFn(async () => {
  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      formInline.value.blFileId = YYZZList.value.map((item: any) => item.id).join(',')
      formInline.value.spFileId = AQXYList.value.map((item: any) => item.id).join(',')
      const url = props.isEdit ? 'updateRelatedInfo' : 'addRelatedInfo'
      let res: any = await $API.post({ url: `edu-inter-server/relatedPartner/${url}`, data: { ...formInline.value } })
      if (res.code == 'success') {
        ElMessage.success(props.isEdit ? '编辑成功' : '新增成功')
        handleClose()
        emits('action')
      }
    }
  })
}, 500)

function getYYZZ(list: any) {
  YYZZList.value = list
}

async function getOptionsByXGFType() {
  let res: any = await $API.get({
    url: 'edu-inter-server/coop/getCoopTypeList',
    params: {
      pageNo: 1,
      pageSize: 1000
    }
  })
  if (res.code == 'success') {
    options.value = res.data.rows
  }
}

const handleInput = (value) => {
  // formInline.value.phone = value.replace(/\D/g, '')
  formInline.value.phone = value.replace(/\D/g, '')
}
// const handleInput2 = (value) => {
//   formInline.value.relatedSceneTel = value.replace(/\D/g, '')
// }
const handleInput3 = (value) => {
  formInline.value.registeredCapital = value.replace(/\D/g, '')
}
getOptionsByXGFType()

function selectChange(id: any) {
  console.log(id, 'item')

  // formInline.value.relatedType
}

watch(
  () => formInline.value.relatedType,
  (newVal: any) => {
    formInline.value.relatedTypeName = options.value?.find((item: any) => item.id == newVal)?.name
  },
  {
    deep: true
  }
)

/**
 * 根据统一信用代码查询单位
 */
const disabled = ref(false)
const debouncedSearchUnit = useDebounceFn(searchUnit, 500)

async function searchUnit(ucid: string) {
  if (!ucid) return
  const url = 'edu-inter-server/relatedPartner/queryUnitInfoByUcidName'
  const params = { ucid }
  const res: any = await $API.get({ url, params })

  if (res.code == 'success' && res.data) {
    const _data = res.data || {}
    _data.blExpireTime = _data?.blExpireTime ? _data?.blExpireTime : null
    formInline.value = Object.assign(formInline.value, _data)
    if (_data.blFiles) {
      YYZZList.value = _data.blFiles.map((item: any) => {
        return {
          name: item.fileName,
          url: config.downloadFileUrl + item.filePath,
          id: item.id
        }
      })
    }
    disabled.value = true
  }
}

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  .w_add_plan_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_plan_con_box {
    font-size: 0.875rem;
    //background: yellowgreen;
    //display: grid;
    // height: 790px;
    // grid-template-rows: 790px;
    //grid-template-columns: 1fr;
    color: rgba(72, 74, 77, 1);
    height: 100%;

    .bg_text {
      color: rgba(48, 49, 51, 1);
      font-size: 16px;
    }

    .w_content_box {
      //background: red;
      width: 100%;
      height: 100%;
      border: 1px solid #e4e5eb;

      .w_plan_con_one {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .div_con20 {
          height: 20px;
          line-height: 20px;
          display: flex;

          .w_flex-1 {
            flex: 1;
          }

          .w_flex-2 {
            flex: 2;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .w_dialog_from_box {
        border-radius: 3px 3px 3px 3px;
        //border: 1px solid #e4e5eb;
        padding: 20px;

        .w_file_btn {
          width: 136px;
          height: 32px;
          border: 1px solid #527cff;
          text-align: center;
          color: #527cff;
          cursor: pointer;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
        }

        //background: red;
        .w_file_t {
          color: #a8abb2;
        }
      }

      .w_table_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 10px 0 16px;
        margin-bottom: 22px;

        .w_dialog_from_title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
        }

        .w_add_bor {
          width: 100px;
          border: 1px solid #527cff;
          color: #527cff;
          text-align: center;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
          cursor: pointer;
        }

        .w_span_cursor {
          color: #527cff;
          cursor: pointer;
        }

        :deep(.el-table__header .el-table__cell) {
          background-color: rgba(245, 246, 249, 1);
          /* 表头背景色 */
          color: #606266;
          /* 表头字体颜色 */
          font-size: 14px;
          /* 表头字体大小 */
          height: 48px;
        }
      }

      .w_page_box {
        margin-top: 16px;
        height: 32px;
        display: flex;
        justify-content: end;
        padding-right: 24px;
      }
    }
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;

    //background-color: red;
    //padding-right: 20px;
    .w_btn {
      height: 32px;
    }

    .w_btn_bg {
      background-color: rgba(82, 124, 255, 1) !important;
    }
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
