<template>
  <div class="w-full h-full flex">
    <el-card
      v-if="ui.unitOrgType !== '1'"
      class="mr-[20px] org-tree"
      :class="!collapsed ? '' : 'p-20px'"
      :style="{ width: collapsed ? '323px' : '0' }"
    >
      <train-tree :collapsed="collapsed" @serach="searchData" />
    </el-card>
    <div class="w_main_box relative">
      <div class="head_box">
        <HeadTab :changeTabs="changeTabs" @changeTab="changeTab"></HeadTab>
        <div v-auth="['qyllMgrAdd']">
          <el-button type="primary" @click="addDialog" v-if="current == 'serverCom'" color="#527CFF">+ 新增</el-button>
        </div>
      </div>
      <div class="w_c_tab_box">
        <el-scrollbar>
          <component :is="tabs[current]" ref="tableRef" :id="orgCode"></component>
        </el-scrollbar>
      </div>
      <div class="expand" @click="collapsedTree" v-if="ui.unitOrgType !== '1'"></div>
      <EditCom ref="dialogRef" @action="handleFun" :id="orgCode" :name="orgName"></EditCom>
    </div>
  </div>
</template>
<script setup lang="ts">
import HeadTab from '@/components/HeadTab/index.vue'
import TrainTree from '@/components/tree/index.vue'
import { ref } from 'vue'
import { useUserInfo } from '~/store'
import EditCom from './comp/edit.vue'
import serverCom from './comp/serverCom.vue'
import stopCom from './comp/stopCom.vue'

const ui: any = useUserInfo()
const tabs = {
  serverCom,
  stopCom
}
const collapsed = ref(true)
const dialogRef = ref()
const tableRef = ref()
const current = ref('serverCom')
const orgCode = ref<string>(ui.value.unitId)
const orgName = ref<string>(ui.value.unitName)
const isAdd = ref<boolean>(false) // 是否新增
const changeTabs = [
  { label: '服务中', value: '1', width: '100' },
  { label: '已停用', value: '2', width: '100' }
]
const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

// 接受树结构选中数据
const searchData = (obj) => {
  orgCode.value = obj.orgCode
  orgName.value = obj.orgName
  isAdd.value = obj.isAdd
}

function changeTab(item: any) {
  let num = item.value
  switch (num) {
    case '1':
      current.value = 'serverCom'
      break
    case '2':
      current.value = 'stopCom'
      break
    default:
      break
  }
}

const addDialog = () => {
  dialogRef.value.showDialog({})
}

function handleFun() {
  tableRef.value.refreshFetch(orgCode.value)
}
// 创建计划
defineOptions({ name: 'planDetailIndex' })
</script>

<style scoped lang="scss">
.org-tree {
  // min-width: 310px;
  background-color: #ffffff;
  height: calc(100vh - 124px);
  overflow: hidden;
  transition: all 0.5s ease-in-out;

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.w_main_box {
  width: 100%;
  height: 100%;
  flex: 1; // grid-row-gap: 20px;

  .crumbs_box {
    //background-color: yellowgreen;
    background-color: white;
  }

  .head_box {
    //background-color: #0081ff;
    // background-color: white;
    height: 40px;
    display: flex;
    // padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .w_c_tab_box {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    height: calc(100vh - 164px);
    overflow: hidden;

    //background-color: #fff;
    //background-color: red;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }

  .expand {
    background: url('@/assets/expand.png') no-repeat;
    width: 34px;
    height: 36px;
    background-size: 100% 100%;
    position: absolute;
    top: 45%;
    left: -17px;
    cursor: pointer;
  }
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
  background: #eef7ff;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

.ifm-child {
  .w_main_box {
    @apply flex flex-col;

    .w_c_tab_box {
      @apply flex-1 h-0;

      :deep(.w_server_content) {
        @apply h-full flex flex-col;
        .w_server_table {
          @apply flex-1 h-0;
        }
      }

      :deep(.w_stop_content) {
        @apply flex flex-col;
        .w_stop_table {
          @apply flex-1 h-0;
        }
      }
    }
  }
}
</style>
