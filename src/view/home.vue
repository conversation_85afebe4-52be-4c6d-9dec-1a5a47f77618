<!--
 * @Author: jingjf <EMAIL>
 * @Date: 2024-07-09 17:08:57
 * @LastEditors: jingjf <EMAIL>
 * @LastEditTime: 2024-07-10 16:21:37
 * @FilePath: \ehs-partner-mgr\src\view\home.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="bg-red-400 h-300px">
    <breadcrumbNavigation></breadcrumbNavigation>
    <el-button @click="getData">ElButton</el-button>
    <div class="w-300px">
      <myTooltip str="绝地反击豆腐脑羧甲淀粉钠看手机打卡机代发年卡手机打"></myTooltip>
    </div>
  </div>
</template>
<script setup lang="ts">
// import { useUserInfo } from '../store'
import $API from '~/common/api'
const getData = async () => {
  let res: any = await $API.post({
    url: '/examDirect/queryDireTreeList',
    params: {
      height: 30,
      width: 70,
    },
  })
  console.log('🚀 ~ getData ~ res:', res)
}
</script>

<style scoped></style>
