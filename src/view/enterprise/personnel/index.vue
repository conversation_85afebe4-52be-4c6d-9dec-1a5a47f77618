<template>
  <div class="w_personnel_box_com">
    <div class="w_personnel_head_com_box">
      <div class="flex">
        <div
          :class="['head_tab', { w_active: current == 'personnelInformationCom' }]"
          @click="ChangTab('personnelInformationCom')"
        >
          人员信息
        </div>
        <div
          :class="['head_tab', { w_active: current == 'personnelCertificateCom' }]"
          @click="ChangTab('personnelCertificateCom')"
        >
          人员证书
        </div>
        <div
          :class="['head_tab', { w_active: current == 'blacklistInformationCom' }]"
          @click="ChangTab('blacklistInformationCom')"
        >
          已加黑名单
        </div>
      </div>
      <div v-if="current == 'personnelInformationCom'">
        <el-button type="primary" @click="addPersonInfo" class="addbtn" color="#527CFF" v-auth="['renyuanxinzeng']"
          >+ 新增</el-button
        >
        <el-button type="primary" @click="importPerson" class="addbtn" color="#527CFF" v-auth="['renyuandaoru']"
          >导入</el-button
        >
      </div>
    </div>
    <div class="w_personnel_tabs_com_box">
      <el-scrollbar>
        <component :is="tabs[current]" ref="curRef"></component>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import personnelInformationCom from './comp/personnelInformation.vue'
import personnelCertificateCom from './comp/personnelCertificate.vue'
import blacklistInformationCom from './comp/blacklistInformation.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

const tabs = {
  personnelInformationCom,
  personnelCertificateCom,
  blacklistInformationCom
}

const current = ref('personnelInformationCom')

const curRef = ref()
const ChangTab = (val: string) => {
  current.value = val
}

watch(
  () => route.query.tab,
  (val) => {
    let num = sessionStorage.getItem('tabNum')
    if (val && val === 'personnelCom' && num == '2') ChangTab('personnelCertificateCom')
  },
  { immediate: true }
)

const addPersonInfo = () => {
  curRef.value.addPersonInfo()
}
const importPerson = () => {
  curRef.value.importPersonInfo()
}

// 创建计划
defineOptions({ name: 'personnelCom' })
</script>

<style scoped lang="scss">
.w_personnel_box_com {
  .w_personnel_head_com_box {
    //background-color: #0081ff;
    background-color: rgba(220, 228, 244, 1);
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .w_personnel_tabs_com_box {
    background-color: #fff;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
