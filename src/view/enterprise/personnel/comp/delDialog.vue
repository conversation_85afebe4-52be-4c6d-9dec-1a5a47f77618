<template>
  <div class="w_qr_dialog_box">
    <el-dialog v-model="dialogFormVisible">
      <template #header>
        <div class="w_qr_header">
          <div class="mr-11px"></div>
          <div>{{ props.title }}</div>
        </div>
      </template>

      <div class="qr_box" v-if="isDel">
        <div class="w_bg_del"></div>
        <div class="w_box mt-1">{{ props.prompt }}</div>
      </div>
      <div class="qr_box" v-else>
        <div class="w_box mt-1">{{ props.prompt }}</div>
      </div>
      <template #footer>
        <div class="w_qr_dialog-footer">
          <el-button @click="handleClose" plain>取消</el-button>
          <el-button @click="saveData" class="w_btn_bg">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
const emits = defineEmits(['action'])

const props = defineProps({
  title: {
    type: String,
    default: '删除人员',
  },
  prompt: {
    type: String,
    default: '确定要删除吗？',
  },
  isDel: {
    type: Boolean,
    default: true,
  },
})

const dialogFormVisible = ref(false)

const from = ref({
  id: '',
})

const handleClose = async () => {
  dialogFormVisible.value = false
  from.value.id = ''
}

const showDialog = (id) => {
  console.log('id', id)
  from.value.id = ''
  from.value.id = id
  dialogFormVisible.value = true
}

const saveData = async () => {
  emits('action', from.value.id)
  dialogFormVisible.value = false
}

defineExpose({
  showDialog,
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.w_qr_dialog_box {
  font-size: 0.875rem;

  :deep(.el-dialog) {
    width: 488px;
    height: 290px;
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    padding-bottom: 0 !important;
  }

  :deep(.el-dialog__headerbtn) {
    height: 54px;
  }

  .qr_box {
    font-size: 14px;
    display: flex;
    height: 184px;
    //background: red;
    border-top: 1px solid #ebeef5;
    //border-bottom: 1px solid #ebeef5;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .w_bg_del {
      height: 72px;
      width: 72px;
      background: url('../../assets/del.png') no-repeat center center;
      background-size: cover;
    }
  }

  .w_qr_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    //background: red;
    justify-content: start;
    padding-left: 24px;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  :deep(.el-dialog__footer) {
    padding-top: 0 !important;
  }

  .w_qr_dialog-footer {
    margin-top: 4px;
    padding-right: 20px;
    //background: red;
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
