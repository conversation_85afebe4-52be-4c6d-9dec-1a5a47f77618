<template>
  <div class="w_blacklist_box">
    <div class="w_blacklist_title_box py-[5px] pl-[120px]">
      <div class="w_blacklist_prompt italic mr-10">温馨提示</div>
      <div>已被加入黑名单的员工将无法登录小程序，点击登录时，则提示”您已被加入黑名单，将无法登录“。</div>
    </div>
    <div class="w_head_table_div">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="name" label="姓名" width="100" align="center" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="account" label="账号" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="depart" label="部门" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.depart || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="post" label="岗位" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.post || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="insureFiles" label="投保信息" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.insureFiles && scope.row.insureFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.insureFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="idFiles" label="身份证照片" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.idFiles && scope.row.idFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.idFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="documentFile" label="证件照" align="center" show-overflow-tooltip>-->
        <!--          <template #default="scope">-->
        <!--            {{ scope.row.documentFile || '&#45;&#45;' }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column prop="idNumber" label="身份证号" width="180" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.idNumber || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="idPeriod" label="身份证有效期" width="200" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.idPeriod || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄（岁）" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.age || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="创建人" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="scope">
            <!--                <span class="span_cursor mr-5">恢复</span>-->
            <el-button
              type="primary"
              plain
              size="small"
              class="span_cursor mr-1"
              v-auth="['renyuanhemingdanhuifu']"
              @click="showDelDialog(scope.row)"
              >恢复</el-button
            >

            <el-button
              type="primary"
              plain
              size="small"
              class="span_cursor mr-1"
              v-auth="['renyuanhemingdanchakanyuanyin']"
              @click="handleBlacklist(scope.row)"
              >查看原因</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <black-reason-com ref="detailRef"></black-reason-com>

    <del-dialog-com
      ref="delRef"
      @action="handleDel"
      title="已加入黑名单"
      prompt="确定要恢复吗？"
      :isDel="false"
    ></del-dialog-com>

    <el-image-viewer
      v-if="showPreview"
      :url-list="currentPreviewImages"
      show-progress
      :initial-index="0"
      @close="showPreview = false"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import blackReasonCom from './blackReasonDialog.vue'
import { useRoute } from 'vue-router'
// import imgViewList from '@/components/imgViewList/index.vue'
import delDialogCom from './delDialog.vue'
import { ElMessage } from 'element-plus'
import { useUserInfo } from '~/store'
import config from '~/config'
const route = useRoute()

const { tableHeight } = mixTableHeight({ subtractHeight: 525 })

const detailRef = ref()

const delRef = ref()

const form = ref({
  userName: '',
  userType: '',
  signIn: ''
})

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const tableData = ref([])

const unitId = ref<any>('')
const userId = ref<string>('')
const ui: Record<string, any> = useUserInfo()

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/staff/queryListWithProj',
    params: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      sysCode: 'web',
      isBlack: '1',
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

// 恢复弹框
const showDelDialog = (row) => {
  // deleteId.value = row.id
  userId.value = row.orgCode
  delRef.value.showDialog(row.id)
}

const handleDel = (id) => {
  handleRestore(id)
}

const handleRestore = async (id) => {
  let res: any = await $API.post({
    url: 'edu-app-server/api/workbench/staff/blackList',
    data: {
      id,
      isBlack: 0,
      unitId: userId.value
    }
  })
  if (res.code == 'success') {
    ElMessage({
      message: '恢复成功',
      type: 'success'
    })
    getList()
  }
}

const handleBlacklist = (row: any) => {
  detailRef.value.showDialog(row.id)
}

// 当前预览的图片列表
const currentPreviewImages = ref<string[]>([])
const showPreview = ref(false)

// 显示图片预览
const showImages = (imgList: any[]) => {
  if (!imgList || imgList.length === 0) return

  currentPreviewImages.value = imgList.map((item: any) => {
    return config.downloadFileUrl + item.filePath
  })

  showPreview.value = true
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
    }
    console.log('参数变化了2', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)
// 创建计划
defineOptions({ name: 'blacklistInformationIndex' })
</script>

<style scoped lang="scss">
.w_blacklist_box {
  padding: 24px;
  font-size: 14px;

  .w_blacklist_title_box {
    height: 56px;
    width: 100%;
    background: url('@/view/resumeManagement/comp/img/bj.png') no-repeat center center;
    background-size: contain;
    display: flex;
    justify-content: start;
    align-items: center;

    .w_blacklist_prompt {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 30px;
      color: #527cff;
      //text-align: left;
    }
  }

  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .w_head_info {
    padding: 0 24px 0px 24px;

    > div {
      height: 44px;
      line-height: 44px;
    }

    .w_div_test_info {
      display: flex;
      justify-content: start;

      //background: yellowgreen;
      > div {
        width: 260px;
        //background: red;
        margin-right: 10px;
      }
    }
  }

  .head_box {
    //background-color: #0081ff;
    background-color: white;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .w_head_table_div {
    margin-top: 20px;

    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    .w_head_table_input {
      //display: flex;
      //height: 32px;
      width: 100%;
      //justify-content: space-between;
      //align-items: center;
      //background: red;
    }

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  :deep(.el-table .el-table__cell) {
    z-index: revert-layer;
  }
}

.upload-status {
  &.uploaded {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
      text-decoration: underline;
    }
  }

  &.not-uploaded {
    color: #999999;
    cursor: default;
  }
}
</style>
