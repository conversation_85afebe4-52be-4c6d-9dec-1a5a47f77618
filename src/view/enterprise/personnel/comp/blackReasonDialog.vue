<template>
  <div class="w_qr_dialog_box">
    <el-dialog v-model="dialogFormVisible">
      <template #header>
        <div class="w_qr_header">
          <div class="mr-11px"></div>
          <div>加入黑名单原因</div>
        </div>
      </template>

      <div class="qr_box">
        <div class="w_reason_box">
          {{ from.textarea }}
          <!--          <el-input-->
          <!--              v-model="from.textarea"-->
          <!--              type="textarea"-->
          <!--              placeholder="请输入加入黑名单原因"-->
          <!--              maxlength="100"-->
          <!--              show-word-limit-->
          <!--              :rows="8"-->
          <!--              style="width: 450px"-->
          <!--              disabled-->
          <!--          />-->
        </div>

      </div>
      <!--      <template #footer>-->
      <!--        <div class="w_qr_dialog-footer">-->
      <!--          <el-button @click="handleClose" class="w_btn_bg">关闭</el-button>-->
      <!--        </div>-->
      <!--      </template>-->
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import $API from '@/common/api'

const dialogFormVisible = ref(false)

const from = ref({
  textarea: '',
  id: '',
})
const handleClose = async () => { }

const showDialog = (id) => {
  console.log('id', id)
  dialogFormVisible.value = true
  from.value.id = id
  getData()
}

const getData = async () => {
  let res:any = await $API.get({
    url: 'edu-app-server/api/workbench/staff/getReason',
    params: {
      id: from.value.id,
    },
  })
  if (res.code == 'success') {
    from.value.textarea = res.data
    // dialogFormVisible.value = false
  }
}

defineExpose({
  showDialog,
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.w_qr_dialog_box {
  font-size: 0.875rem;

  :deep(.el-dialog) {
    width: 500px;
    height: 360px;
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    padding-bottom: 0 !important;
  }

  :deep(.el-dialog__headerbtn) {
    height: 54px;
  }

  .qr_box {
    font-size: 14px;
    display: flex;
    height: 240px;
    //background: red;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    padding: 30px 24px 18px;
  }

  .w_reason_box {
    border-radius: 3px 3px 3px 3px;
    border: 1px solid #E4E5EB;
    width: 100%;
    height: 180px;
    //background-color: red;
    padding: 10px;
  }

  .w_qr_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    //background: red;
    justify-content: start;
    padding-left: 24px;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_qr_dialog-footer {
    margin-top: 6px;
    padding-right: 24px;
    //background: red;
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
