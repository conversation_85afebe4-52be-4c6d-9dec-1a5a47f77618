<template>
  <div class="w_qr_dialog_box">
    <el-dialog v-model="dialogFormVisible">
      <template #header>
        <div class="w_qr_header">
          <div class="mr-11px"></div>
          <div>恢复人员</div>
        </div>
      </template>

      <div class="qr_box">
        确定要恢复吗？
        <div class="w_box"></div>
      </div>

      <template #footer>
        <div class="w_qr_dialog-footer">
          <el-button @click="handleClose" plain>取消</el-button>
          <el-button @click="saveData" class="w_btn_bg">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import $API from '@/common/api'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
const emits = defineEmits(['action'])
const dialogFormVisible = ref(false)

interface RuleForm {
  textarea: string
  id: string
}

const from = ref<RuleForm>({
  textarea: '',
  id: '',
})

const ruleFormRef = ref<FormInstance>()

const rules = ref<FormRules>({
  textarea: [
    {
      required: true,
      message: '请输入黑名单原因',
      trigger: ['blur', 'change'],
    },
  ],
})

const handleClose = async () => {
  dialogFormVisible.value = false
  from.value.id = ''
  from.value.textarea = ''
}

const showDialog = (id) => {
  console.log('id', id)
  dialogFormVisible.value = true
  from.value.id = id
  from.value.textarea = ''
}

const saveData = async () => {
  try {
    await ruleFormRef.value?.validate()
  } catch (e) {
    console.log(e)
    return
  }
  console.log('fasongqing黑名單')
  let res: any = await $API.post({
    url: 'edu-app-server/api/workbench/staff/blackList',
    data: {
      id: from.value.id,
      reason: from.value.textarea,
      isBlack: 1,
    },
  })
  if (res.code == 'success') {
    dialogFormVisible.value = false
    ElMessage({
      message: '加入黑名单成功',
      type: 'success',
    })
    emits('action')
  }
}

defineExpose({
  showDialog,
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.w_qr_dialog_box {
  font-size: 0.875rem;

  :deep(.el-dialog) {
    width: 500px;
    height: 360px;
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    padding-bottom: 0 !important;
  }

  :deep(.el-dialog__headerbtn) {
    height: 54px;
  }

  .qr_box {
    font-size: 14px;
    display: flex;
    height: 240px;
    //background: red;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    padding: 30px 24px 18px;
    overflow: hidden;

    //.w_box {
    //  height: 100px;
    //  background-color: red;
    //}
  }

  .w_qr_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    //background: red;
    justify-content: start;
    padding-left: 24px;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_qr_dialog-footer {
    margin-top: 6px;
    padding-right: 24px;
    //background: red;
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
