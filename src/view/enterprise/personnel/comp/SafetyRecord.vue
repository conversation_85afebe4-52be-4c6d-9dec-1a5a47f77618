<template>
  <el-drawer v-model="visible" direction="rtl" size="85%" :with-header="false">
    <div class="safety-record">
      <div class="close-btn" @click="visible = false">
        <el-icon>
          <Close />
        </el-icon>
      </div>

      <el-tabs v-model="activeTab" class="safety-tabs">
        <el-tab-pane label="基本情况" name="basic">
          <BaseInfo :record-data="recordData" />
        </el-tab-pane>

        <el-tab-pane label="持证情况" name="certificates">
          <CertificateInfo :record-data="certifData" :loading="loading" />
        </el-tab-pane>

        <el-tab-pane label="培训记录" name="training">
          <TrainingInfo :record-data="recordData" @update:page="handleTrainingPage" @update:size="handleTrainingSize" />
        </el-tab-pane>

        <el-tab-pane label="作业记录" name="operations">
          <OperationInfo
            :record-data="recordData"
            :loading="operationsLoading"
            @update:page="handleOperationsPage"
            @update:size="handleOperationsPage"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import BaseInfo from '@/components/safetyRecord/baseInfo.vue'
import CertificateInfo from '@/components/safetyRecord/certificateHold.vue'
import TrainingInfo from '@/components/safetyRecord/trainRecord.vue'
import OperationInfo from '@/components/safetyRecord/workRecord.vue'
import { Close } from '@element-plus/icons-vue'
import { defineEmits, defineProps, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'

const route = useRoute()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  option: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])
const loading = ref(false)
const visible = ref(false)
const recordData = ref<any>({})
const certifData = ref<any>([])

// 当前激活的tab
const activeTab = ref('basic')

// 添加作业记录加载状态
const operationsLoading = ref(false)

const getBase = async () => {
  try {
    const [res1, res2] = await Promise.all([
      $API.get({
        url: 'edu-app-server/api/workbench/safelyResume/personalBaseInfo',
        params: { id: props.option.id, unitId: route.query.id ? route.query.id : props.option.unitId }
      }),
      $API.post({
        url: 'edu-app-server/api/workbench/safelyResume/wokerRecord',
        params: { userId: props.option.id, phone: props.option.phone }
      })
    ])
    baseInfo.value = res1?.data
    // 处理基础数据
    recordData.value = {
      ...res1?.data,
      insuranceDocs:
        res1?.data?.insureFiles?.map((file) => ({
          url: file.fileUrl
        })) || [],
      idPhotos:
        res1?.data?.idFiles?.map((file) => ({
          url: file.fileUrl
        })) || [],
      workHistory: res2?.data || []
    }
  } catch (error) {
    console.error('获取基本信息失败:', error)
  }
}
// 监听visible变化
watch(
  () => props.modelValue,
  async (val) => {
    visible.value = val
    // 当抽屉打开时，获取基本信息
    if (val && props.option.id) {
      getBase()
    }
  }
)

// 监听drawer关闭
watch(
  () => visible.value,
  (val) => {
    activeTab.value = 'basic'
    emit('update:modelValue', val)
    // 关闭时清空数据
    if (!val) {
      recordData.value = {}
      certifData.value = []
    }
  }
)
const baseInfo = ref({
  name: '',
  depart: '',
  post: ''
})

// 监听tab切换
watch(
  () => activeTab.value,
  async (newTab) => {
    if (!props.option.id) return

    try {
      let res
      switch (newTab) {
        case 'basic':
          getBase()
          break

        case 'certificates':
          loading.value = true
          res = await $API.get({
            url: 'edu-app-server/api/workbench/safelyResume/certificateInfoList',
            params: {
              relId: route.query.id ? route.query.id : props.option.unitId,
              staffId: props.option.id,
              sysCode: 'web',
              type: '0',
              pageNo: -1,
              pageSize: 10
            }
          })
          certifData.value = res?.data.rows || []
          loading.value = false
          break

        case 'training':
          try {
            const [trainStats, trainList] = await Promise.all([
              $API.get({
                url: 'edu-app-server/api/workbench/safelyResume/detail',
                params: {
                  userId: props.option.id,
                  deptId: props.option.orgCode ? props.option.orgCode : props.option.unitId
                }
              }),
              $API.get({
                url: 'edu-app-server/api/workbench/safelyResume/queryTaskListByUserId',
                params: {
                  userId: props.option.id,
                  deptId: props.option.orgCode ? props.option.orgCode : props.option.unitId,
                  pageNo: 1,
                  pageSize: 20
                }
              })
            ])
            recordData.value = {
              stats: trainStats?.data || {},
              list: trainList?.data.rows || [],
              total: trainList?.data?.total || 0
            }
          } catch (error) {
            console.error('获取培训记录失败:', error)
          }
          break

        case 'operations':
          try {
            operationsLoading.value = true
            const [opStats, opList] = await Promise.all([
              $API.post({
                url: 'edu-app-server/api/workbench/safelyResume/queryUserOperBaseStatic',
                data: { personnelIds: props.option.id, projectId: '' }
              }),
              $API.post({
                url: 'edu-app-server/api/workbench/safelyResume/queryUserOperBaseList',
                data: {
                  personnelIds: props.option.id,
                  projectId: '',
                  pageNo: 1,
                  pageSize: -1
                }
              })
            ])
            recordData.value = {
              stats: opStats?.data || {},
              list: opList?.data || [],
              total: opList?.data?.total || 0
            }
          } catch (error) {
            console.error('获取作业记录失败:', error)
          } finally {
            operationsLoading.value = false
          }
          break
      }
    } catch (error) {
      console.error(`获取${newTab}数据失败:`, error)
    }
  },
  { immediate: true }
)

// 修改处理培训记录分页的方法
const handleTrainingPage = async (page: number, size?: number) => {
  if (activeTab.value !== 'training' || !props.option.id) return

  try {
    const trainList = await $API.get({
      url: 'edu-app-server/api/workbench/safelyResume/queryTaskListByUserId',
      params: {
        userId: props.option.id,
        deptId: props.option.orgCode,
        pageNo: page,
        pageSize: size || 20
      }
    })

    recordData.value = {
      ...recordData.value,
      list: trainList?.data.rows || [],
      total: trainList?.data?.total || 0
    }
  } catch (error) {
    console.error('获取培训记录分页数据失败:', error)
  }
}

// 添加处理页面大小变化的方法
const handleTrainingSize = (page: number, size: number) => {
  handleTrainingPage(page, size)
}

// 添加处理作业记录分页的方法
const handleOperationsPage = async (page: number, size: number) => {
  if (activeTab.value !== 'operations' || !props.option.id) return

  try {
    operationsLoading.value = true
    const opList = await $API.post({
      url: 'edu-inter-server/perManager/queryUserOperBaseList',
      data: {
        personnelIds: props.option.id,
        projectId: '',
        pageNo: page,
        pageSize: size
      }
    })

    recordData.value = {
      ...recordData.value,
      list: opList?.data || [],
      total: opList?.data?.total || 0
    }
  } catch (error) {
    console.error('获取作业记录分页数据失败:', error)
  } finally {
    operationsLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.safety-record {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .close-btn {
    position: absolute;
    right: 10px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    z-index: 1;

    .el-icon {
      font-size: 20px;
      color: #909399;
      transition: all 0.3s;
    }

    &:hover {
      .el-icon {
        transform: rotate(90deg);
        color: #606266;
      }
    }
  }

  .safety-tabs {
    flex: 1;
    :deep(.el-tabs__header) {
      margin: -10px -20px 0;
      background: #fff;
      padding: 0 20px;
      border-bottom: 1px solid #e4e7ed;
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__item) {
      padding: 0 20px;
      height: 48px;
      line-height: 48px;
      font-size: 14px;
      color: #606266;
      transition: all 0.3s;

      &.is-active {
        color: #409eff;
        font-weight: 500;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 20%;
          width: 60%;
          height: 2px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      &:hover {
        color: #409eff;
      }
    }

    :deep(.el-tabs__content) {
      background: #fff;
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      border-radius: 8px;
    }
  }

  .basic-info {
    border-radius: 8px;

    .info-header {
      display: flex;

      .avatar {
        width: 120px;
        height: 120px;
        margin-right: 24px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .info-content {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;

        .info-item {
          .label {
            color: #909399;
            margin-right: 8px;
          }
        }
      }
    }
  }

  .work-history {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
  }
}

// 各个tab页内容区域的基础样式
.certificates-content,
.training-content,
.operations-content {
  background: rgba(187, 204, 243, 1);
  border-radius: 8px;
  padding: 24px;
  min-height: 200px;
}
</style>
