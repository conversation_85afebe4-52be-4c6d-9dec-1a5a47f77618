<template>
  <div class="w_detail_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer">
      <template #header>
        <div class="w_detail_header_box">
          <div class="mr-11px"></div>
          <div>复审记录</div>
        </div>
      </template>
      <div class="w_detail_box">
        <div class="w_detail_info_box mb-5">
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">姓名：</div>
            <div>{{ formInline.staffName || '--' }}</div>
          </div>
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">加入时间：</div>
            <div>{{ formInline.createTime || '---' }}</div>
          </div>
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">手机号：</div>
            <div>{{ formInline.phone || '--' }}</div>
          </div>
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">账号：</div>
            <div>{{ formInline.account || '--' }}</div>
          </div>
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">证书名称：</div>
            <div>{{ formInline.certificateName || '--' }}</div>
          </div>
          <div class="mb-4 w_detail_title flex">
            <div class="mr-4">是否复审：</div>
            <div>{{ formInline.isReview == 1 ? '是' : '否' }}</div>
          </div>
          <div class="w_detail_title flex mb-4">
            <div class="mr-4">下次复审日期：</div>
            <div>{{ formInline.nextReviewTime && formInline.isReview == 1 ? formInline.nextReviewTime : '--' }}</div>
          </div>
          <div class="w_detail_img_box flex">
            <div class="mr-4">证书照片：</div>
            <div class="w_detail_img">
              <imgViewList :img-list="formInline.certificateFiles || []" height="114px" width="114px" />
            </div>
          </div>
        </div>

        <div>
          <el-table :data="tableData" stripe style="width: 100%; height: 360px">
            <el-table-column type="index" width="60" label="序号" align="center" />
            <el-table-column prop="lastReviewTime" label="最近复审日期" align="center" show-overflow-tooltip />
            <el-table-column prop="nextReviewTime" label="下次复审日期" align="center" show-overflow-tooltip />
            <el-table-column prop="createName" label="记录人" align="center" show-overflow-tooltip />
            <el-table-column prop="createTime" label="记录时间" align="center" show-overflow-tooltip />
          </el-table>
        </div>
        <div class="w_page_box mt-5">
          <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import $API from '@/common/api'
import imgViewList from '@/components/imgViewList/index.vue'

const drawer = ref(false)

const formInline = ref<any>({})

const detailId = ref('')

const tableData = ref<any>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const getDetail = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/certificate/detail',
    params: {
      id: detailId.value,
    },
  })
  console.log(res, 'res---------------')
  if (res.code == 'success') {
    formInline.value = res.data
  }
}

const getTableList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/certificate/getRecordList',
    params: {
      id: detailId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
    },
  })

  tableData.value = res.data.rows
  total.value = res.data.total


}

const handleSizeChange = () => {
  pageNo.value = 1
  getTableList()
}
const handleCurrentChange = () => {
  getTableList()
}

// const handleClose = () => {
//   drawer.value = false
// }

const showDialog = (id = '') => {
  pageNo.value = 1
  pageSize.value = 10
  detailId.value = id
  getDetail()
  getTableList()
  drawer.value = true
}

defineExpose({
  showDialog,
})
// 创建计划
defineOptions({ name: 'coursePersonnelDialog' })
</script>

<style scoped lang="scss">
.w_detail_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
    font-size: 16px;
    font-weight: 700;
    color: #333;
  }

  .w_detail_header_box {
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .w_detail_box {
    //display: grid;
    //height: 100%;
    //grid-template-rows: 1fr;
    //grid-template-columns: 1fr;

    .w_detail_info_box {
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;
      padding: 20px 24px;

      .w_img_detail_base {
        width: 200px;
        height: 100px;
        //background: red;
      }
    }
  }

  .w_detail_class_box {
    margin-top: 20px;
    padding-left: 18px;
    padding-bottom: 20px;
    border-left: 1px dashed #527cff;
    position: relative;

    .w_detail_class_bg {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      text-align: center;
      color: white;
      position: absolute;
      top: 0;
      left: -10px;
      background: #527cff;
    }

    .w_detail_class_item {
      padding: 0 18px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;

      //> div {
      //  border-bottom: 1px solid #d8d8d8;
      //}
      >div:not(:last-child) {
        border-bottom: 1px solid #d8d8d8;
      }

      .w_detail_div_box {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        //background: #9a6e3a;
        //border: 1px solid #D8D8D8;

        .w_detail_div_l {
          width: 380px;
          //background: #28a458;
          display: flex;

          .w_detail_div_l_hide {
            width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            //display: inline-block;
            //background: red;
          }
        }

        .w_detail_div_r {
          width: 66px;
          height: 32px;
          border: 1px solid #527cff;
          line-height: 32px;
          text-align: center;
          color: #527cff;
        }
      }
    }
  }

  .w_detail_comment_box {
    //background: #0081ff;
    //padding: 0 18px;

    padding-left: 18px;
    padding-bottom: 20px;

    position: relative;

    .w_detail_class_bg {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      text-align: center;
      color: white;
      position: absolute;
      top: 0;
      left: -10px;
      background: #527cff;
    }

    .w_detail_c_head_box {
      padding: 20px 18px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;

      .w_detail_head_item {
        border-bottom: 1px solid #e4e5eb;

        .w_detail_head_title {
          height: 22px;
          font-weight: 500;
          font-size: 16px;
          color: #303133;
          line-height: 22px;
          width: 460px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 16px;
        }

        .w_detail_comment_con {
          display: flex;

          >div {
            width: 160px;
            height: 20px;
            line-height: 20px;
            margin-bottom: 20px;
          }
        }
      }

      .w_detail_examination_box {
        .w_detail_examination_item {
          display: flex;
          justify-content: space-between;

          .w_detail_item_l {
            display: flex;
            align-items: center;

            .w_detail_item_round {
              width: 6px;
              height: 6px;
              background: #527cff;
              border-radius: 50%;
            }

            .w_detail_red {
              color: rgba(209, 0, 0, 1);
            }

            .w_detail_green {
              color: rgba(36, 182, 105, 1);
            }
          }
        }
      }
    }
  }

  .w_page_box {
    display: flex;
    justify-content: end;
  }

  .w_detail_title {
    height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }

  .w_detail_img_box {
    height: 114px;
    font-weight: 500;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }

  .w_detail_img {
    width: 114px;
    height: 114px;
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
