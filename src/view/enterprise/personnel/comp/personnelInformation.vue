<template>
  <div class="w_sign_box">
    <div class="w_head_table_div">
      <div class="flex mb-4 justify-between">
        <div class="w_table_title w_bg1">
          <div class="pl-[30px] mt-[20px] mb-[8px] text-[18px]">总人数</div>
          <div>
            <span class="pl-[30px] mt-[8px] text-3xl">{{ dataCountObj.totalCount }}</span>
            <span class="text-[18px]">人</span>
          </div>
        </div>
        <div class="w_table_title w_bg2">
          <div class="pl-[30px] mt-[20px] mb-[8px] text-[18px]">特种作业人数</div>
          <div>
            <span class="pl-[30px] mt-[8px] text-3xl">{{ dataCountObj.specialCount }}</span>
            <span class="text-[18px]">人</span>
          </div>
        </div>
        <div class="w_table_title w_bg3">
          <div class="pl-[30px] mt-[20px] mb-[8px] text-[18px]">人员证书总数</div>
          <div>
            <span class="pl-[30px] mt-[8px] text-3xl">{{ dataCountObj.certCount }}</span>
            <!--            <span class="text-[18px]">人</span>-->
          </div>
        </div>
        <div class="w_table_title w_bg4">
          <div class="pl-[30px] mt-[20px] mb-[8px] text-[18px]">已被加黑名单数</div>
          <div>
            <span class="pl-[30px] mt-[8px] text-3xl">{{ dataCountObj.blackCount }}</span>
            <!--            <span class="text-[18px]">人</span>-->
          </div>
        </div>
      </div>
      <!--      <el-button type="primary" @click="addPersonInfo" class="addbtn">新增</el-button>-->
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="name" label="姓名" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.name || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="center" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.phone || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="center" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.account || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="depart" label="部门" align="center" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.depart || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="post" label="岗位" align="center" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.post || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="insureFiles" label="投保信息（工伤）" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.insureFiles && scope.row.insureFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.insureFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="idFiles" label="身份证照片" align="center" width="160" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.idFiles && scope.row.idFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.idFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="documentFiles" label="证件照（免冠照）" align="center" width="160" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.documentFiles && scope.row.documentFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.documentFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="idNumber" label="身份证号" align="center" width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.idNumber || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="idPeriod" label="身份证有效期" align="center" width="200" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.idPeriod || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄（岁）" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.age || '--' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="specialOperation"
          label="否是特种作业人员"
          align="center"
          width="140"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="scope.row.specialOperation == 1">是</span>
            <span v-else-if="scope.row.specialOperation == 0">否</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="创建人" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="200" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" plain size="small" @click="viewSafetyRecord(scope.row)">安全履历</el-button>
            <el-button
              type="danger"
              plain
              size="small"
              @click="handleBlacklist(scope.row)"
              v-if="hasPermission(scope.row, 'renyuanheimingdan')"
              >加入黑名单</el-button
            >
            <el-dropdown v-if="shouldShowDropdown(scope.row)">
              <el-button type="primary" plain size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="edit(scope.row)" v-if="hasPermission(scope.row, 'renyuanbianji')"
                    >编辑</el-dropdown-item
                  >
                  <el-dropdown-item @click="showDelDialog(scope.row)" v-if="hasPermission(scope.row, 'renyuanshanchu')"
                    >删除</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <black-dialog-com ref="detailRef" @action="handleData"></black-dialog-com>

    <del-dialog-com ref="delRef" @action="handleDel" title="删除人员信息"></del-dialog-com>

    <PersonInfo ref="dialogRef" @action="handleFun" />

    <safety-record v-model="safetyRecordVisible" :option="currentRecord" />

    <import-person ref="importPersonRef" @action="handleData"></import-person>

    <el-image-viewer
      v-if="showPreview"
      :url-list="currentPreviewImages"
      show-progress
      :initial-index="0"
      @close="showPreview = false"
    />
  </div>
</template>
<script setup lang="ts">
// import imgViewList from '@/components/imgViewList/index.vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { ref, watch, nextTick } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import blackDialogCom from './blackDialog.vue'
import delDialogCom from './delDialog.vue'
import PersonInfo from './personInfo.vue'
import SafetyRecord from './SafetyRecord.vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'
import { useAuthByKey } from '~/hooks/useAuthByKey'
import importPerson from './importPerson.vue'
import config from '~/config'

const ui: Record<string, any> = useUserInfo()
const { tableHeight } = mixTableHeight({ subtractHeight: 465 })
const route = useRoute()
const detailRef = ref()
const dialogRef = ref()
const importPersonRef = ref()
const delRef = ref()
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const tableData = ref([])
const unitId = ref<any>('')

const dataCountObj: any = ref({
  blackCount: 0,
  certCount: 0,
  specialCount: 0,
  totalCount: 0
})
/**
 * 承包商员工与负责人进入权限判断
 * 员工只能修改自己  负责人可以修改全部
 */
const getRoles = (row: any) => {
  if (ui.value.roleMarks === 'xfgmrjs') {
    return row.id === ui.value.id
  } else {
    return true
  }
}

/**
 * 综合权限检查方法
 * @param row 数据行
 * @param authKey 权限标识
 * @returns 是否有权限
 */
const hasPermission = (row: any, authKey: string) => {
  const hasAuth = useAuthByKey(authKey)

  // 正式员工：只需要检查 v-auth 权限
  if (ui.value.roleMarks !== 'xfgmrjs' && ui.value.roleMarks) {
    return hasAuth
  }

  // 第三方员工/负责人：需要同时检查 v-auth 权限和 getRoles 权限
  const hasRoles = getRoles(row)
  return hasAuth && hasRoles
}

/**
 * 判断是否应该显示下拉菜单按钮
 * 当编辑和删除都不满足条件时，不显示整个下拉按钮
 */
const shouldShowDropdown = (row: any) => {
  const canEdit = hasPermission(row, 'renyuanbianji')
  const canDelete = hasPermission(row, 'renyuanshanchu')

  // 只有当至少有一个操作权限时才显示下拉按钮
  return canEdit || canDelete
}

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/staff/queryListWithProj',
    params: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      sysCode: 'web',
      isBlack: '0',
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

// 新增人员信息
const addPersonInfo = () => {
  dialogRef.value.showDialog({})
}
// 编辑人员信息
const edit = (row) => {
  dialogRef.value.showDialog(row)
}
const handleFun = () => {
  getList()
}
const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

const handleData = () => {
  getList()
  getPerNum()
}

// const deleteId = ref('')
// 删除弹框
const showDelDialog = (row) => {
  // deleteId.value = row.id
  delRef.value.showDialog(row.id)
}

const handleDel = (id) => {
  sendDel(id)
}

const sendDel = async (id) => {
  let res: any = await $API.post({
    url: 'edu-app-server/api/workbench/staff/delete',
    data: {
      id,
      unitId: route.query.id
    }
  })
  if (res.code == 'success') {
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
    getList()
    getPerNum()
  }
}

const handleBlacklist = (row: any) => {
  detailRef.value.showDialog(row)
}

const getPerNum = async () => {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/queryRelateStatictisWithProj',
    params: {
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })
  if (res.data) {
    dataCountObj.value.totalCount = res.data.totalCount || 0
    dataCountObj.value.specialCount = res.data.specialCount || 0
    dataCountObj.value.certCount = res.data.certCount || 0
    dataCountObj.value.blackCount = res.data.blackCount || 0
  }
  console.log('res+++++++++++++', res)
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
      getPerNum()
    }
    console.log('参数变化了2', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)
// 创建计划
defineOptions({ name: 'basicInfoPlanIndex' })

// 安全履历显示状态
const safetyRecordVisible = ref(false)
// 当前查看的记录
const currentRecord = ref({})

// 查看安全履历
const viewSafetyRecord = (row) => {
  currentRecord.value = row
  safetyRecordVisible.value = true
}

const importPersonInfo = () => {
  importPersonRef.value.showDialog()
}

// 当前预览的图片列表
const currentPreviewImages = ref<string[]>([])
const showPreview = ref(false)

// 显示图片预览
const showImages = (imgList: any[]) => {
  if (!imgList || imgList.length === 0) return

  currentPreviewImages.value = imgList.map((item: any) => {
    return config.downloadFileUrl + item.filePath
  })

  showPreview.value = true
}

defineExpose({ addPersonInfo, importPersonInfo })
</script>

<style scoped lang="scss">
.w_sign_box {
  padding: 24px;
  font-size: 14px;

  //background-color: white;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .addbtn {
    float: right;
    margin-bottom: 10px;
  }

  .w_head_info {
    padding: 0 24px 0px 24px;

    > div {
      height: 44px;
      line-height: 44px;
    }

    .w_div_test_info {
      display: flex;
      justify-content: start;

      //background: yellowgreen;
      > div {
        width: 260px;
        //background: red;
        margin-right: 10px;
      }
    }
  }

  .head_box {
    //background-color: #0081ff;
    background-color: white;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .table_box {
    background-color: chocolate;
    background-color: white;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 50px;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_head_table_div {
    //padding-top: 20px;
    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    .w_head_table_input {
      //display: flex;
      //height: 32px;
      width: 100%;
    }

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  .w_red_bg {
    height: 8px;
    width: 8px;
    background-color: #f62a2a;
    border-radius: 50%;
    margin-right: 6px;
  }

  .w_green_bg {
    height: 8px;
    width: 8px;
    background-color: #16ac3e;
    border-radius: 50%;
    margin-right: 6px;
  }

  :deep(.el-table-fixed-column--right) {
    .cell {
      display: flex;
      justify-content: space-evenly;
    }

    .el-button {
      margin-left: 0 !important;
    }
  }
}

.w_table_title {
  width: 260px;
  height: 116px;
  //line-height: 40px;
  //text-align: center;
  border-radius: 6px;
  color: rgba(255, 255, 255, 1);
}

.w_bg1 {
  background: url('../../assets/bg_per1.png') no-repeat 100% 100%;
}

.w_bg2 {
  background: url('../../assets/bg_per2.png') no-repeat 100% 100%;
}

.w_bg3 {
  background: url('../../assets/bg_per3.png') no-repeat 100% 100%;
}

.w_bg4 {
  background: url('../../assets/bg_per4.png') no-repeat 100% 100%;
}

.upload-status {
  &.uploaded {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
      text-decoration: underline;
    }
  }

  &.not-uploaded {
    color: #999999;
    cursor: default;
  }
}
</style>
