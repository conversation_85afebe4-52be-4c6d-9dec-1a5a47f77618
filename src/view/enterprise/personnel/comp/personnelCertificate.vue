<template>
  <div class="w_text_box">
    <div class="w_head_table_div">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="staffName" label="姓名" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="account" label="账号" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="depart" label="部门" align="center" width="100" show-overflow-tooltip />
        <!-- <el-table-column prop="post" label="岗位" align="center" width="140" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.post || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="certificateTypeName" label="证书类型" width="140" align="center" show-overflow-tooltip />
        <el-table-column prop="certificateName" label="证书名称" width="140" align="center" show-overflow-tooltip />

        <el-table-column prop="certificateNo" label="证书编号" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.certificateNo || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="issueStartTime" label="证书有效期" width="180" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.issueStartTime || '--' }} - {{ scope.row.issueEndTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="certificateFiles" label="证书照片" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.certificateFiles && scope.row.certificateFiles.length > 0">
              <span class="upload-status uploaded" @click="showImages(scope.row.certificateFiles)"> 已上传 </span>
            </template>
            <template v-else>
              <span class="upload-status not-uploaded">未上传</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          prop="certificateCheckUrl"
          label="证书查询地址"
          width="140"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <a :href="scope.row.certificateCheckUrl" target="_blank" v-if="scope.row.certificateCheckUrl">{{
              scope.row.certificateCheckUrl
            }}</a>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="institution" label="发证机构" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.institution || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="issueStartTime" label="发证日期" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.issueStartTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="isReview" label="是否复审" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.isReview == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="nextReviewTime" label="下次复审日期" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.isReview == 1">
              <div>{{ scope.row.nextReviewTime || '--' }}</div>
              <div
                :style="{
                  color:
                    scope.row.status === '正常' ? '#67c23a' : scope.row.status === '即将逾期' ? '#e6a23c' : '#f56c6c',
                  marginTop: '4px'
                }"
              >
                ({{ scope.row.status }})
              </div>
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="证书状态" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '正常' ? 'success' : scope.row.status === '即将逾期' ? 'warning' : 'danger'"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="earlyWarning" label="预警天数" align="center" width="120" show-overflow-tooltip>
          <template #default="scope">
            <span style="color: #f56c6c">{{ scope.row.earlyWarning || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建日期" align="center" width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              plain
              size="small"
              class="span_cursor mr-1"
              v-auth="['renyuanzhengshushanchu']"
              @click="showDelDialog(scope.row)"
              >删除</el-button
            >
            <el-button
              type="primary"
              plain
              size="small"
              class="span_cursor mr-1"
              v-auth="['renyuanzhengshufushen']"
              @click="handleBlacklist(scope.row)"
              >复审记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <review-record-com ref="detailRef"></review-record-com>

    <del-dialog-com ref="delRef" @action="handleDel" title="删除人员证书"></del-dialog-com>

    <el-image-viewer
      v-if="showPreview"
      :url-list="currentPreviewImages"
      show-progress
      :initial-index="0"
      @close="showPreview = false"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import reviewRecordCom from './reviewRecordDialog.vue'
// import imgViewList from '@/components/imgViewList/index.vue'
import delDialogCom from './delDialog.vue'
import config from '~/config'
const { tableHeight } = mixTableHeight({ subtractHeight: 465 })

import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserInfo } from '~/store'

const route = useRoute()

const detailRef = ref()

const delRef = ref()

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const tableData = ref([])

const unitId = ref<any>('')

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

// 删除弹框
const showDelDialog = (row) => {
  // deleteId.value = row.id
  delRef.value.showDialog(row.id)
}

const handleDel = (id) => {
  sendDel(id)
}

const sendDel = async (id) => {
  let res: any = await $API.post({
    url: 'edu-app-server/api/workbench/certificate/delete',
    data: {
      id
    }
  })
  if (res.code == 'success') {
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
    getList()
  }
}

const handleBlacklist = (row: any) => {
  detailRef.value.showDialog(row.id)
}

const ui: Record<string, any> = useUserInfo()
const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/certificate/queryListWithProj',
    params: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      type: 0,
      staffId: '',
      relId: unitId.value,
      sysCode: 'web',
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

// 当前预览的图片列表
const currentPreviewImages = ref<string[]>([])
const showPreview = ref(false)

// 显示图片预览
const showImages = (imgList: any[]) => {
  if (!imgList || imgList.length === 0) return

  currentPreviewImages.value = imgList.map((item: any) => {
    return config.downloadFileUrl + item.filePath
  })

  showPreview.value = true
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
    }
    console.log('参数变化了2', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)

// 创建计划
defineOptions({ name: 'personnelCertificateIndex' })
</script>

<style scoped lang="scss">
.w_text_box {
  padding: 24px;
  font-size: 14px;

  //background-color: white;
  //background: chocolate;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .w_head_table_div {
    //padding-top: 20px;
    //background-color:red;
    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    .w_head_table_input {
      width: 100%;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  .w_red_bg {
    height: 8px;
    width: 8px;
    background-color: #f62a2a;
    border-radius: 50%;
    margin-right: 6px;
  }

  .w_green_bg {
    height: 8px;
    width: 8px;
    background-color: #16ac3e;
    border-radius: 50%;
    margin-right: 6px;
  }

  :deep(.el-table .el-table__cell) {
    z-index: revert-layer;
  }
}

.upload-status {
  &.uploaded {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
      text-decoration: underline;
    }
  }

  &.not-uploaded {
    color: #999999;
    cursor: default;
  }
}
</style>
