<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" @closed="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div class="title-info">导入人员</div>
        </div>
      </template>
      <div class="w_dialog_box">
        <div class="w_down_box">
          <div class="w_down_title">
            <div class="w_down_title_yuan">!</div>
            <div>每次上传前请下载最新模板</div>
          </div>
          <div class="download">
            <el-icon size="30" class="pl-[12px]">
              <svg xmlns="http://www.w3.org/2000/svg" t="1725271998052" class="icon" viewBox="0 0 1024 1024"
                version="1.1" p-id="20549" width="200" height="200">
                <path
                  d="M682.666667 42.666667H298.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666v213.333334l426.666667 213.333333 170.666666 64 170.666667-64V298.666667l-341.333333-256z"
                  fill="#21A366" p-id="20550" />
                <path d="M256 298.666667h426.666667v213.333333H256z" fill="#107C41" p-id="20551" />
                <path
                  d="M1024 85.333333v213.333334h-341.333333V42.666667h298.666666c21.333333 0 42.666667 21.333333 42.666667 42.666666z"
                  fill="#33C481" p-id="20552" />
                <path
                  d="M682.666667 512H256v426.666667c0 25.6 17.066667 42.666667 42.666667 42.666666h682.666666c25.6 0 42.666667-17.066667 42.666667-42.666666v-213.333334l-341.333333-213.333333z"
                  fill="#185C37" p-id="20553" />
                <path
                  d="M588.8 256H256v597.333333h324.266667c29.866667 0 59.733333-29.866667 59.733333-59.733333V307.2c0-29.866667-21.333333-51.2-51.2-51.2z"
                  opacity=".5" p-id="20554" />
                <path
                  d="M546.133333 810.666667H51.2C21.333333 810.666667 0 789.333333 0 759.466667V264.533333C0 234.666667 21.333333 213.333333 51.2 213.333333h499.2c25.6 0 46.933333 21.333333 46.933333 51.2v499.2c0 25.6-21.333333 46.933333-51.2 46.933334z"
                  fill="#107C41" p-id="20555" />
                <path
                  d="M145.066667 682.666667L256 512 153.6 341.333333h81.066667l55.466666 106.666667c8.533333 12.8 8.533333 21.333333 12.8 25.6l12.8-25.6L375.466667 341.333333h76.8l-102.4 170.666667 106.666666 170.666667h-85.333333l-64-119.466667c0-4.266667-4.266667-8.533333-8.533333-17.066667 0 4.266667-4.266667 8.533333-8.533334 17.066667L226.133333 682.666667H145.066667z"
                  fill="#FFFFFF" p-id="20556" />
                <path d="M682.666667 512h341.333333v213.333333h-341.333333z" fill="#107C41" p-id="20557" />
              </svg>
            </el-icon>
            <div class="w_down" @click="getDown">点击下载模版</div>
          </div>
        </div>

        <div class="mt-[10px]">
          <el-form ref="formRef" :model="formData" :rules="rules" label-position="left" label-width="auto">
            <div>
              <el-form-item label="导入文件：" prop="file">
                <el-upload ref="uploadRef" class="upload-demo" :auto-upload="false" :on-change="handleFileChange"
                  :on-remove="handleFileRemove" :file-list="fileList" :limit="1" accept=".xls,.xlsx"
                  :show-file-list="true">
                  <el-button type="primary">上传模版</el-button>
                </el-upload>
              </el-form-item>
            </div>
          </el-form>

          <!-- 导入结果区域 -->
          <div class="flex">
            <div class="w_res_l">导入结果:</div>
            <div class="w_res_r">
              <el-scrollbar style="max-height: 400px">
                <div v-if="checkLoading" class="text-[#2753bf]">正在检查文件格式，请稍候...</div>
                <div v-else-if="resultMessages.length">
                  <div v-for="(item, index) in resultMessages" :key="index" :class="item.class">
                    {{ item.text }}
                  </div>
                </div>
                <div v-else-if="resultMessage" :class="resultMessageClass">
                  {{ resultMessage }}
                </div>
                <div v-else class="text-[#717376]">点击导入后输出</div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()" :loading="submitLoading"
            :disabled="!canSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadFile, UploadFiles } from 'element-plus'
import $API from '~/common/api'
import { useRoute } from 'vue-router'

// 组件 props 和 emits
const emit = defineEmits(['action'])
const route = useRoute()
// 响应式数据
const drawer = ref(false)
const submitLoading = ref(false)
const checkLoading = ref(false)
const formRef = ref()
const uploadRef = ref()
const fileList = ref<UploadFiles>([])
const currentFile = ref<File | null>(null)
const resultMessages = ref<{ text: string; class: string }[]>([])
const resultMessage = ref('')
const resultMessageClass = ref('')
const errorList = ref<string[]>([])
const checkPassed = ref(false)

// 表单数据
const formData = ref({
  file: null as File | null
})

// 表单验证规则
const rules = {
  file: [{ required: true, message: '请选择要上传的文件', trigger: 'change' }]
}

// 计算属性：是否可以提交
const canSubmit = computed(() => {
  return currentFile.value && checkPassed.value
})

// 重置状态
const resetStatus = () => {
  resultMessage.value = ''
  resultMessages.value = []
  resultMessageClass.value = ''
  errorList.value = []
  checkPassed.value = false
}

// 关闭抽屉
const handleClose = () => {
  drawer.value = false
  resetStatus()
  fileList.value = []
  currentFile.value = null
  formData.value.file = null
  submitLoading.value = false
  checkLoading.value = false
}

// 显示抽屉
const showDialog = () => {
  drawer.value = true
  resetStatus()
}

// 下载模板
const getDown = () => {
  const link = document.createElement('a')
  link.href = 'https://agjp.tanzervas.com/aqsc/v1/file1/inter/批量导入人员模板.xlsx'
  link.type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 文件上传前检查
const validateFileBeforeUpload = async (file: File) => {
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.name.endsWith('.xlsx') ||
    file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    fileList.value = []
    currentFile.value = null
    formData.value.file = null
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    fileList.value = []
    currentFile.value = null
    formData.value.file = null
    return false
  }

  return true
}

function formatDuplicateMessages(data: Record<string, string[]>): { text: string; class: string }[] {
  const messages: { text: string; class: string }[] = []

  for (const key in data) {
    const values = data[key]
    if (!Array.isArray(values) || values.length === 0) continue

    let cls = ''

    // 根据字段类型设置样式
    if (key === 'phone' || key === 'idNum' || key === 'nullMsg' || key === 'repeatPhone' || key === 'repeatIdNum') {
      cls = 'text-[#f56c6c]'
    }

    // 将数组中的每个消息都作为单独的一条展示
    values.forEach((message) => {
      if (message && message.trim()) {
        messages.push({ text: message, class: cls })
      }
    })
  }

  return messages
}

// 调用预检查接口
const checkFileFormat = async (file: File) => {
  checkLoading.value = true
  resetStatus()
  try {
    const formData = new FormData()
    formData.append('fileName', file)
    const response: any = await $API.formDataFile(
      {
        url: 'edu-app-server/api/workbench/staff/batchImportAddBefor'
      },
      formData
    )
    console.log('response', response)
    if (response.code === 'success') {
      const data = response.data || {}
      const msgs = formatDuplicateMessages(data)
      console.log('msgs', msgs)
      if (msgs.length) {
        resultMessages.value = msgs
        checkPassed.value = false
        fileList.value = []
        currentFile.value = null
      } else {
        resultMessage.value = response.msg
        resultMessageClass.value = 'text-[#2753bf]'
        resultMessages.value = []
        checkPassed.value = true
      }
    } else {
      fileList.value = []
      currentFile.value = null
      resultMessage.value = ''
      resultMessageClass.value = 'text-[#f56c6c]'
      resultMessages.value = []
      checkPassed.value = false
    }
  } catch (error) {
    console.error('预检查失败:', error)
  } finally {
    checkLoading.value = false
  }
}

// 文件选择变化
const handleFileChange = async (file: UploadFile) => {
  if (!file.raw) return

  const isValid = await validateFileBeforeUpload(file.raw)
  if (!isValid) {
    handleFileRemove()
    return
  }

  currentFile.value = file.raw
  formData.value.file = file.raw
  await checkFileFormat(file.raw)
}

// 移除文件
const handleFileRemove = () => {
  fileList.value = []
  currentFile.value = null
  formData.value.file = null
  resetStatus()
}

// 提交导入
const onSubmit = async () => {
  if (!currentFile.value) {
    ElMessage.error('请先选择要上传的文件')
    return
  }
  submitLoading.value = true
  try {
    const formData = new FormData()
    formData.append('fileName', currentFile.value)
    const response: any = await $API.formDataFile(
      {
        url: 'edu-app-server/api/workbench/staff/batchAdd',
        params: {
          unitId: route.query.id
        }
      },
      formData
    )

    if (response.code === 'success') {
      ElMessage.success('人员导入成功')
      emit('action')
      handleClose()
    }
  } catch (error) {
    console.error('导入失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 暴露方法给父组件
defineExpose({ showDialog })
</script>

<style scoped lang="scss">
.w_add_plan_header {
  font-weight: 700;
  color: #333;
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: start;
  align-items: center;

  div:first-of-type {
    width: 18px;
    height: 12px;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    background-size: 100% 100%;
  }

  .title-info {
    font-size: 16px;
  }
}

.w_dialog_box {
  height: 100% !important;
  width: 100%;
  padding: 20px;
  position: relative;
}

.w_down_box {
  height: 100px;
  width: 100%;
  background-color: #f1f1f1;
  padding-left: 20px;
  padding-top: 8px;

  .w_down_title {
    color: red;
    display: flex;
    align-items: center;
  }

  .w_down_title_yuan {
    height: 14px;
    width: 14px;
    border-radius: 50%;
    background-color: red;
    text-align: center;
    color: white;
    font-size: 12px;
    line-height: 16px;
    margin-right: 4px;
  }

  .download {
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #2753bf;
    cursor: pointer;
  }
}

.w_down {
  height: 32px;
  line-height: 32px;
  color: #527cff;
  cursor: pointer;
}

.w_res_l {
  color: #717376;
}

.w_res_r {
  padding: 0 5px 5px;
  margin-left: 10px;
  width: 390px;
  min-height: 100px;
  max-height: 400px;
  border: 1px solid #c0c4cc;
  background-color: #f1f1f1;
}

:deep(.el-drawer.rtl) {
  width: 648px !important;
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
  border-bottom: 1px solid rgba(235, 238, 245, 1);
  padding-top: 0 !important;
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.upload-demo) {
  .el-upload-list {
    margin-top: 10px;
  }
}
</style>
