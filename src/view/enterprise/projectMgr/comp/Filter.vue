<template>
  <div class="header-filter">
    <div class="projectMgr-filter">
      <el-form inline :label-width="99" :model="formData">
        <el-form-item label="项目名称" prop="projectName" class="w-[260px]">
          <el-input v-model="formData.projectName" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus" class="w-[260px]">
          <el-select v-model="formData.projectStatus" placeholder="全部" clearable>
            <el-option
              v-for="item in statusOpt"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型" prop="xmlx" class="w-[260px]">
          <el-select v-model="formData.xmlx" placeholder="全部" clearable>
            <el-option v-for="item in xmlxOpt" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="施工现场类型" class="w-[260px]">
          <el-select v-model="formData.sgxclx" placeholder="全部" clearable>
            <el-option v-for="item in sgxcOpt" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="施工现场" prop="projectName" class="w-[260px]">
          <el-input v-model="formData.sgxcName" placeholder="请输入施工现场名称" clearable />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ACTION } from '@/view/projectMgr/constant'
import $API from '~/common/api'

defineOptions({ name: 'projectMgrFilter' })

const sgxcOpt = ref<
  Array<{
    dictLabel: string
    dictValue: string
    [prop: string]: any
  }>
>([])
const formData = ref({
  projectName: '',
  projectStatus: '',
  xmlx: '',
  sgxclx: null,
  sgxcName: ''
})
const statusOpt = ref<
  Array<{
    dictLabel: string
    dictValue: string
    [prop: string]: any
  }>
>([])
const xmlxOpt = ref<
  Array<{
    dictLabel: string
    dictValue: string
    [prop: string]: any
  }>
>([])
const getDict = (type: string) => {
  $API
    .get({
      url: 'atomic-upms-service//common/v1/queryDictList',
      params: {
        type
      }
    })
    .then((res: any) => {
      if (+res.code === 200) {
        if (type === 'projectStatus') {
          statusOpt.value = res.data
        }
        if (type === 'xmsg_xmlx') {
          xmlxOpt.value = res.data
        }
        if (type === 'sgxc') {
          sgxcOpt.value = res.data
        }
      }
    })
}
getDict('projectStatus')
getDict('xmsg_xmlx')
getDict('sgxc')

watch(
  () => formData.value,
  () => {
    actionHandle(ACTION.SEARCH, formData.value)
  },
  {
    deep: true
  }
)

const emits = defineEmits(['action'])
const actionHandle = (action, data: any = null) => {
  emits('action', {
    action,
    data
  })
}
</script>

<style scoped lang="scss">
.header-filter {
  @apply flex flex-row flex-nowrap justify-between;
  .header-action {
    @apply flex flex-row flex-nowrap gap-[8px];
  }
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
}

:deep(.el-form) {
  width: 104%;
  margin-left: -35px;
}
</style>
