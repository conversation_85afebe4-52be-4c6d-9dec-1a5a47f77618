<template>
  <ul class="projectMgr-bar">
    <li v-for="item in list" :key="item.name" :class="['bar-item', `bar-item_${item.type}`]">
      <span class="name">{{ item.name }}</span>
      <span class="value">{{ item.value }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'

defineOptions({ name: 'projectMgrDashBar' })

const route = useRoute()
const ui = useUserInfo()

const list = ref([
  {
    type: 1,
    name: '待开始项目',
    value: 0
  },
  {
    type: 2,
    name: '进行中项目',
    value: 0
  }
  // {
  //   type: 3,
  //   name: '已退场项目',
  //   value: 0
  // }
])

async function getData() {
  const url = '/atomic-upms-service/relate/project/statisticsProjectCount'
  const res: any = await $API.get({
    url,
    params: {
      orgCode: route.query.orgCode || ui.value.serverUnitId,
      relateOrgCode: route.query.id || ui.value.relateOrgCode
    }
  })
  if (res.code === 200) {
    list.value[0].value = res.data.toStartProjectNum || 0
    list.value[1].value = res.data.processingProjectNum || 0
    list.value[2].value = res.data.endProjectNum || 0
  }
}

getData()

defineExpose({
  getData
})
</script>

<style scoped lang="scss">
.projectMgr-bar {
  @apply flex flex-row flex-nowrap justify-center w-full mb-[20px];

  .bar-item {
    @apply relative flex-1 flex flex-col text-[14px] text-[#333] py-[15px] pl-[86px] pr-[20px] mr-[40px];
    line-height: 20px;
    max-width: 218px;
    background: linear-gradient(180deg, #ffffff 0%, #e1e7fa 99%);
    box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
    border-radius: 8px;
    border: 1px solid #e2e8f3;
    &:last-of-type {
      @apply mr-0;
    }

    &::before {
      @apply absolute top-[6px] left-[10px];
      content: '';
      content: '';
      width: 66px;
      height: 66px;
    }

    .value {
      font-size: 20px;
      margin-top: 10px;
    }
  }
  .bar-item_1 {
    &::before {
      background: url('@/assets/projectMgr/icon_1.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #fd8410;
    }
  }
  .bar-item_2 {
    &::before {
      background: url('@/assets/projectMgr/icon_2.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #527cff;
    }
  }
  .bar-item_3 {
    &::before {
      background: url('@/assets/projectMgr/icon_3.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .value {
      color: #737373;
    }
  }
  // .bar-item_4 {
  //   &::before {
  //     background: url('@/assets/projectMgr/icon_4.png') no-repeat 0 0;
  //     background-size: 100% 100%;
  //   }
  //   .value {
  //     color: #fd8410;
  //   }
  // }
}
</style>
