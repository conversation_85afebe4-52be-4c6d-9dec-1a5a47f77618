<template>
  <div class="projectMgr-aside">
    <el-drawer v-model="showDrawer" v-if="showDrawer" @closed="drawerClosed" size="85%">
      <template #header>
        <div class="aside-header">
          {{ drawerTitle }}
        </div>
      </template>

      <!--项目进场管理-->
      <ApproachMgr
        ref="appRoachMgrRef"
        :obj="obj"
        v-if="actionDrawer === ACTION.PEOPLEMGR || actionDrawer === ACTION.ENTRYTRAININGTRACK"
      />
      <!-- 项目过程管理 -->
      <ApprocessMgr v-if="actionDrawer === ACTION.RISK || actionDrawer === ACTION.DANGERWORK" :id="detailId" />
      <!--培训课程设定-->
      <TrainingCourseSetting
        v-if="actionDrawer === ACTION.ENTRYTRAININGSET"
        :project-data="projectData"
        @refresh-list="refreshList"
      />
      <!-- 项目信息管理 -->
      <InforMgr v-if="actionDrawer === ACTION.PROJECTINFO" @cancel="modifyCancel" @confirm="modifyUpdate" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ACTION, ACTION_LABEL } from '@/view/enterprise/projectMgr/constant'
import ApproachMgr from './approachMgr/index.vue'
import TrainingCourseSetting from './trainingCourseSetting/index.vue'
import ApprocessMgr from './approcessMgr/index.vue'
import InforMgr from './infoMgr/index.vue'
const appRoachMgrRef = ref(null)
defineOptions({ name: 'ProjectMgrAside' })

const emits = defineEmits(['action'])

const showDrawer = ref(false)
const drawerTitle = ref('')

let detailId = ''
const projectData = ref<any>()

const actionDrawer = ref('')

const modifyCancel = () => {
  showDrawer.value = false
}
const modifyUpdate = () => {
  emits('action', {
    action: ACTION.UPDATESUCCESS
  })
  showDrawer.value = false
}

const drawerClosed = () => {}

const refreshList = () => {
  emits('action', {
    action: ACTION.SEARCH
  })
}
const obj = ref({})
const asideShowHandle = (res: any) => {
  obj.value = res.data
  const { action, data } = res
  if ([ACTION.PEOPLEMGR, ACTION.ENTRYTRAININGTRACK].includes(action)) {
    drawerTitle.value = '项目进场管理'
  } else if ([ACTION.RISK, ACTION.DANGERWORK].includes(action)) {
    drawerTitle.value = '项目过程管理'
  } else {
    drawerTitle.value = ACTION_LABEL[action]
  }

  actionDrawer.value = action
  detailId = data?.projectId || ''
  projectData.value = data

  showDrawer.value = true
}

defineExpose({
  asideShowHandle
})
</script>

<style scoped lang="scss">
.projectMgr-aside {
  :deep(.el-overlay) {
    background-color: rgba(0, 0, 0, 0);
  }

  :deep(.el-drawer) {
    border-radius: 10px 0 0 10px;
    //width: 45% !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgb(235, 238, 245);
    padding-top: 0 !important;
  }

  .aside-header {
    @apply relative w-full h-[54px] pl-[28px] flex justify-start items-center;

    &::before {
      @apply absolute top-[50%] left-0 w-[18px] h-[12px];
      content: '';
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
</style>
