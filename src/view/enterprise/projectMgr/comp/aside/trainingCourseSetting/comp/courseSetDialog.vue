<template>
  <el-dialog
    class="dialog-no-header"
    v-model="dialogShow"
    width="1400px"
    @close="cancel"
    align-center
    title="设定培训课程"
  >
    <div class="w-full h-full flex">
      <el-card
        v-if="ui.unitOrgType !== '1'"
        class="org-tree"
        :class="!collapsed ? '' : 'p-20px'"
        :style="{ width: collapsed ? '323px' : '0', marginRight: collapsed ? '20px' : '0' }"
      >
        <train-tree :collapsed="collapsed" @serach="searchData" type="train" />
      </el-card>
      <el-card class="right relative w-full flex-1">
        <div class="flex justify-end pr-[24px]">
          <el-input class="!w-[240px]" v-model="keywords" placeholder="请输入关键词搜索" clearable @input="pageList" />
        </div>
        <div class="trainCourse">
          <div class="train-content" v-loading="loading">
            <el-scrollbar>
              <div v-for="(item, index) in trainCourseList" :key="index" class="train-content-box mr-[24px]">
                <div class="top relative" :style="{ background: `url(${getBackground(index)}) no-repeat` }">
                  <div class="mb-[15px] title" v-if="item.curriculumName.length < 30">{{ item.curriculumName }}</div>
                  <div class="mb-[15px] title" v-else>
                    <el-tooltip effect="dark" :content="item.curriculumName" placement="top-start">
                      {{ item.curriculumName.slice(0, 19) + '...' }}
                    </el-tooltip>
                  </div>
                  <div class="train-type" v-if="item.projectTypeName.length < 20">{{ item.projectTypeName }}</div>
                  <div class="train-type" v-else>
                    <el-tooltip effect="dark" :content="item.projectTypeName" placement="top-start">
                      {{ item.projectTypeName.slice(0, 10) + '...' }}
                    </el-tooltip>
                  </div>
                </div>
                <div class="bottom">
                  <div class="flex justify-between items-center mt-[14px]">
                    <div class="createTime">创建时间: {{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
                  </div>
                  <div class="flex items-center justify-end mb-[16px]">
                    <el-radio :value="item.id" v-model="selected" @change="handleSelect(item)"></el-radio>
                  </div>
                </div>
              </div>
              <no-data v-if="!trainCourseList.length"></no-data>
            </el-scrollbar>
          </div>
          <div class="btn-page" v-if="total">
            <el-pagination
              layout=" total, sizes, prev, pager, next, jumper"
              :page-size="pageSize"
              :page-sizes="[8, 12, 24, 36]"
              :total="total"
              v-model:current-page="curPage"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="expand" v-if="ui.unitOrgType !== '1'" @click="collapsedTree"></div>
      </el-card>
    </div>
    <template #footer>
      <div class="dialog-footer pr-[20px]">
        <el-button @click="cancel" plain>取消</el-button>
        <el-button type="primary" @click="submit" :disabled="!selected">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import dayjs from 'dayjs'
import TrainTree from '~/components/tree/index.vue'
import { useUserInfo } from '~/store'
import listBg1 from '~/assets/image/top-bj1.png'
import listBg2 from '~/assets/image/top-bj2.png'
import listBg3 from '~/assets/image/top-bj3.png'
import listBg4 from '~/assets/image/top-bj4.png'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'

const props = defineProps({
  show: Boolean,
  projectId: String
})
const $emit = defineEmits(['update:show', 'success'])

const dialogShow = computed({
  get: () => props.show,
  set: (value: any) => $emit('update:show', value)
})

const collapsed = ref(true)
const ui: any = useUserInfo()
const trainCourseList = ref<any>([])
const boxColor = ref([listBg1, listBg2, listBg3, listBg4])
const loading = ref(false)
const total = ref()
const curPage = ref(1)
const pageSize = ref(12)
const pageNo = ref(1)
const orgCode = ref<string>(ui.value.unitId)
const orgName = ref<string>(ui.value.unitName)
const keywords = ref('')
const selected = ref('')
const selectedData = ref<any>()

const collapsedTree = () => {
  collapsed.value = !collapsed.value
}
// 接受树结构选中数据
const searchData = (obj) => {
  orgCode.value = obj.orgCode
  orgName.value = obj.orgName
  getTrainCourseList()
}
const getBackground = (index) => {
  return boxColor.value[index % boxColor.value.length]
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  pageNo.value = 1
  getTrainCourseList()
}
const handleCurrentChange = (val: number) => {
  pageNo.value = val
  getTrainCourseList()
}

const pageList = () => {
  pageNo.value = 1
  getTrainCourseList()
}

const getTrainCourseList = () => {
  selected.value = ''
  loading.value = true
  $API
    .post({
      url: 'edu-inter-server/train/curriculum/manage/getCurriculumPage',
      data: {
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        unitId: orgCode.value ? orgCode.value : ui.value.unitId,
        curriculumName: keywords.value,
        trainType: '2'
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        loading.value = false
        trainCourseList.value = res.data.rows
        pageNo.value = res.data.pageNo
        pageSize.value = res.data.pageSize
        total.value = res.data.total
      }
    })
}

function handleSelect(data: any) {
  selectedData.value = data
}

function cancel() {
  dialogShow.value = false
  selected.value = ''
  selectedData.value = null
}

async function submit() {
  const params = {
    projectId: props.projectId, // 项目ID
    trainCurriculumManageId: selectedData.value.id // 培训课程ID
  }
  const res: any = await $API.post({
    url: 'edu-inter-server/relateProject/entryManager/configRelateProjectTrainCurriculumSetting',
    data: params
  })
  if (res.code === 'success') {
    ElMessage.success('培训课程设定成功')
    $emit('success', selectedData.value.id)
    cancel()
  }
}

const firstLoad = ref(true) // 首次加载通过tree触发查询
watch(
  () => props.show,
  (val) => {
    if (val) {
      // 首次加载并且存在机构tree组件，等待tree触发擦好像
      if (firstLoad.value && ui.value.unitOrgType !== '1') {
        firstLoad.value = false
        return
      }
      getTrainCourseList()
    }
  }
)

defineOptions({ name: 'CourseSetDialog' }) // 课程设定弹窗
</script>

<style scoped lang="scss">
.org-tree {
  background: #eef7ff;
  overflow: hidden;
  transition: all 0.5s ease-in-out;

  :deep(.el-tree) {
    .el-tree-node__content {
      //   height: 40px;
      margin-top: 4px;
      border-radius: 6px;
      /* height: auto; */
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.right {
  .trainCourse {
    @apply w-full flex flex-col;
    height: calc(100vh - 275px);
    margin-bottom: 10px;
    padding-top: 20px;
    overflow: hidden;
    overflow-x: auto;

    .train-content {
      @apply flex-1 h-0;
      background: #fff;
      border-bottom: 1px solid #ebeef5;
      padding-left: 24px;

      .train-content-box {
        margin-bottom: 20px;
        border-radius: 8px;

        .top {
          height: 126px;
          padding-left: 17px;
          padding-top: 20px;
          background-size: 100% 100% !important;

          .title {
            font-weight: 600;
            font-size: 18px;
            color: #ffffff;
          }

          .train-type {
            display: inline-block;
            white-space: nowrap;
            padding: 0 10px;
            height: 24px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 3px 3px 3px 3px;
            color: #fff;
            text-align: center;
            font-size: 14px;
            line-height: 24px;
          }

          .el-icon {
            position: absolute;
            right: 20px;
            bottom: 35px;
          }
        }

        .bottom {
          padding: 0 17px;
          display: flex;
          flex-direction: column;
          border: 1px solid #ccc;
          border-top: none;
          border-radius: 0 0 8px 8px;
          .createTime {
            font-weight: 400;
            font-size: 14px;
            color: #2f2f2f;
          }
        }

        :deep(.el-radio__inner) {
          border-color: #909399;
        }
      }

      :deep(.el-scrollbar__view) {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        row-gap: 10px;
        height: auto;
      }
    }

    .btn-page {
      width: 100%;
      margin-top: 13px;
      padding-right: 20px;
      text-align: right;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.expand {
  background: url('@/assets/expand.png') no-repeat;
  width: 34px;
  height: 36px;
  background-size: 100% 100%;
  position: absolute;
  top: 45%;
  left: -17px;
  cursor: pointer;
  z-index: 2;
}

.span_cursor {
  color: #527cff;
  cursor: pointer;

  &:last-child {
    // color: #d43c31 !important;
    margin-left: 20px;
  }
}

.text-info {
  margin-top: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #484a4d;
}

:deep(.el-card) {
  overflow: inherit !important;
  position: relative;
  z-index: 1;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

:deep(.el-button--primary.is-plain) {
  margin-left: 0;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.el-button--primary.is-plain) {
  margin-left: 20px;
}
</style>
