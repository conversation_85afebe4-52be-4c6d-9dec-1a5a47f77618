<template>
  <div class="projectMgr-info">
    <!--详情-->
    <ProjectDetail />
    <!--项目信息管理-->
    <div class="info-main">
      <h3 class="title">项目管理</h3>
      <el-form ref="formRef" class="custom-content" :model="formData" :rules="formRules">
        <el-form-item class="!mb-[24px]" label="施工方案" prop="constructScheme">
          <el-input v-model="formData.constructScheme" v-show="false"></el-input>
          <file-uploader
            ref="cPlanRef"
            v-model="cPlanLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'cPlanFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
        <el-form-item class="!mb-[24px]" label="应急预案" prop="emergencyPlan">
          <el-input v-model="formData.emergencyPlan" v-show="false"></el-input>
          <file-uploader
            ref="ePlanRef"
            v-model="ePlanLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'ePlanFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
        <el-form-item label="安全协议" prop="securityProtocol">
          <el-input v-model="formData.securityProtocol" v-show="false"></el-input>
          <file-uploader
            ref="protocolRef"
            v-model="protocolLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'protocolFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="info-action" v-if="!isOut">
      <el-button :disabled="isSubmitting" @click="cancelHandle">取消</el-button>
      <el-button type="primary" :loading="isSubmitting" @click="submitHandle">{{
        isSubmitting ? '提交中...' : '确定'
      }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed, Ref } from 'vue'
import ProjectDetail from '../ProjectDetail.vue'
import FileUploader from '~/components/upload/FileUploader.vue'
import { formData, formRules, resetFormData } from './infoForm'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import { PROVIDE_KEY, IActionData } from '@/view/enterprise/projectMgr/constant'

defineOptions({ name: 'ProjectMgrInfo' }) // 项目信息管理

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const isOut = computed(() => +actionData.value.projectStatus === 4)

const formRef = ref()

const accept = '.pdf,.doc,.docx'
const cPlanLi = ref<any[]>([])
const ePlanLi = ref<any[]>([])
const protocolLi = ref<any[]>([])
const isUploading = ref(false)
const cPlanRef = ref<any>(null)
const ePlanRef = ref<any>(null)
const protocolRef = ref<any>(null)

// 设置初始值
formData.value.projectId = actionData.value.projectId
formData.value.relateOrgCode = actionData.value.relateOrgCode

// 获取详情
async function getDetail() {
  const processFileList = (fileList) => {
    if (!fileList?.length) return []
    return fileList.map((file) => ({
      ...file,
      name: file.fileName,
      url: file.fileUrl || file.filePath,
      uid: file.id || file.fileId || Date.now() + Math.random(),
      status: 'success',
      fileId: file.id || file.fileId,
      isOriginal: true,
      originalData: { ...file }
    }))
  }

  const url = '/atomic-upms-service/relate/project/detail'
  const params = { projectId: actionData.value.projectId, relateOrgCode: actionData.value.relateOrgCode }
  const res: any = await $API.get({ url, params })
  if (res.code === 200) {
    const { constructSchemeList, emergencyPlanList, securityProtocolList } = res.data

    const cFiles = processFileList(constructSchemeList)
    const eFiles = processFileList(emergencyPlanList)
    const pFiles = processFileList(securityProtocolList)

    cPlanLi.value = [...cFiles]
    ePlanLi.value = [...eFiles]
    protocolLi.value = [...pFiles]

    formData.value.constructScheme = cFiles.map((file) => file.fileId || file.id).join(',')
    formData.value.emergencyPlan = eFiles.map((file) => file.fileId || file.id).join(',')
    formData.value.securityProtocol = pFiles.map((file) => file.fileId || file.id).join(',')
  }
}

getDetail()

// 修改文件变化处理函数
const handleFileChange = (files: any[], key: string) => {
  const fileArray = files || []

  const originalFiles = fileArray.filter((file) => file.isOriginal)
  const newFiles = fileArray.filter((file) => !file.isOriginal)

  const originalFileIds = originalFiles.map((file) => file.fileId || file.id).filter(Boolean)
  const newFileIds = newFiles.map((file) => file.fileId || file.response?.data?.id).filter(Boolean)

  const allFileIds = [...originalFileIds, ...newFileIds].join(',')
  switch (key) {
    case 'cPlanFile':
      formData.value.constructScheme = allFileIds
      break
    case 'ePlanFile':
      formData.value.emergencyPlan = allFileIds
      break
    case 'protocolFile':
      formData.value.securityProtocol = allFileIds
      break
  }
}

// 更新上传状态
const updateUploadingStatus = (status: boolean) => {
  isUploading.value = status
}

const checkUploadStatus = async () => {
  const refs = [cPlanRef, ePlanRef, protocolRef]
  for (const ref of refs) {
    if (ref.value && typeof ref.value.checkUploadStatus === 'function') {
      const status = await ref.value.checkUploadStatus()
      if (!status) {
        return false
      }
    }
  }
  return true
}
// 清除文件
const clearFile = () => {
  resetFormData()
  cPlanLi.value = []
  ePlanLi.value = []
  protocolLi.value = []
}

const emits = defineEmits(['cancel', 'confirm'])

const isSubmitting = ref(false)
const submitHandle = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      const uploadComplete = await checkUploadStatus()
      if (!uploadComplete) {
        ElMessage.warning('部分文件未上传完成，请等待上传完成后再保存')
        return
      }

      const params = JSON.parse(JSON.stringify(formData.value))
      isSubmitting.value = true
      $API
        .post({
          url: 'atomic-upms-service/relate/project/manageProjectFile',
          data: params
        })
        .then((res: any) => {
          if (+res.code === 200) {
            ElMessage.success('保存成功')
            clearFile()
            emits('confirm')
          }
        })
        .finally(() => {
          isSubmitting.value = false
        })
    }
  })
}
const cancelHandle = () => {
  clearFile()
  emits('cancel')
}
</script>

<style scoped lang="scss">
.projectMgr-info {
  @apply w-full h-full;
  display: grid;
  grid-template-rows: auto 1fr;
  color: #000000;

  .info-main {
    @apply mt-[20px] mb-[30px] text-[#333];
  }

  .title {
    @apply mb-[20px] text-[#527CFF];
  }

  .info-action {
    @apply absolute bottom-0 h-[62px] bg-[#fff] flex justify-end;
    width: calc(100% - 40px);
    border-top: 1px solid rgb(235, 238, 245);
    padding: 10px 20px 20px;
    z-index: 9;
  }
}
</style>
