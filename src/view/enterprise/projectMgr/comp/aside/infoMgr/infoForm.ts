import { ref } from 'vue'

export const initFormData = () => ({
  projectId: '',
  relateOrgCode: '',
  constructScheme: '',
  emergencyPlan: '',
  securityProtocol: ''
})
export const formData = ref(initFormData())

export const resetFormData = () => {
  formData.value = initFormData()
}

export const formRules = {
  constructScheme: [{ required: true, message: '请选择施工方案', trigger: ['change'] }],
  emergencyPlan: [{ required: true, message: '请选择应急预案', trigger: ['change'] }],
  securityProtocol: [{ required: true, message: '请选择安全协议', trigger: ['change'] }]
}
