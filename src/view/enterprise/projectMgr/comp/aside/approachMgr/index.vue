<template>
  <div class="approach-wrap">
    <!--详情-->
    <ProjectDetail class="!w-full" />
    <!--列表-->
    <el-tabs v-model="activeTab" class="min-h-0">
      <el-tab-pane class="h-full" label="人员管理" name="personMgr" v-if="!flag">
        <PersonMgrComp :obj="obj" :activeTab="activeTab" />
      </el-tab-pane>
      <el-tab-pane class="h-full" label="人员管理" name="personMgr" v-else>
        <PersonMgrComp2 :obj="obj" ref="PersonMgrCompRef" />
      </el-tab-pane>
      <el-tab-pane class="h-full" label="入场培训跟踪" name="trainingTrack">
        <TrainingTrackComp :activeTab="activeTab" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, Ref, watch, computed } from 'vue'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import ProjectDetail from '~/view/projectMgr/right/comp/aside/ProjectDetail.vue'
import PersonMgrComp from '~/view/projectMgr/right/comp/aside/approachMgr/PersonMgr.vue'
import TrainingTrackComp from '~/view/projectMgr/right/comp/aside/approachMgr/TrainingTrack.vue'
import PersonMgrComp2 from '~/view/enterprise/projectMgr/comp/aside/approachMgr/PersonMgr2.vue'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const activeTab = ref('personMgr')
const flag = computed(() => currentAction.value.action === ACTION.ENTRYTRAININGTRACK)

defineProps({ obj: Object })

watch(
  () => currentAction.value.action,
  (val) => {
    if (val === ACTION.PEOPLEMGR) {
      activeTab.value = 'personMgr'
    } else {
      activeTab.value = 'trainingTrack'
    }
  },
  { immediate: true }
)

defineOptions({ name: 'approachMgr' }) // 项目进场管理
</script>

<style scoped lang="scss">
.approach-wrap {
  @apply w-full h-full;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 30px;
}
</style>
