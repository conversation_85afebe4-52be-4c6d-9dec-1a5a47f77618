<template>
  <div class="w_learn_box">
    <div class="w_learn_table_input">
      <el-form :model="form" inline>
        <el-form-item label="完成状态:">
          <el-select v-model="form.studyState" placeholder="全部" :clearable="true">
            <el-option
              :label="item.paramValue"
              :value="item.paramCode"
              v-for="(item, index) in completeData"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="培训结果:">
          <el-select v-model="form.trainResults" placeholder="全部" :clearable="true">
            <el-option
              :label="item.paramValue"
              :value="item.paramCode"
              v-for="(item, index) in trainData"
              :key="index"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="">
          <el-input v-model="form.userName" placeholder="请输入姓名搜索" clearable />
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" stripe style="height: 100%">
      <el-table-column type="index" width="60" label="序号" align="center" />
      <el-table-column prop="userName" label="姓名" align="center" show-overflow-tooltip width="180" />
      <el-table-column prop="unitName " label="单位" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.unitName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="className" label="课程名称" align="center" show-overflow-tooltip width="180" />
      <el-table-column prop="deptName" label="部门" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.deptName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="postName" label="岗位" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.postName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="finishStatusName" label="完成状态" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          <div class="flex justify-center items-center">
            <div>{{ scope.row.finishStatusName || '--' }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="studyDuration" label="学习时间(分钟)" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.studyDuration || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="学习进度" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{
            scope.row.progress === 0 ? scope.row.progress + '%' : scope.row.progress ? scope.row.progress + '%' : '--'
          }}
        </template>
      </el-table-column>
      <el-table-column prop="lastStudyTime" label="最近学习时间" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.lastStudyTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="signPhoto" label="参训人签名" align="center" width="180">
        <template #default="scope">
          <el-image
            v-if="scope.row.signPhoto"
            class="w_img_base"
            :src="scope.row.signPhoto"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            preview-teleported
            :preview-src-list="[scope.row.signPhoto]"
          />
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column prop="signTime" label="参训人签名时间" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.signTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="examStatusName" label="是否参考" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.examStatusName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="submitTime" label="试卷提交时间" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.submitTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="examUseTime" label="考试用时（分钟）" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.examUseTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="score" label="考试分值" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.score || '0' }}
        </template>
      </el-table-column>
      <el-table-column prop="examResultName" label="考试结果" align="center" show-overflow-tooltip width="180">
        <template #default="scope">
          {{ scope.row.examResultName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="trainResultName" label="培训结果" align="center" fixed="right" width="100">
        <template #default="scope">
          {{ scope.row.trainResultName || '--' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="170">
        <template #default="scope">
          <span class="w_span_cursor mr-5" @click="handleDetail(scope.row)">培训详情</span>
          <span class="w_span_cursor" v-if="+scope.row.isExam === 1" @click="handleExam(scope.row)">考试试卷</span>
          <span class="w_span_cursor3" v-else>考试试卷</span>
        </template>
      </el-table-column>
    </el-table>

    <!--    </div>-->
    <div class="w_learn_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!--  -->
    <detailCom ref="detailRef"></detailCom>
    <!-- 考试试卷 -->
    <ExamPaperComp ref="examRef" />
  </div>
</template>
<script setup lang="ts">
import $API from '@/common/api.ts'
import { computed, inject, Ref, ref, watch } from 'vue'
import { useUserInfo } from '~/store'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import detailCom from './comp/detail.vue'
import ExamPaperComp from './comp/ExamPapesDrawer.vue'

const props = defineProps({
  activeTab: {
    type: String,
    default: ''
  }
})

watch(
  () => props.activeTab,
  (val) => {
    if (val === 'trainingTrack') {
      form.value = {
        studyState: '', // 学习状态
        trainResults: '', // 培训结果
        userName: '' //用户名
      }
      pageSize.value = 20
      pageNo.value = 1
      total.value = 0
      // getList()
    }
  }
)

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const detailRef = ref()

const examRef = ref()

// 字典数据
const completeData = [
  {
    paramValue: '已完成',
    paramCode: 1
  },
  {
    paramValue: '未完成',
    paramCode: 0
  }
] // 完成状态
const trainData = [
  {
    paramValue: '已通过',
    paramCode: 1
  },
  {
    paramValue: '未通过',
    paramCode: 0
  }
] // 培训结果

// 表单
const form = ref({
  studyState: '', // 学习状态
  trainResults: '', // 培训结果
  userName: '' //用户名
})

// 列表page
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

// 列表数据
const tableData = ref([])

// id
// const unitId = ref<any>(actionData.value.unitId || null )
const projId = ref<any>(actionData.value.projectId || '')
const orgCode = ref<any>(actionData.value.orgCode || '')

const ui: Record<string, any> = useUserInfo()

// 列表接口
const getList = async () => {
  const requestData: any = {
    trainType: '2',
    // unitId: unitId.value,
    projId: projId.value, // 项目id - 来源于项目详情
    orgCode: orgCode.value || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    ...form.value
  }

  // 如果是已退场状态，添加userStatus=3参数
  if (actionData.value.projectStatus === '4') {
    requestData.userStatus = 3
  }

  let res = await $API.post({
    url: 'edu-inter-server/safeTrain/pageEnterListWithProj',
    data: requestData
  })

  tableData.value = res.data.rows || []
  total.value = res.data.total || 0
}

// 翻页事件
const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
// 翻页事件
const handleCurrentChange = () => {
  getList()
}

const handleDetail = (row) => {
  detailRef.value.showDialog(row.id)
}

const handleExam = (row) => {
  const params = {
    ...row
  }
  examRef.value.showDialog(params)
}

watch(
  () => form.value,
  () => {
    pageNo.value = 1
    getList()
  },
  {
    deep: true,
    immediate: true
  }
)

defineOptions({ name: 'trainingTrackComp' })
</script>

<style scoped lang="scss">
.w_learn_box {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: 20px;

  .w_learn_table_input {
    @apply flex justify-end;
    width: 100%;
    :deep(.el-form-item__content) {
      width: 207px;
    }
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }

  .span_cursor {
    color: #527cff;
    cursor: pointer;
  }

  .w_learn_page_num {
    @apply flex justify-end;
  }

  .w_span_cursor {
    color: #527cff;
    cursor: pointer;
  }

  .w_span_cursor3 {
    color: #cccccc !important;
  }

  .w_img_base {
    height: 44px;
    width: 64px;
  }
}
</style>
