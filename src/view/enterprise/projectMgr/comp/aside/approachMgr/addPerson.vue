<template>
  <el-dialog v-model="dialogVisible" width="60%" align-center @close="handleClose">
    <template #header>
      <HeadTitle title="新增人员" />
    </template>
    <div class="add-person-form mb-4">
      <el-form :inline="true" :model="formData">
        <el-form-item label="员工姓名：">
          <el-input v-model="formData.userName" placeholder="请输入员工姓名" clearable @input="debouncedSearch" />
        </el-form-item>
        <el-form-item label="员工手机号：">
          <el-input v-model="formData.phoneNo" placeholder="请输入员工手机号" clearable @input="debouncedSearch" />
        </el-form-item>
      </el-form>
    </div>

    <el-table
      ref="tableRef"
      :data="personList"
      row-key="userId"
      stripe
      style="width: 100%"
      height="400px"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column label="员工姓名" prop="userName" align="center" show-overflow-tooltip />
      <el-table-column label="手机号" prop="phoneNo" align="center" show-overflow-tooltip />
      <el-table-column label="证件名称" prop="cardName" align="center" show-overflow-tooltip />
      <el-table-column label="证件编号" prop="cardNo" align="center" show-overflow-tooltip />
      <el-table-column label="作业次数" prop="workNum" align="center" show-overflow-tooltip />
      <el-table-column label="作业违规次数" prop="workViolateNum" align="center" show-overflow-tooltip />
    </el-table>

    <div class="pagination-container flex justify-end mt-4 mb-4">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <div class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirmAdd">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { computed, inject, nextTick, reactive, ref, Ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { IActionData, PROVIDE_KEY } from '~/view/enterprise/projectMgr/constant'

const route = useRoute()
const props = defineProps({
  addPersonVisible: {
    type: Boolean,
    default: false
  },
  selectedPersons: {
    type: Array,
    default: () => []
  }
})
const dialogVisible = ref(true)
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>
const emits = defineEmits(['update', 'update:addPersonVisible'])

const loading = ref(false)
const formData = reactive({
  userName: '',
  phoneNo: ''
})
const selectedRows = ref([])
const personList = ref([])
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 5
})

const selectable = (row) => {
  return row.isCheck === 0
}

const getPersonList = async () => {
  loading.value = true
  const url = 'construction-core-service/relate/project/queryRelateUserList'
  const res: any = await $API.post({
    url,
    data: {
      ...formData,
      relateOrgCode: route.query.id,
      pageNo: pagination.value.currentPage,
      pageSize: pagination.value.pageSize
    }
  })
  if (res.code === 200) {
    personList.value = res.data.rows
    pagination.value.total = res.data.total
    loading.value = false

    nextTick(() => {
      setSelectedRows()
    })
  }
}

const tableRef = ref(null) // 添加表格ref
// 设置已选中的行
const setSelectedRows = () => {
  if (tableRef.value) {
    personList.value.forEach((row: any) => {
      if (props.selectedPersons.some((selected: any) => selected.userId === row.userId)) {
        tableRef.value?.toggleRowSelection(row, true)
      }
    })
  }
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const confirmAdd = () => {
  emits('update', selectedRows.value)
  emits('update:addPersonVisible', false)
}

const handleClose = () => {
  formData.userName = ''
  formData.phoneNo = ''
  selectedRows.value = []
  emits('update:addPersonVisible', false)
}

const searchUsers = async () => {
  if (!formData.userName && !formData.phoneNo) {
    return
  }
  getPersonList()
}

const debouncedSearch = useDebounceFn(() => {
  searchUsers()
}, 500)

watch(
  () => [formData.userName, formData.phoneNo],
  () => {
    if (!formData.userName && !formData.phoneNo) {
      getPersonList()
    }
  }
)

defineExpose({
  dialogVisible
})

watch(
  () => props.addPersonVisible,
  async (val) => {
    dialogVisible.value = val
    if (val) {
      await getPersonList()
      // 打开弹窗时，设置已选中的行
      nextTick(() => {
        if (tableRef.value && props.selectedPersons) {
          // 先清空所有选择
          tableRef.value?.clearSelection()
          // 设置已选中的行
          personList.value.forEach((row: any) => {
            if (props.selectedPersons.some((selected: any) => selected.userId === row.userId)) {
              tableRef.value?.toggleRowSelection(row, true)
            }
          })
        }
      })
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.add-person-dialog {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
