<template>
  <div class="person-list">
    <!--filter-->
    <div class="flex justify-end">
      <el-input
        class="!w-[260px]"
        v-model="query.userName"
        placeholder="请输入姓名搜索"
        :suffix-icon="Search"
        clearable
      />
    </div>

    <el-table :data="tableData" height="100%" stripe>
      <el-table-column type="index" width="60" label="序号" align="center" />
      <el-table-column prop="relateOrgName" label="承包商企业" align="center" width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.relateOrgName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="承包商人员姓名" align="center" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.userName || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="post" label="岗位" align="center" min-width="140" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.post || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="insureFiles" label="投保信息（工伤）" align="center" min-width="140" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList
            v-if="scope.row.insureFiles && scope.row.insureFiles.length > 0"
            :img-list="scope.row.insureFiles"
          />
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column prop="idFiles" label="身份证照片" align="center" width="160" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList v-if="scope.row.idFile && scope.row.idFiles.length > 0" :img-list="scope.row.idFiles || []" />
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="documentFiles" label="证件照（免冠照）" align="center" width="160" show-overflow-tooltip>
        <template #default="scope">
          <imgViewList
            v-if="scope.row.documentFiles && scope.row.documentFiles.length > 0"
            :img-list="scope.row.documentFiles || []"
          />
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="idNumber" label="身份证号" align="center" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.idNumber || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="idStartPeriod" label="身份证有效期" align="center" min-width="210" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.idStartPeriod && scope.row.idEndPeriod">
            {{ scope.row.idStartPeriod }} ~ {{ scope.row.idEndPeriod }}
          </span>
          <span v-else> -- </span>
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄（岁）" align="center" width="100" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.age || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="specialOperation"
        label="否是特种作业人员"
        align="center"
        min-width="140"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.specialOperation == 1">是</span>
          <span v-else-if="scope.row.specialOperation == 0">否</span>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="140" fixed="right">
        <template #default="scope">
          <el-button type="danger" plain size="small" @click="handleRemove(scope.row)">移除人员</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="flex justify-end">
      <el-pagination
        v-model:currentPage="query.pageNo"
        v-model:page-size="query.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import imgViewList from '@/components/imgViewList/index.vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, inject, Ref, ref, watch } from 'vue'
import $API from '~/common/api'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'

const props = defineProps({
  activeTab: {
    type: String,
    default: ''
  }
})

watch(
  () => props.activeTab,
  (val) => {
    if (val === 'personMgr') {
      query.value = {
        pageNo: 1,
        pageSize: 20,
        userName: ''
      }
      total.value = 0
      getDataList()
    }
  }
)

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const total = ref<number>(0)
const query = ref({
  pageNo: 1,
  pageSize: 20,
  userName: ''
})

const handleSizeChange = () => {
  query.value.pageNo = 1
  getDataList()
}
const handleCurrentChange = () => {
  getDataList()
}

const tableData = ref([])

watch(
  () => query.value.userName,
  () => {
    query.value.pageNo = 1
    getDataList()
  },
  { immediate: true }
)

async function getDataList() {
  const params: any = {
    ...query.value,
    projectId: actionData.value.projectId
  }

  // 如果是已退场状态，添加userStatus=3参数
  if (actionData.value.projectStatus === '4') {
    params.userStatus = 3
  }

  const url = 'construction-core-service/relate/project/queryProjectUserList'
  const res: any = await $API.post({ url, data: params })
  if (res.code === 200) {
    total.value = res.data.total
    tableData.value = res.data.rows
  }
}

function handleRemove(row: any) {
  ElMessageBox({
    title: '提示',
    message: '确认移除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        const url = 'construction-core-service/relate/project/delProjectUser'
        const params = { userId: row.userId, projectId: actionData.value.projectId }
        let res: any = await $API.post({ url, data: params })
        if (+res.code === 200) {
          ElMessage({ message: '移除成功', type: 'success' })
          await getDataList()
        }
      }
      done()
    }
  })
}

defineOptions({ name: 'personMgrComp' })
</script>

<style scoped lang="scss">
.person-list {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: 20px;
}
</style>
