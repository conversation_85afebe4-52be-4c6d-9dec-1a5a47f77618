<template>
  <div class="dailyCheck">
    <div class="header">
      <div class="flex justify-start items-center">
        <div class="trouble-number flex justify-start items-center">
          <div style="z-index: 2">
            <div>已完成检查任务</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic group-separator="," :value="newObj && newObj.completionTaskNum"></el-statistic>
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px">
          <div style="z-index: 2">
            <div>检查出隐患</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.total"></el-statistic>
            </n-statistic>
          </div>
        </div>

        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>已整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.disposedNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px]">
          <div style="z-index: 2">
            <div>整改中</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.disposingNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
        <div class="trouble-number flex justify-start items-center ml-[16px] !mr-[0]">
          <div style="z-index: 2">
            <div>待整改</div>
            <n-statistic tabular-nums style="--n-value-text-color: white">
              <el-statistic :value="newObj && newObj.unDisposedNum"></el-statistic>
            </n-statistic>
          </div>
        </div>
      </div>
      <div class="projectMgr-table" style="margin-top: 20px">
        <el-table
          ref="multipleTableRef"
          :data="tableData"
          stripe
          :height="tableHeight"
          style="width: 100%"
          row-key="projectId"
        >
          <el-table-column type="index" width="60" label="序号" align="center" />
          <el-table-column prop="taskName" label="任务名称" align="center" show-overflow-tooltip />
          <el-table-column
            prop="pointCheckTypeName"
            label="任务类型"
            align="center"
            width="110"
            show-overflow-tooltip
          />
          <el-table-column prop="taskState" label="任务状态" align="center" width="110" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-if="scope.row.taskState == 1" color="#E6A23C" style="color: white">待开始</el-tag>
              <el-tag v-if="scope.row.taskState == 2" color="#527CFF" style="color: white">进行中</el-tag>
              <el-tag v-if="scope.row.taskState == 3" color="#67C23A" style="color: white">已完成</el-tag>
              <el-tag v-if="scope.row.taskState == 4" color="#F56C6C" style="color: white">已关闭</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="timeState" label="任务时效" align="center" width="110" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.timeState == 1">正常</span>
              <span v-if="scope.row.timeState == 2">逾期</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="checkedPointNum"
            label="排查点位进度"
            align="center"
            min-width="110"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div>
                <span class="text-color-[#409eff]">{{ scope.row.checkedPointNum || '0' }}</span> /{{
                  scope.row.checkPointNum || '0'
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="hazardEventNum" label="上报隐患数" align="center" width="110" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.hazardEventNum || '0' }} </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="checkUserName"
            label="检查负责人"
            align="center"
            min-width="110"
            show-overflow-tooltip
          />
          <el-table-column
            prop="planStartTime"
            label="任务起止时间"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="!scope.row.planStartTime">未开始</span>
              <span v-else-if="scope.row.planEndTime">
                {{ scope.row.planStartTime }} - {{ scope.row.planEndTime }}
              </span>
              <span v-else>{{ scope.row.planStartTime }} - 未完成</span>
            </template>
          </el-table-column>
          <el-table-column prop="createByName" label="创建人" align="center" width="110" show-overflow-tooltip />
          <el-table-column prop="createTime" label="创建时间" align="center" width="110" show-overflow-tooltip />
          <el-table-column label="操作" fixed="right" align="center" width="110" show-overflow-tooltip>
            <template #default="scope">
              <span @click="handleDetail(scope.row)" class="text-color-[#409eff] cursor-pointer">详情</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-box">
          <el-pagination
            v-model:currentPage="pagi.pageNo"
            v-model:page-size="pagi.pageSize"
            :total="pagi.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="getTableData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, Ref, ref } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import { IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import config from '~/config'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

console.log(actionData.value, '<<<<<<<<<<<<<<<<<<<')

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const { tableHeight } = mixTableHeight({ subtractHeight: 530 })
const tableData = ref([])
const getTableData = async () => {
  let res: any = await $API.post({
    url: 'construction-core-service/org/project/getHazardTaskPageByProjectId',
    params: {
      projectId: actionData.value.projectId,
      unitId: actionData.value.orgCode,
      pageNo: pagi.value.pageNo,
      pageSize: pagi.value.pageSize
    }
  })
  console.log('re3333333s', res)
  if (res.code == '200') {
    tableData.value = res.data.rows
    pagi.value.total = res.data?.total || 0
  }
}
const newObj = ref()
const getDetail = async () => {
  let res = await $API.post({
    url: `/edu-inter-server/coopManageForHazard/queryCompletionTaskNum?projectId=${actionData.value.projectId}`
  })
  console.log('res', res)
  newObj.value = res.data
}
const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

//跳转隐患治理任务跟踪详情
const handleDetail = async (row) => {
  const userInfoJson = window.sessionStorage.getItem('@@web_userInfo_partner')
  const userInfo = userInfoJson ? JSON.parse(userInfoJson) : {}
  $API
    .post({
      url: 'ehs-clnt-platform-service/login/checkSysPower',
      data: {
        sysCode: 'hazard_inves', //隐患治理
        userId: userInfo.id
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        // const base = window.location.origin
        // const base = 'https://test-bw.gsafetycloud.com'
        const base = config.downloadFileUrl
        const path = `/ehs-hazard-web/#/task-management/hazard-task-detail/${row.taskId}/task`
        const params = {
          tab: '1',
          planTypeId: row.planTypeId,
          checkRange: '3',
          flag: '2',
          startTime: row.planStartTime.substring(0, 10),
          endTime: row.planEndTime.substring(0, 10),
          taskAssignInspection: '0',
          taskAssignDeptId: '',
          text: '',
          planCreateSource: '0',
          token: String(res.data.token),
          sysCode: 'hazard_inves'
        }
        window.open(base + path + '?' + new URLSearchParams(params).toString(), '_blank')
      }
    })
}
onMounted(() => {
  getDetail()
  getTableData()
})

defineOptions({ name: 'HazardMgrComp' })
</script>

<style scoped lang="scss">
::v-deep {
  .el-statistic__content {
    color: white !important;
  }
}

.projectMgr-table {
  display: grid;

  .pagination-box {
    @apply flex justify-end mt-[20px];
  }
}

.trouble-number:nth-child(1) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png1.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(2) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(3) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png3.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(4) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png4.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.trouble-number:nth-child(5) {
  color: white;
  margin-right: 16px;
  width: 20%;
  height: 120px;
  padding: 20px 12px 17px 12px;
  background-image: url('./img/png5.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
