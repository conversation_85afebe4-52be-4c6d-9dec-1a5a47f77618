<template>
  <div class="approach-wrap">
    <!--详情-->
    <ProjectDetail />

    <!--列表-->
    <el-tabs v-model="activeTab" class="safety-tabs">
      <el-tab-pane label="隐患排查治理" name="HazardMgr">
        <!-- 隐患排查组件 -->
        <div class="w_server_title_box flex items-center pl-[80px] mt-[-10px] mb-[5px]">
          <span class="text-[#527CFF] font-bold italic pt-[4px]">温馨提示:</span>
          <span class="ml-[13px] must pt-[4px]">
            本列表是基于施工安全管理，针对每个项目系统自动创建的风险排查任务、每天9点系统自动短信提醒项目现场负责人，进行每日风险排查。
          </span>
        </div>
        <HazardMgr />
      </el-tab-pane>
      <el-tab-pane label="安全作业" name="safe">
        <!-- 危险作业组件 -->
        <WorkRecord />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, Ref } from 'vue'
import ProjectDetail from '~/view/projectMgr/right/comp/aside/ProjectDetail.vue'
import HazardMgr from './HazardMgr.vue'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/projectMgr/constant'
import WorkRecord from './workRecord.vue'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>

const activeTab = computed(() => (currentAction.value.action === ACTION.RISK ? 'HazardMgr' : 'safe'))

// const activeTab = computed(() => 'HazardMgr')

defineOptions({ name: 'processMgr' }) // 项目进场管理
</script>

<style scoped lang="scss">
.w_server_title_box {
  font-family: MyCustomFont, sans-serif;
  width: 100%;
  background: url('@/assets/img/must-bj.png') no-repeat;
  background-size: 100% 100%;
  line-height: 54px;
  font-size: 20px;

  .must {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #061032;
  }
}
.approach-wrap {
  @apply w-full h-full;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 8px;
}
</style>
