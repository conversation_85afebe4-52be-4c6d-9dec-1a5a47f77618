<template>
  <div class="work-record">
    <!-- 原统计数据 -->
    <div class="statistics">
      <div class="stat-box" v-for="(stat, index) in statistics" :key="stat.label" :class="`stat-box-${index + 1}`">
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-value">{{ stat.value }}</div>
      </div>
    </div>
    <!-- 原列表内容 -->
    <div class="record-list">
      <el-scrollbar>
        <no-data v-if="records.length == 0"></no-data>
        <div class="record-item" v-else v-for="item in records" :key="item.operationNo">
          <div class="info-row">
            <span>作业时间：{{ item.timeRange }}</span>
          </div>
          <div class="info-row">
            <span>作业单位：{{ item.unit }}</span>
          </div>
          <div class="info-row">
            <span>作业位置：{{ item.location }}</span>
          </div>
          <div class="info-row">
            <span>作业编号：{{ item.number }}</span>
          </div>
          <div class="info-row">
            <span>作业等级：{{ item.level }}</span>
          </div>
          <div class="action-row">
            <div class="info-item">
              <div class="flex gap-2">
                <div v-if="item.operationStatus">
                  <div class="w_tag_b w-tag" v-if="item.operationStatus === '2'">申请中</div>
                  <div class="w_tag_l w-tag" v-if="item.operationStatus === '3'">作业中</div>
                  <div class="w_tag_s w-tag" v-if="item.operationStatus === '4'">已完成</div>
                </div>
                <div class="w_tag_l w-tag" v-if="item.operationTypeName">
                  {{ item.operationTypeName }}
                </div>
                <div class="w_tag_o w-tag">合规</div>
              </div>
            </div>
            <el-button type="primary" @click="viewWorkTicket(item.operationBaseId)"> 查看作业票 </el-button>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, ref, onMounted, Ref } from 'vue'
import $API from '@/common/api'
import type { StatData, WorkRecord } from './type'
import { PROVIDE_KEY, IActionData } from '~/view/enterprise/projectMgr/constant'
import config from '~/config'

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const loading = ref(false)

const statsData = ref<StatData>({
  operTotal: 0,
  operTodayTotal: 0,
  operApplyTotal: 0,
  operWorkTotal: 0,
  operOverTotal: 0,
  operOk: 0,
  hazardNum: 0
})

const recordsList = ref<WorkRecord[]>([])

const fetchStatsData = async () => {
  try {
    const res = await $API.post({
      url: 'edu-app-server/api/workbench/safelyResume/queryUserOperBaseStatic',
      data: { projectId: currentAction.value.data.projectId, personnelIds: '' }
    })
    if (res.code == '200') {
      statsData.value = res.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const fetchRecordsList = async () => {
  try {
    loading.value = true
    const res = await $API.post({
      url: 'edu-app-server/api/workbench/safelyResume/queryUserOperBaseList',
      data: {
        projectId: currentAction.value.data.projectId,
        personnelIds: '',
        pageNo: 1,
        pageSize: -1
      }
    })
    if (res.code == '200') {
      recordsList.value = res.data || []
    }
  } catch (error) {
    console.error('获取记录列表失败:', error)
  } finally {
    loading.value = false
  }
}

const statistics = computed(() => {
  return [
    {
      label: '作业总次数（次）',
      value: statsData.value.operTotal
    },
    {
      label: '合规次数（次）',
      value: statsData.value.operOk
    },
    {
      label: '隐患次数（次）',
      value: statsData.value.hazardNum
    }
  ]
})

const records = computed(() => {
  return recordsList.value.map((item: WorkRecord) => {
    return {
      ...item,
      timeRange: item.planStartTime && item.planEndTime ? `${item.planStartTime} ~ ${item.planEndTime}` : '--',
      unit: item.unitName || '--',
      location: item.operationLocationPart || '--',
      number: item.operationNo || '--',
      level: item.operationLevelName || '--'
    }
  })
})

// 查看作业票
const viewWorkTicket = async (id: string) => {
  let url = config.workPreview + id
  window.open(url, '_target')
}

onMounted(() => {
  fetchStatsData()
  fetchRecordsList()
})
</script>

<style lang="scss" scoped>
.work-record {
  .statistics {
    display: flex;
    gap: 20px;
    margin-bottom: 35px;

    .stat-box {
      flex: 1;
      height: 130px;
      padding: 25px 40px;

      &.stat-box-1 {
        background: url('@/assets/image/work-1.png') no-repeat;
        background-size: 100% 100%;
      }

      &.stat-box-2 {
        background: url('@/assets/image/work-2.png') no-repeat;
        background-size: 100% 100%;
      }

      &.stat-box-3 {
        background: url('@/assets/image/work-3.png') no-repeat;
        background-size: 100% 100%;
      }

      .stat-label {
        color: #ffffff;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .stat-value {
        color: #ffffff;
        font-size: 28px;
        font-weight: bold;
        margin-top: 20px;
      }
    }
  }

  .record-list {
    height: calc(100vh - 510px);
    position: relative;

    .record-item {
      border: 1px solid #ccc;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 4px;
      background: #f5f8ff;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }

      .info-row {
        margin-bottom: 18px;
        color: #606266;

        span {
          color: #606266;
        }
      }

      .action-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;

        .status-tags {
          .tag {
            background: #ecf5ff;
            color: #409eff;
            padding: 2px 8px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 14px;
          }
        }
      }
    }
    :deep(.el-scrollbar__wrap) {
      height: 100%;
    }
  }

  .empty-data {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    font-size: 14px;
  }

  // 添加骨架屏样式
  .skeleton-stat {
    flex: 1;
    height: 130px !important;
    background: #e4e7ed !important;
  }

  .skeleton-item {
    .el-skeleton {
      padding: 10px 0;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
.w-tag {
  font-family: AlibabaPuHuiTiR;
  font-size: 14px;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
  font-style: normal;
  //width: 75px;
  padding: 0 10px;
  height: 30px;
  border-radius: 4px;
}
.w_tag_l {
  background: #527cff;
}

.w_tag_b {
  background: rgba(80, 178, 125, 1);
}

.w_tag_s {
  background: rgba(145, 148, 151, 1);
}

.w_tag_o {
  background: rgba(233, 157, 66, 1);
}
</style>
