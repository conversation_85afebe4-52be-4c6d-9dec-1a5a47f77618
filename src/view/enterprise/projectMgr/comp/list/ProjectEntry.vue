<template>
  <div class="project-entry">
    <header class="title">施工进场管理</header>

    <div class="btn" @click="emits('action', ACTION.PROJECTINFO)">
      <span class="icon icon_project"></span>
      项目信息
    </div>
    <div class="btn" @click="emits('action', ACTION.PEOPLEMGR)">
      <span class="icon icon_people"></span>
      人员管理
    </div>
    <div class="btn" @click="emits('action', ACTION.ENTRYTRAININGTRACK)">
      <span class="icon icon_entry"></span>
      进场培训跟踪
    </div>
  </div>
</template>

<script setup lang="ts">
import { ACTION } from '../../constant'

const emits = defineEmits(['action'])

defineOptions({ name: 'ProjectMgrEntryAction' })
</script>

<style scoped lang="scss">
.project-entry {
  @apply px-[20px] pt-[6px] pb-[25px] h-full flex flex-col items-center;
  border-radius: 8px;
  border: 1px solid #dfe6f2;

  .title {
    @apply text-[18px];
    font-weight: 700;
    line-height: 36px;
  }

  .btn {
    @apply w-[129px] bg-[#e5ebff] flex flex-row items-center justify-center mt-[10px] cursor-pointer text-[#527CFF] text-[14px];
    line-height: 32px;
    border-radius: 4px;
    border: 1px solid #527cff;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;

      &.icon_project {
        background: url('@/assets/projectMgr/icon_project.png') no-repeat 0 0;
        background-size: 100% 100%;
      }

      &.icon_people {
        background: url('@/assets/projectMgr/icon_people.png') no-repeat 0 0;
        background-size: 100% 100%;
      }

      &.icon_entry {
        background: url('@/assets/projectMgr/icon_entry.png') no-repeat 0 0;
        background-size: 100% 100%;
      }
    }

    &:hover {
      box-shadow: 0 0 8px 4px #e5ebff;
    }
  }
}
</style>
