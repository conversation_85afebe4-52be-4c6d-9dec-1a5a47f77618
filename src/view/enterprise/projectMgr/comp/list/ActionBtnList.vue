<template>
  <div class="action-btns" @mouseenter="enterHandle" @mouseleave="leaveHandle">
    <el-button type="primary" plain>
      项目变更
      <el-icon class="el-icon--right">
        <component :is="arrowIcon" />
      </el-icon>
    </el-button>

    <ul class="btn-list" v-show="isHover">
      <li class="btn-item" v-for="item in list" :key="item.text">
        <el-button :key="item.text" link @click="actionHandle(item.action)">
          {{ item.text }}
        </el-button>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowUp, ArrowRight } from '@element-plus/icons-vue'
import { ACTION, ACTION_LABEL } from '../../constant'

defineOptions({ name: 'ActionBtns' })

const isHover = ref(false)
const arrowIcon = computed(() => (isHover.value ? ArrowUp : ArrowRight))

const list = ref([
  {
    text: ACTION_LABEL.EDIT,
    action: ACTION.EDIT
  },
  {
    text: ACTION_LABEL.DELETE,
    action: ACTION.DELETE
  }
])

const enterHandle = () => {
  isHover.value = true
}
const leaveHandle = () => {
  isHover.value = false
}

const emits = defineEmits(['action'])

const actionHandle = (action: ACTION) => {
  emits('action', action)
}
</script>

<style scoped lang="scss">
.action-btns {
  @apply relative flex;
  &::before {
    @apply absolute bottom-[-20px] left-0 w-full h-[11px];
    content: '';
    z-index: 8;
  }

  .btn-list {
    @apply absolute bottom-[-10px] left-[50%] w-full bg-[#fff];
    transform: translate(-50%, 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.0859);
    border-radius: 4px;
    border: 1px solid #ebebeb;
    z-index: 9;

    .btn-item {
      @apply py-[5px] w-full h-[32px] text-[14px] flex items-center justify-center;
      border-bottom: 1px solid #ececec;
      &:last-of-type {
        border-bottom: none;
      }
    }
  }
}
</style>
