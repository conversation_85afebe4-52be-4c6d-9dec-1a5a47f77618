<template>
  <div class="project-info">
    <div class="info-header flex-nowrap flex">
      <span class="label">项目名称：</span>
      <span
        style="max-width: calc(100% - 100px)"
        class="value whitespace-nowrap overflow-hidden text-ellipsis"
        :title="info.projectName"
      >{{ info.projectName || '--' }}</span
      >
    </div>
    <div class="info-body">
      <el-row>
        <el-col :span="10">
          <div class="info-item">
            <span class="label">项目所属单位：</span>
            <span class="value" :title="info.orgName">{{ info.orgName }}</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">项目类型：</span>
            <span class="value" :title="info.xmlxMc">{{ info.xmlxMc || '--' }}</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">施工现场类型：</span>
            <span class="value" :title="info.sgxclxMc">{{ info.sgxclxMc || '--' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <div class="info-item">
            <span class="label">项目安全管理员：</span>
            <span class="value !flex-none">{{ info.responsibleUserName }}</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">项目负责部门：</span>
            <span class="value" :title="info.xmssbmName">{{ info.xmssbmName || '--' }}</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">施工现场：</span>
            <span class="value" :title="info.sgxcName">{{ info.sgxcName || '--' }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <div class="info-item">
            <span class="label">项目起止日期：</span>
            <span class="value">{{ info.startDate }} ~ {{ info.endDate }}</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">项目成员数：</span>
            <span class="value">{{ info.projectUserNum || 0 }}人</span>
          </div>
        </el-col>
        <el-col :span="7">
          <div class="info-item">
            <span class="label">风险点：</span>
            <span class="value">{{ info.riskPointNum || 0 }}个</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ITableRow } from '@/view/projectMgr/right/type'

defineOptions({ name: 'ProjectMgrBaseInfo' })

const props = withDefaults(
  defineProps<{
    info: Partial<ITableRow>
  }>(),
  {
    info: () => ({})
  }
)
</script>

<style scoped lang="scss">
.project-info {
  .info-header {
    @apply text-[19px];
    line-height: 36px;
    font-weight: 700;
  }

  :deep(.info-body .el-row) {
    margin-bottom: 0;
  }

  .info-item {
    @apply flex flex-row flex-nowrap;
    white-space: nowrap;
    .value {
      @apply flex-1 truncate min-w-[130px];
    }
  }
}
</style>
