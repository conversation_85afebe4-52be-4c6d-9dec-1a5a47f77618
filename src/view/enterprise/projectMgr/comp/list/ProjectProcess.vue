<template>
  <div class="project-process">
    <header class="title">施工过程管理</header>

    <div class="btn" @click="emits('action', ACTION.RISK)">
      <span class="icon icon_warn"></span>
      隐患排查治理
    </div>

    <div class="btn" @click="emits('action', ACTION.DANGERWORK)">
      <span class="icon icon_danger"></span>
      安全作业
    </div>
  </div>
</template>

<script setup lang="ts">
import { ACTION } from '../../constant'
const emits = defineEmits(['action'])
defineOptions({ name: 'ProjectMgrProcessAction' })
</script>

<style scoped lang="scss">
.project-process {
  @apply px-[20px] pt-[6px] pb-[67px] h-full flex flex-col items-center;
  border-radius: 8px;
  border: 1px solid #dfe6f2;

  .title {
    @apply text-[18px];
    font-weight: 700;
    line-height: 36px;
  }

  .btn {
    @apply w-[129px] bg-[#e5ebff] flex flex-row items-center justify-center mt-[10px] cursor-pointer text-[#527CFF] text-[14px];
    line-height: 32px;
    border-radius: 4px;
    border: 1px solid #527cff;

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;

      &.icon_warn {
        background: url('@/assets/projectMgr/icon_warn.png') no-repeat 0 0;
        background-size: 100% 100%;
      }

      &.icon_danger {
        background: url('@/assets/projectMgr/icon_danger.png') no-repeat 0 0;
        background-size: 100% 100%;
      }
    }

    &:hover {
      box-shadow: 0 0 8px 4px #e5ebff;
    }
  }
}
</style>
