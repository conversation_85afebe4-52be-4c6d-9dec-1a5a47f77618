<template>
  <div class="projectMgr-list-wrapper">
    <el-scrollbar :height="tableHeight">
      <ul class="projectMgr-list" v-loading="loading" v-if="listData.length > 0">
        <li
          class="projectMgr-item"
          :class="`projectMgr-item_${item.projectStatus}`"
          v-for="item in listData"
          :key="item.projectId"
        >
          <div class="item-left-box">
            <BaseInfo :info="item" class="flex-1" />
            <!-- <div class="action-wrap">
              <el-button class="ml-[12px]" type="primary" @click="actionHandle(ACTION.ENTRYTRAININGSET, item)">{{
                ACTION_LABEL.ENTRYTRAININGSET
              }}</el-button>
            </div> -->
          </div>
          <EntryAction @action="(action: ACTION) => actionHandle(action, item)" />
          <ProcessAction @action="(action: ACTION) => actionHandle(action, item)" />
        </li>
      </ul>
      <Empty v-else />
    </el-scrollbar>

    <div class="pagination-box">
      <el-pagination
        v-model:currentPage="pagi.pageNo"
        v-model:page-size="pagi.pageSize"
        :total="pagi.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="getTableData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseInfo from './BaseInfo.vue'
import EntryAction from './ProjectEntry.vue'
import ProcessAction from './ProjectProcess.vue'
import mixTableHeight from '~/common/tableHeight.js'
import { ACTION } from '../../constant'
import $API from '~/common/api'
import { ITableRow } from '../../type'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'
import Empty from '@/components/public/noData.vue'

defineOptions({ name: 'projectMgrList' })

const route = useRoute()
const ui = useUserInfo()

let filterData: any = {} // 搜索条件

const pagi = ref({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const loading = ref(false)
const { tableHeight } = mixTableHeight({ subtractHeight: 152 })
const listData = ref<ITableRow[]>([])

const getTableData = () => {
  const params = {
    pageNo: pagi.value.pageNo,
    pageSize: pagi.value.pageSize,
    orgCodes: route.query.orgCode || '',
    relateOrgCode: route.query.id || ui.value.serverUnitId,
    ...filterData
  }
  $API
    .post({
      url: 'atomic-upms-service/relate/project/list',
      data: params
    })
    .then((res: any) => {
      if (+res.code === 200) {
        listData.value = res.data?.rows || []
        pagi.value.total = res.data?.total || 0
      }
    })
}

const resetTableData = (data: any = {}) => {
  filterData = Object.assign({}, data)
  handleSizeChange()
}

const handleSizeChange = () => {
  pagi.value.pageNo = 1
  getTableData()
}

// const preDeleteList = ref<ITableRow[]>([])
// const deleteHandle = async () => {
//   const ids = preDeleteList.value.map((item: any) => item.projectId)
//   const res: any = await $API.get({
//     url: 'atomic-upms-service/org/project/batchDeleteProject',
//     params: {
//       projectIds: ids.join(',')
//     }
//   })
//   if (+res.code === 200) {
//     emits('action', {
//       action: ACTION.UPDATESUCCESS
//     })
//   }
// }
// const deleteConfirm = () => {
//   if (preDeleteList.value.length < 1) return
//   ElMessageBox({
//     title: '提示',
//     message: '确认删除吗?',
//     showCancelButton: true,
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     beforeClose: async (action, instance, done) => {
//       if (action === 'confirm') {
//         deleteHandle()
//         done()
//       } else {
//         done()
//       }
//     }
//   })
// }

getTableData()

const emits = defineEmits(['action'])

type TActions = keyof typeof ACTION
const actionHandle = (actionType: TActions, data) => {
  emits('action', {
    action: actionType,
    data
  })
}

defineExpose({
  resetTableData,
  getTableData
})
</script>

<style scoped lang="scss">
.projectMgr-list-wrapper {
  overflow: hidden;
  display: grid;
  .pagination-box {
    @apply flex justify-end pt-[20px];
    border-top: 1px solid #ececec;
  }
}

.projectMgr-list {
  padding-bottom: 20px;
}

.projectMgr-item {
  @apply relative py-[15px] pl-[25px] pr-[84px] mb-[16px] text-[14px] text-[#222] flex flex-row flex-nowrap items-center gap-[20px];
  line-height: 36px;
  background: linear-gradient(204deg, #e3eaff 0%, #fafbff 62%);
  border-radius: 6px;
  border: 1px solid #fff;
  &::before {
    @apply absolute top-0 right-0 w-[80px] h-[70px];
    content: '';
  }
  &::after {
    @apply absolute bottom-[15px] right-0 w-[84px] h-[84px];
    content: '';
    background: url('@/assets/projectMgr/card-bg.png') no-repeat 0 0;
    background-size: 100% 100%;
  }

  &.projectMgr-item_0::before {
    background: url('@/assets/projectMgr/status_1.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  &.projectMgr-item_1::before {
    background: url('@/assets/projectMgr/status_2.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  &.projectMgr-item_4::before {
    background: url('@/assets/projectMgr/status_3.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
  &.projectMgr-item_5::before {
    background: url('@/assets/projectMgr/status_5.png') no-repeat 0 0;
    background-size: 100% 100%;
  }
}

.item-left-box {
  @apply flex-1 flex flex-col justify-between;
  min-width: 0;
}

.action-wrap {
  @apply flex flex-row items-center;
}
</style>
