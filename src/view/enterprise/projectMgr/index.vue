<template>
  <div class="projectMgr-wrapper">
    <!-- <DashBar ref="dashBarRef" /> -->
    <Filter @action="actionHandle" />
    <div class="w-full">
      <ListComp ref="listRef" @action="actionHandle" />
    </div>

    <AsideComp ref="asideRef" @action="actionHandle" />
  </div>
</template>

<script setup lang="ts">
import { provide, Ref, ref } from 'vue'
// import DashBar from './comp/dashBar.vue'
import Filter from './comp/Filter.vue'
import ListComp from './comp/list/index.vue'
import AsideComp from './comp/aside/index.vue'
import { ACTION, IActionData, PROVIDE_KEY } from './constant'

defineOptions({ name: 'projectMgr' })

// const dashBarRef = ref()
const listRef = ref()
const asideRef = ref()

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} })
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction)

const actionHandle = (payload: any) => {
  const { action, data } = payload

  if (action === ACTION.SEARCH) {
    listRef.value.resetTableData(data)
  }

  if (
    [
      ACTION.DETAIL,
      ACTION.PEOPLEMGR,
      ACTION.ENTRYTRAININGTRACK,
      ACTION.ENTRYTRAININGSET,
      ACTION.RISK,
      ACTION.DANGERWORK,
      ACTION.OUT,
      ACTION.PROJECTINFO
    ].includes(action)
  ) {
    currentAction.value = payload
    console.log(payload, '===')
    asideRef.value.asideShowHandle({
      action,
      data
    })
  }

  if (action === ACTION.UPDATESUCCESS) {
    // dashBarRef.value.getData()
    listRef.value.resetTableData()
  }
}
</script>

<style scoped lang="scss">
.projectMgr-wrapper {
  height: 100%;
  padding: 20px;
  background-color: #fff;
}
</style>
