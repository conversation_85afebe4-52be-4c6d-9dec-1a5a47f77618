<template>
  <div class="safe-score">
    <!-- 顶部积分展示 -->
    <div class="score-header">
      <div class="score-info">
        <div class="score-line">
          <div class="title-status-wrapper">
            <div class="score-title">安全积分</div>
            <span class="status" :class="workStatus === '1' ? 'normal' : 'stopped'" v-if="workStatus">
              {{ workName }}
            </span>
          </div>
          <div>
            <span class="text-[#0081FF] text-[40px]">
              <CountTo
                :start-val="0"
                :end-val="totalScore"
                :duration="2000"
                :decimals="Number.isInteger(totalScore) ? 0 : 1"
              />
            </span>
            分
          </div>
          <div class="eval-time mt-[19px]">评估时间: {{ evaluationTime || '--' }}</div>
        </div>
      </div>

      <!-- 右侧圆形积分展示 -->
      <div class="score-circle"></div>
    </div>

    <!-- 将历史记录和操作按钮放在同一行 -->
    <div class="flex justify-between items-center mb-3 operation-wrapper">
      <div class="head_tab ml-[20px]">历史记录</div>
      <div class="button-group mr-[10px]">
        <!-- 通过停工和正常来判断按钮显示  正常只显示扣分  停工后依次显示复核和确认 -->
        <el-button color="#527CFF" @click="handleAddScore" v-auth="['xzkfnr']" v-if="workType === '1'"
          >新增扣分内容</el-button
        >
        <el-button color="#527CFF" @click="handleResumeWorkAudit" v-auth="['fgsh']" v-if="workType === '3'"
          >复工审核</el-button
        >
        <el-button color="#527CFF" @click="handleResumeWorkConfirm" v-auth="['fgqr']" v-if="workType === '2'"
          >复工确认</el-button
        >
      </div>
    </div>

    <!-- 积分历史表格 -->
    <div class="w_server_table">
      <el-scrollbar max-height="100%">
        <el-table :data="scoreHistory" stripe height="100%">
          <template #empty>
            <no-data />
          </template>
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="createTime" label="操作时间" width="180" align="center" />
          <el-table-column prop="createName" label="操作人员" width="120" align="center" />
          <el-table-column prop="typeName" label="操作类型" width="140" align="center">
            <template #default="{ row }">
              <div class="operation-type-tag">{{ row.typeName || '扣分' }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="content" label="操作内容" align="center" />
          <el-table-column label="相关附件" align="center" prop="fileAttachmentList">
            <template #default="{ row }">
              <div class="attachment-list" v-if="row.fileAttachmentList">
                <div
                  v-for="(name, index) in row.fileAttachmentList"
                  :key="index"
                  class="attachment-item"
                  @click="handleFileClick(name.filePath)"
                  :title="name.fileName"
                >
                  {{ name.fileName }}
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="unitScore" label="操作后积分" width="100" align="center" />
        </el-table>
      </el-scrollbar>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="total">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 添加复工审核抽屉组件 -->
    <ResumeWorkDialog ref="resumeWorkDialogRef" @submit="handleAddScoreSubmit" />
    <!-- 添加复工确认抽屉组件 -->
    <ResumeWorkConfirm ref="resumeWorkConfirmRef" @submit="handleAddScoreSubmit" />
    <!-- 添加新的侧边栏组件 -->
    <AddScoreRecord ref="addScoreRecordRef" @submit="handleAddScoreSubmit" />
  </div>
</template>

<script setup lang="ts">
import { useUserInfo } from '@/store'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CountTo } from 'vue3-count-to'
import $API from '~/common/api'
import config from '~/config'
import AddScoreRecord from './comp/AddScoreRecord.vue'
import ResumeWorkConfirm from './comp/ResumeWorkConfirm.vue'
import ResumeWorkDialog from './comp/ResumeWorkDialog.vue'

const route = useRoute()
const router = useRouter()
const ui = useUserInfo()
const totalScore = ref(0)
const workStatus = ref('') // 1-正常 2-停工
const workName = ref('')
const workType = ref('') // 判断按钮 4-审核 3-确认
const evaluationTime = ref('')
// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 模拟历史数据
const scoreHistory = ref([])
const resumeWorkDialogRef = ref<any>(null)
const resumeWorkConfirmRef = ref<any>(null)
const addScoreRecordRef = ref<any>(null)

const handleResumeWorkAudit = () => {
  resumeWorkDialogRef.value.open()
}

// 修改复工确认按钮的点击事件处理
const handleResumeWorkConfirm = () => {
  resumeWorkConfirmRef.value.open()
}

// 处理文件点击
const handleFileClick = (fileName) => {
  if (!fileName) return
  const suffix = fileName.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + fileName
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    window.open(config.downloadFileUrl + fileName, '_blank')
  }
}

// 添加新增积分记录按钮的点击事件处理
const handleAddScore = () => {
  addScoreRecordRef.value.open()
}

// 添加获取数据的方法
const getScoreHistory = async () => {
  try {
    const res = await $API.post({
      url: 'edu-inter-server/safeScore/pageList',
      data: {
        unitId: route.query.id,
        pageNo: currentPage.value,
        pageSize: pageSize.value
      }
    })
    if (res.data && res.code == 'success') {
      scoreHistory.value = res.data.rows
      total.value = res.data.total || 0
    } else {
      scoreHistory.value = []
      total.value = 0
    }

    // 获取总分和工作状态
    await getUnitScoreInfo()
  } catch (error) {
    console.error('获取积分历史记录失败:', error)
  }
}

// 获取单位积分信息
const getUnitScoreInfo = async () => {
  try {
    const response = await $API.post({
      url: 'edu-inter-server/safeScore/getUnitSafetyScore',
      params: {
        unitId: route.query.id
      }
    })

    if (response.data) {
      totalScore.value = response.data.unitScore || 0
      workStatus.value = response.data.unitStatus
      workType.value = response.data.type
      workName.value = response.data.unitStatusName
      evaluationTime.value = response.data.lastOperateTime
    }
  } catch (error) {
    console.error('获取单位积分信息失败:', error)
  }
}

// 监听分页变化
const handlePageChange = (newPage) => {
  currentPage.value = newPage
  getScoreHistory()
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
  getScoreHistory()
}

const handleAddScoreSubmit = () => {
  getScoreHistory()
}

// const handleScoreSetting = () => {
//   ElMessage({
//     message: '正在研发中，待上线',
//     type: 'warning'
//   })
// }

// 初始化加载数据
onMounted(() => {
  getScoreHistory()
})
</script>

<style lang="scss" scoped>
.safe-score {
  width: 100%;
  height: 100%;
  padding: 0 23px;
  background-color: #eef7ff;

  .score-header {
    display: flex;
    justify-content: space-between;

    .score-info {
      width: 705px;
      height: 205px;
      background: url('@/assets/img/score-bg.png') no-repeat center center;
      background-size: 100% 100%;

      .score-line {
        margin-left: 303px;
        margin-top: 2.375rem;

        .title-status-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;
        }

        .score-title {
          font-family: PingFang SC-Semibold;
          font-size: 18px;
          font-weight: 600;
          color: #333333;
        }

        .status {
          width: 44px;
          height: 27px;
          border-radius: 15px 0px 15px 0px;
          font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          text-align: center;
          line-height: 27px;

          &.normal {
            background: #2ba471;
          }

          &.stopped {
            background: #d54941;
          }
        }

        .eval-time {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }

    .score-circle {
      color: #527cff;
      font-size: 14px;
      margin-top: 24px;
      cursor: pointer;
    }
  }

  .operation-wrapper {
    height: 44px;
    background: rgba(82, 124, 255, 0.15);
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 12px;

    .head_tab {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #527cff;

      &::before {
        content: '';
        width: 18px;
        height: 12px;
        margin-right: 10px;
        background: url('@/assets/image/drawer_bg.png') no-repeat;
        background-size: 100% 100%;
        display: inline-block;
      }
    }

    .button-group {
      .el-button {
        margin-left: 12px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  .w_server_table {
    width: 100%;
    height: calc(100vh - 390px);

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 0.8);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .attachment-list {
    .attachment-item {
      color: #409eff;
      cursor: pointer;
      line-height: 1.5;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 auto;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .operation-type-tag {
    width: 76px;
    height: 31px;
    background: rgba(82, 124, 255, 0.1);
    border-radius: 4px;
    border: 1px solid #527cff;
    color: #527cff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }
}
</style>
