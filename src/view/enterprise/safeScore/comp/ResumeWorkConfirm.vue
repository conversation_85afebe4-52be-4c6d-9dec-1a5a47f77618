<template>
  <popup-side v-model="visible" popupTitle="复工确认" width="620px" :closeOnClickModal="false">
    <div class="resume-confirm-content">
      <!-- 申请信息 -->
      <div class="section">
        <div class="section-title">申请信息</div>
        <el-form label-width="100px" label-position="left">
          <el-form-item label="申请单位">
            <span>{{ formData.applyUnit || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请人员">
            <span>{{ formData.applyPerson || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请时间">
            <span>{{ formData.applyTime || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请原因">
            <span>{{ formData.applyReason || '--' }}</span>
          </el-form-item>
          <el-form-item label="相关附件">
            <div class="attachment-list">
              <div v-for="(file, index) in formData.applyFiles" :key="index" class="file-item">
                <div class="attachment-item" @click="handleFileClick(file)" :title="file.fileName">
                  {{ file.fileName.length > 35 ? file.fileName.substring(0, 35) + '...' : file.fileName }}
                </div>
              </div>
              <div v-if="!formData.applyFiles || formData.applyFiles.length === 0">--</div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 复工审核 -->
      <div class="section">
        <div class="section-title">复工审核</div>
        <el-form label-width="100px" label-position="left">
          <el-form-item label="复核单位">
            <span>{{ formData.auditUnit || '--' }}</span>
          </el-form-item>
          <el-form-item label="审核人员">
            <span>{{ formData.auditPerson || '--' }}</span>
          </el-form-item>
          <el-form-item label="审核时间">
            <span>{{ formData.auditTime || '--' }}</span>
          </el-form-item>
          <el-form-item label="审核意见">
            <span>{{ formData.auditOpinion || '--' }}</span>
          </el-form-item>
          <el-form-item label="相关附件">
            <div class="attachment-list">
              <div v-for="(file, index) in formData.auditFiles" :key="index" class="file-item">
                <div class="attachment-item" @click="handleFileClick(file)" :title="file.fileName">
                  {{ file.fileName.length > 35 ? file.fileName.substring(0, 35) + '...' : file.fileName }}
                </div>
              </div>
              <div v-if="!formData.auditFiles || formData.auditFiles.length === 0">--</div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 审核意见 -->
      <div class="section">
        <div class="section-title">审核意见</div>
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px" label-position="top">
          <el-form-item label="审核结果" prop="confirmResult">
            <el-radio-group v-model="formData.confirmResult">
              <el-radio value="1">同意复工</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="confirmOpinion">
            <el-input
              v-model="formData.confirmOpinion"
              type="textarea"
              :rows="2"
              maxlength="100"
              placeholder="请输入审核意见"
            />
          </el-form-item>
          <el-form-item label="上传附件" prop="files">
            <el-input v-model="formData.files" v-show="false"></el-input>
            <file-uploader
              v-model="formData.fileVoList"
              ref="fileUploaderRef"
              @change="handleFileListChange"
              @uploading="updateUploadingStatus"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading" :disabled="isUploading">确定</el-button>
      </div>
    </template>
  </popup-side>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineEmits, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import FileUploader from '@/components/upload/FileUploader.vue'
import $API from '~/common/api'
import { useRoute, useRouter } from 'vue-router'
import config from '~/config'

const route = useRoute()
const router = useRouter()
const visible = ref(false)
const loading = ref(false)
const isUploading = ref(false)
const formRef = ref<any>(null)
const formData = reactive({
  applyUnit: '',
  applyPerson: '',
  applyTime: '',
  applyReason: '',
  applyFiles: [],
  auditUnit: '',
  auditPerson: '',
  auditTime: '',
  auditOpinion: '',
  auditFiles: [],
  confirmResult: '1',
  confirmOpinion: '整改已完成，同意复工',
  files: '',
  fileVoList: [],
  uploadFiles: []
})
const emit = defineEmits(['submit'])
const fileUploaderRef = ref<any>(null)
const rules = {
  confirmResult: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  confirmOpinion: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
  files: [{ required: true, message: '请上传附件', trigger: 'change' }]
}
// 先获取复工信息
const getWork = async () => {
  let res = await $API.post({
    url: 'edu-inter-server/scoreManage/queryUnWorkConfirm',
    data: {
      unitId: route.query.id
    }
  })
  console.log('res', res)
  if (res && res.code === 'success') {
    formData.applyUnit = res.data[0].unitName || ''
    formData.applyPerson = res.data[0].createName || ''
    formData.applyTime = res.data[0].createTime || ''
    formData.applyReason = res.data[0].content || ''
    formData.applyFiles = res.data[0].fileAttachments || []
    formData.auditUnit = res.data[1].unitName || ''
    formData.auditPerson = res.data[1].createName || ''
    formData.auditTime = res.data[1].createTime || ''
    formData.auditOpinion = res.data[1].content || ''
    formData.auditFiles = res.data[1].fileAttachments || []
  }
}
const open = () => {
  resetForm()
  visible.value = true
  getWork()
}

const handleFileClick = (file) => {
  const suffix = file.filePath.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + file.filePath
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    window.open(config.downloadFileUrl + file.filePath, '_blank')
  }
}

const handleFileListChange = (fileList) => {
  console.log('fileList', fileList)
  if (fileList && fileList.length > 0) {
    formData.files = fileList.length + ''
  } else {
    formData.files = ''
  }
}

const updateUploadingStatus = (status) => {
  isUploading.value = status
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    // 设置加载状态为true，防止重复点击
    loading.value = true
    // 修正获取上传文件ID的方式
    if (fileUploaderRef.value && typeof fileUploaderRef.value.getFileIds === 'function') {
      formData.uploadFiles = fileUploaderRef.value.getFileIds()
    } else {
      formData.uploadFiles = formData.fileVoList.map((file: any) => file.id) as never[]
    }
    let data = {
      unitId: route.query.id,
      unitName: formData.applyUnit,
      fileIds: formData.uploadFiles,
      content: formData.confirmOpinion,
      isAgree: formData.confirmResult
    }
    let res: any = await $API.post({
      url: 'edu-inter-server/scoreManage/workConfirm',
      data: data
    })
    if (res && res.code === 'success') {
      ElMessage.success('审核成功')
      visible.value = false
      emit('submit')
    }
  } catch (error) {
    console.error('表单验证失败')
  } finally {
    // 无论成功或失败，都需要重置加载状态
    loading.value = false
  }
}

const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()

  // 重置文件上传组件
  nextTick(() => {
    if (fileUploaderRef.value && typeof fileUploaderRef.value.reset === 'function') {
      fileUploaderRef.value.reset()
    }
  })

  formData.fileVoList = []
  formData.uploadFiles = []
  formData.confirmResult = '1'
  formData.confirmOpinion = '整改已完成，同意复工'
  formData.files = ''
}
const handleCancel = () => {
  resetForm()
  visible.value = false
}

watch(visible, (val) => {
  if (!val) {
    resetForm()
  }
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.resume-confirm-content {
  padding: 20px;

  .section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
      padding-left: 10px;
    }
  }

  .attachment-list {
    .attachment-item {
      color: #409eff;
      cursor: pointer;
      line-height: 1.8;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .file-list {
    .file-item {
      display: flex;
      justify-content: space-between;
    }
  }
}

.dialog-footer {
  padding: 20px;
  text-align: right;
}

:deep(.el-form-item__content) span {
  line-height: 32px;
}

.w_file_btn {
  width: 136px;
  height: 32px;
  border: 1px solid #527cff;
  text-align: center;
  color: #527cff;
  cursor: pointer;
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
}

.w_file_t {
  color: #a8abb2;
}

:deep(.el-form) {
  padding-left: 20px;
}
</style>
