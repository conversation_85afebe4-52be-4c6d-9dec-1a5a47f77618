<template>
  <popup-side v-model="visible" popupTitle="新增扣分内容" width="620px" :showFooter="true" :closeOnClickModal="false">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="drawer-form" label-position="top">
      <el-form-item label="扣分项目" prop="deductionItemsId">
        <el-select v-model="form.deductionItemsId" placeholder="请选择扣分项目" @change="getScoreItems">
          <el-option v-for="item in deductionItems" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="处罚事项" prop="settingId">
        <el-select v-model="form.settingId" placeholder="请选择处罚事项" :disabled="!form.deductionItemsId">
          <el-option v-for="item in scoreItems" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="记分标准">
        <el-input v-model="form.settingScore" disabled placeholder="请输入记分标准" />
      </el-form-item>

      <el-form-item label="详细扣分内容" prop="content">
        <el-input v-model="form.content" type="textarea" :rows="2" maxlength="100" placeholder="请输入详细扣分内容" />
      </el-form-item>
      <el-form-item label="累计出现次数" prop="occurCount">
        <el-input-number
          v-model="form.occurCount"
          :min="1"
          :precision="0"
          :step="1"
          :controls="true"
          placeholder="请输入累计出现次数"
          onkeypress="return( /[\d]/.test(String.fromCharCode(event.keyCode)))"
        />
      </el-form-item>
      <el-form-item label="实际扣分" prop="score">
        <el-input v-model="form.score" disabled placeholder="请输入实际扣分" />
      </el-form-item>

      <el-form-item label="上传附件" prop="files">
        <el-input v-model="form.files" v-show="false"></el-input>
        <file-uploader
          ref="uploadRef"
          v-model="uploadFiles"
          @change="handleFileChange"
          @uploading="updateUploadingStatus"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="isUploading">确定</el-button>
      </div>
    </template>
  </popup-side>
</template>

<script setup lang="ts">
import FileUploader from '@/components/upload/uploader.vue'
import { ElMessage } from 'element-plus'
import { inject, onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'

// 定义表单数据接口
interface ScoreRecordForm {
  deductionItemsId: string // 扣分项 ID
  deductionItemsName: string // 扣分项名称
  settingId: string // 处罚事项 ID
  settingName: string // 处罚事项名称
  settingScore: number | string | null // 标准分值
  occurCount: number // 发生累计次数
  score: number | string // 实际扣分值
  content: string // 详细扣分内容
  remark: string // 备注内容
  fileIds: string // 附件 ids，逗号拼接
  unitId: string | undefined // 单位id
  type: string // 操作类型
  typeName: string // 类型名称
  files?: string // 附件数量
}

const unitInfo = inject('unitName')
const route = useRoute()
const visible = ref(false)
const formRef = ref<any>(null)
const uploadRef = ref<any>(null)
const fileList = ref([])
const uploadFiles = ref([])
const deductionItems = ref([]) // 扣分项目列表
const scoreItems = ref([]) // 处罚事项列表
const loading = ref(false) // 添加loading状态变量
const isUploading = ref(false)
const form = reactive<ScoreRecordForm>({
  deductionItemsId: '', // 扣分项 ID
  deductionItemsName: '', // 扣分项名称
  settingId: '', // 处罚事项 ID
  settingName: '', // 处罚事项名称
  settingScore: '', // 标准分值，改为空字符串作为默认值
  occurCount: 1, // 发生累计次数
  score: '', // 实际扣分值，改为空字符串作为默认值
  content: '', // 详细扣分内容
  remark: '', // 备注内容
  fileIds: '', // 附件 ids，逗号拼接
  unitId: route.query.id as string, // 单位id
  type: '1', // 操作类型，默认值为 1
  typeName: '扣分'
})

const rules = {
  deductionItemsId: [{ required: true, message: '请选择扣分项目', trigger: 'change' }],
  settingId: [{ required: true, message: '请选择处罚事项', trigger: 'change' }],
  occurCount: [
    { required: true, message: '请输入累计出现次数', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value === undefined || value === null) {
          callback(new Error('请输入累计出现次数'))
        } else if (!Number.isInteger(value) || value < 1 || value > 9999) {
          callback(new Error('累计出现次数必须是1-9999之间的整数'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  content: [{ required: true, message: '请输入详细扣分内容', trigger: 'blur' }],
  files: [{ required: true, message: '请上传附件', trigger: 'change' }],
  score: [{ required: true, message: '请输入实际扣分', trigger: 'blur' }]
}
const updateUploadingStatus = (status) => {
  isUploading.value = status
}

const handleFileChange = (files) => {
  if (files && files.length > 0) {
    form.files = files.length + ''
    form.fileIds = uploadRef.value?.getFileIds() || ''
  } else {
    form.files = ''
    form.fileIds = ''
  }
}

const emit = defineEmits(['submit', 'cancel'])

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  fileList.value = []
  uploadFiles.value = []
  if (uploadRef.value) {
    uploadRef.value.reset()
  }
  form.deductionItemsId = ''
  form.deductionItemsName = ''
  form.settingId = ''
  form.settingName = ''
  form.settingScore = '' // 改为空字符串
  form.occurCount = 1
  form.score = '' // 改为空字符串
  form.content = ''
  form.remark = ''
  form.fileIds = ''
  form.unitId = route.query.id as string
  form.type = '1'
  form.typeName = '扣分'
}

// 取消处理
const handleCancel = () => {
  resetForm()
  visible.value = false
  emit('cancel')
}

// 提交处理
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true // 开始加载状态
      try {
        // 提交前确保实际扣分计算正确
        form.score = Number(form.settingScore) * Number(form.occurCount)
        // 确保获取最新的文件IDs
        form.fileIds = uploadRef.value?.getFileIds() || ''
        form.deductionItemsName =
          deductionItems.value.find((item: any) => item.value === form.deductionItemsId)?.label || ''
        form.settingName = scoreItems.value.find((item: any) => item.value === form.settingId)?.label || ''

        // 构建提交数据
        const submitData = {
          ...form,
          unitName: unitInfo?.value?.deptName
        }
        console.log('submitData', submitData)
        let res = await $API.post({
          url: 'edu-inter-server/scoreManage/addDeductPoints',
          data: submitData
        })
        if (res && res.code === 'success') {
          ElMessage.success('新增成功')
          // 重置表单并关闭弹窗
          resetForm()
          visible.value = false
          emit('submit')
        }
      } catch (error) {
        console.error('提交失败', error)
      } finally {
        loading.value = false // 结束加载状态
      }
    }
  })
}

// 获取扣分项目列表
const getDeductionItems = async () => {
  try {
    const res = await $API.get({
      url: 'train-server/train/dict/queryDictList',
      params: { dictType: 'score_deduction_item' }
    })
    if (res && res.data) {
      deductionItems.value = res.data.map((item) => ({
        label: item.paramValue,
        value: item.paramCode
      }))
    }
  } catch (error) {
    console.error('获取扣分项目失败', error)
  }
}

// 获取处罚事项列表
const getScoreItems = async (deductionItem) => {
  scoreItems.value = []
  form.settingId = ''
  form.settingScore = '' // 改为空字符串
  form.score = '' // 改为空字符串
  try {
    const res = await $API.post({
      url: 'edu-inter-server/score/queryScoreSettingList',
      params: { deductionItems: form.deductionItemsId }
    })
    if (res && res.data) {
      scoreItems.value = res.data.map((item) => ({
        label: item.punishmentMatters,
        value: item.id,
        standard: item.score
      }))
    }
  } catch (error) {
    console.error('获取处罚事项失败', error)
  }
}

// 监听处罚事项变化
watch(
  () => form.settingId,
  (newVal) => {
    if (newVal) {
      const selectedItem = scoreItems.value.find((item) => item.value === newVal)
      if (selectedItem && selectedItem.standard !== undefined) {
        form.settingScore = Number(selectedItem.standard)
        form.score = form.settingScore * (form.occurCount || 0)
      }
    }
  },
  { immediate: true }
)

watch(
  () => form.occurCount,
  (newVal) => {
    // 确保 settingScore 不为 null，如果为 null 则默认为 0
    form.score = Number(form.settingScore || 0) * Number(newVal || 0)
  },
  { immediate: true }
)

watch(visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 打开弹窗
const open = () => {
  visible.value = true
  resetForm() // 打开时重置表单
  getDeductionItems() // 获取扣分项目列表
}

onMounted(() => {
  getDeductionItems()
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.drawer-form {
  padding: 20px;
}

.upload-wrapper {
  .upload-btn {
    margin-bottom: 8px;
  }
  .upload-tip {
    color: #909399;
    font-size: 12px;
  }
}

:deep(.upload-button) {
  .el-icon {
    margin-right: 4px;
    vertical-align: middle;
  }
}

:deep(.el-upload-list) {
  width: 100%;
}

.w_file_btn {
  width: 136px;
  height: 32px;
  border: 1px solid #527cff;
  text-align: center;
  color: #527cff;
  cursor: pointer;
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
}

.w_file_t {
  color: #a8abb2;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
