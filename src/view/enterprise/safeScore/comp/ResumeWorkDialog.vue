<template>
  <popup-side v-model="visible" popupTitle="复工审核" width="620px" :closeOnClickModal="false">
    <div class="resume-work-form">
      <div class="info-section">
        <h3>申请信息</h3>
        <el-form label-width="100px" label-position="left">
          <el-form-item label="申请单位">
            <span>{{ workInfo.unitName || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请人员">
            <span>{{ workInfo.applyUser || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请时间">
            <span>{{ workInfo.applyTime || '--' }}</span>
          </el-form-item>
          <el-form-item label="申请内容">
            <span>{{ workInfo.applyContent || '--' }}</span>
          </el-form-item>
          <el-form-item label="相关附件">
            <div class="attachment-list">
              <div
                v-for="(file, index) in workInfo.fileList"
                :key="index"
                class="attachment-item"
                @click="handleFileClick(file)"
              >
                {{ file.fileName }}
              </div>
              <div v-if="!workInfo.fileList || workInfo.fileList.length === 0">--</div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div class="audit-section">
        <h3>审核意见</h3>
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px" label-position="top">
          <el-form-item label="审核结果" prop="auditResult">
            <el-radio-group v-model="formData.auditResult">
              <el-radio value="1">同意复工</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="auditComment">
            <el-input
              v-model="formData.auditComment"
              type="textarea"
              :rows="2"
              maxlength="100"
              placeholder="请输入审核意见"
            />
          </el-form-item>
          <el-form-item label="上传附件" prop="files" class="upload-item">
            <el-input v-model="formData.files" v-show="false"></el-input>
            <file-uploader
              ref="fileUploaderRef"
              v-model="uploadFiles"
              @change="handleFileChange"
              @uploading="updateUploadingStatus"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="isUploading">确定</el-button>
      </div>
    </template>
  </popup-side>
</template>

<script setup lang="ts">
import { ref, defineExpose, watch, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import FileUploader from '@/components/upload/FileUploader.vue'
import $API from '~/common/api'
import { useRoute, useRouter } from 'vue-router'
import config from '~/config'

const route = useRoute()
const router = useRouter()
const visible = ref(false)
const formRef = ref<any>(null)
const loading = ref(false)
const isUploading = ref(false)
const workInfo = ref({
  unitName: '',
  applyUser: '',
  applyTime: '',
  applyContent: '',
  fileList: []
})
const formData = ref({
  auditResult: '1',
  auditComment: '整改已完成，同意复工',
  files: '',
  fileIds: ''
})
const emit = defineEmits(['submit'])

const rules = {
  auditResult: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  auditComment: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
  files: [{ required: true, message: '请上传附件', trigger: 'change' }]
}

const fileUploaderRef = ref<any>(null)
const uploadFiles = ref([])

// 先获取复工信息
const getWork = async () => {
  try {
    let res: any = await $API.post({
      url: 'edu-inter-server/scoreManage/queryUnWorkApprove',
      data: {
        unitId: route.query.id
      }
    })
    console.log('res', res)
    if (res.code === 'success' && res.data) {
      workInfo.value = {
        unitName: res.data[0].unitName || '',
        applyUser: res.data[0].createName || '',
        applyTime: res.data[0].createTime || '',
        applyContent: res.data[0].content || '',
        fileList: res.data[0].fileAttachments || []
      }
    }
  } catch (error) {
    console.error('获取复工信息异常', error)
  }
}

const handleFileClick = (file) => {
  const suffix = file.filePath.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + file.filePath
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    window.open(config.downloadFileUrl + file.filePath, '_blank')
  }
}

const updateUploadingStatus = (status) => {
  isUploading.value = status
}

// 处理文件变化
const handleFileChange = (files) => {
  if (files && files.length > 0) {
    formData.value.files = files.length + ''
  } else {
    formData.value.files = ''
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    // 获取文件ID列表
    if (fileUploaderRef.value) {
      formData.value.fileIds = fileUploaderRef.value.getFileIds()
    }
    // 表单验证通过，处理表单提交逻辑
    let data = {
      unitId: route.query.id,
      unitName: workInfo.value.unitName,
      fileIds: formData.value.fileIds,
      content: formData.value.auditComment,
      isAgree: formData.value.auditResult
    }
    let res: any = await $API.post({
      url: 'edu-inter-server/scoreManage/workApproval',
      data: data
    })
    if (res && res.code === 'success') {
      ElMessage.success('审核成功')
      visible.value = false
      emit('submit')
    }
  } catch (error) {
    console.error('表单验证失败')
  } finally {
    loading.value = false
  }
}

const open = () => {
  resetForm()
  visible.value = true
  getWork()
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  if (fileUploaderRef.value) {
    fileUploaderRef.value.reset()
  }
  uploadFiles.value = []
  workInfo.value = {
    unitName: '',
    applyUser: '',
    applyTime: '',
    applyContent: '',
    fileList: []
  }
  formData.value = {
    auditResult: '1',
    auditComment: '整改已完成，同意复工',
    files: '',
    fileIds: ''
  }
}
// 修改取消按钮点击事件
const handleCancel = () => {
  resetForm()
  visible.value = false
}
// 监听visible变化，关闭时重置表单
watch(visible, (val) => {
  if (!val) {
    resetForm()
  }
})

watch(
  () => uploadFiles.value.length,
  (val) => {
    if (val > 0) {
      formData.value.files = val + ''
    } else {
      formData.value.files = ''
    }
  },
  { immediate: true }
)
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.resume-work-form {
  padding: 20px;

  .info-section,
  .audit-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .attachment-list {
    .attachment-item {
      color: #409eff;
      cursor: pointer;
      line-height: 1.8;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .w_file_btn {
    width: 136px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    color: #527cff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
  }

  //background: red;
  .w_file_t {
    color: #a8abb2;
  }
}
.dialog-footer {
  padding: 20px;
  text-align: right;
}

:deep(.el-form) {
  padding-left: 20px;
}
</style>
