<template>
  <div class="homework">
    <div class="head">
      <li>
        <div>
          <p>总作业数</p>
          <p>{{ countObj.workCount }}</p>
        </div>
      </li>
      <li>
        <div>
          <p>今日作业数</p>
          <p>{{ countObj.todayWorkNum }}</p>
        </div>
      </li>
      <li>
        <div>
          <p>申请中作业数</p>
          <p>{{ countObj.applyWorkCount }}</p>
        </div>
      </li>
      <li>
        <div>
          <p>作业中作业数</p>
          <p>{{ countObj.workingCount }}</p>
        </div>
      </li>
      <li>
        <div>
          <p>今日已完成作业数</p>
          <p>{{ countObj.todayFinishNum }}</p>
        </div>
      </li>
      <li>
        <div>
          <p>出现隐患作业数</p>
          <p>{{ countObj.violationCount }}</p>
        </div>
      </li>
    </div>

    <stopCom @addDialog="addDialog" />
    <EditCom :nameee="namee" ref="dialogRef" @action="handleFun"></EditCom>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import EditCom from './comp/edit.vue'
import stopCom from './comp/stopCom.vue'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const route = useRoute()
const dialogRef = ref()
const tableRef = ref()
const namee = ref('')
const countObj = ref({
  workCount: 0,
  todayWorkNum: 0,
  applyWorkCount: 0,
  workingCount: 0,
  todayFinishNum: 0,
  violationCount: 0
})
const addDialog = (obj: any, str: any) => {
  namee.value = str
  dialogRef.value.showDialog(obj)
}

const getCountData = async () => {
  let res: any = await $API.get({
    url: 'edu-inter-server/workManage/queryStatisticWithProj',
    params: {
      unitId: route.query.id,
      relId: route.query.id,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })
  if (res.data && res.code == 'success') {
    countObj.value = res.data
  }
}
getCountData()
function handleFun() {
  tableRef.value.refreshFetch()
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.homework {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;

  .head {
    width: 100%;
    margin-top: 20px;
    color: #fff;
    list-style: none;
    height: 80px;
    display: flex;
    justify-content: space-around;

    li:nth-child(1) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/one.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #527cff;
        }
      }
    }

    li:nth-child(2) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/two.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #0097a9;
        }
      }
    }

    li:nth-child(3) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/tree.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #00cfe8;
        }
      }
    }

    li:nth-child(4) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/four.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #527cff;
        }
      }
    }

    li:nth-child(5) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/five.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #409d13;
        }
      }
    }

    li:nth-child(6) {
      width: 198px;
      border-radius: 12px;
      height: 100%;

      background-image: url('@/assets/img/six.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      div {
        padding-left: 38%;
        box-sizing: border-box;
        padding-top: 16px;
        font-size: 14px;

        p:nth-child(1) {
          color: #333333;
        }

        p:nth-child(2) {
          margin-top: 1px;
          font-size: 20px;
          color: #fd8410;
        }
      }
    }
  }
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  border: none;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}
</style>
