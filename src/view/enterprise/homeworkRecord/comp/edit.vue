<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>{{ props.nameee == '1' ? '详情' : '监测' }}</div>
        </div>
      </template>
      <el-tabs type="card" v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="作业详情" name="1"> </el-tab-pane>
        <el-tab-pane label="智能监测" name="2"> </el-tab-pane>
        <!-- <el-tab-pane label="过程隐患" name="3"> </el-tab-pane> -->
      </el-tabs>
      <Detail :detailObj="detailObj" v-show="activeName === '1'" ref="workRef" @showgis="handleGis" />
      <JianCe v-show="activeName === '2'" ref="checkRef" />
      <YinHuan v-show="activeName === '3'" ref="recordRef" />
      <!--      <template #footer>-->
      <!--        <div class="w_dialog_foot">-->
      <!--          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>-->
      <!--          &lt;!&ndash; <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()"-->
      <!--            >保存</el-button-->
      <!--          > &ndash;&gt;-->
      <!--        </div>-->
      <!--      </template>-->
    </el-drawer>
    <el-dialog v-model="isShowGis" title="位置" class="h-[640px] w-[600px]">
      <div v-if="isShowGis" class="w-[860px] h-[500px]">
        <floorMap :floor-info="floorData" :isAddMark="true" :pointer="pointer" @add-mark="addMark"></floorMap>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { GISOBJ } from '@/view/indoorMap/gisGlobal'
import floorMap from '@/view/indoorMap/index.vue'

import Detail from './detail.vue'
import JianCe from './jiance.vue'
import YinHuan from './yinhuan.vue'

const isShowGis = ref(false)
// const pointer = ref({
//   x: 3028718.761922908,
//   y: 909491.4322155592,
// })
const pointer = ref({
  x: '',
  y: ''
})

const floorData = ref({
  unitId: '',
  buildingId: '',
  floorId: '',
  floorAreaImg: ''
})

const addMark = (val: { x: number; y: number; text: string }) => {
  console.log('🚀 ~ addMark ~ val:', val)
}

const handleGis = (obj) => {
  isShowGis.value = true
  pointer.value.x = obj.x
  pointer.value.y = obj.y
  floorData.value.unitId = obj.unitId
  floorData.value.buildingId = obj.buildingId
  floorData.value.floorId = obj.floorId
  console.log(obj)
}

const props = defineProps({
  nameee: {
    type: String,
    default: '1'
  }
})
const activeName = ref('1')
// 作业详情
const workRef = ref()
// 智能检测
const checkRef = ref()
// 异常记录
const recordRef = ref()

const detail = ref('')

const handleClick = (tab: any) => {
  if (tab.props.name == '1') {
    nextTick(() => {
      workRef.value.initData(detail.value)
    })
  } else if (tab.props.name == '2') {
    checkRef.value.initData(detail.value)
  } else {
    recordRef.value.initData(detail.value)
  }
}
// const emits = defineEmits(['action', 'submit'])

const drawer = ref(false)
// const options = ref<Record<string, any>[]>([])

// const url = ref(config.update_file + '/file/uploadfile')

// const handleClose = () => {
//   drawer.value = false
//   AQXYList.value = []
//   YYZZList.value = []
// }
// async function getDeatail(id: string) {
//   let res: any = await $API.get({
//     url: 'edu-inter-server/relatedPartner/unitInfo',
//     params: {
//       unitId: id
//     }
//   })
//   formInline.value = { ...res.data }

//   if (!formInline.value.blExpireTime) {
//     formInline.value.blExpireTime = null
//   }
//   if (res.data.blFiles) {
//     YYZZList.value = res.data.blFiles.map((item: any) => {
//       return {
//         name: item.fileName,
//         url: config.downloadFileUrl + item.filePath,
//         id: item.id
//       }
//     })
//   }

//   if (res.data.spFiles) {
//     AQXYList.value = res.data.spFiles.map((item: any) => {
//       return {
//         name: item.fileName,
//         url: config.downloadFileUrl + item.filePath,
//         id: item.id
//       }
//     })
//   }
// }
const detailObj = ref({})
const showDialog = (row: any) => {
  detail.value = row.id
  if (row.id) {
    detailObj.value = row
    console.log('detailObj:', detailObj.value)
  }
  drawer.value = true
  nextTick(() => {
    activeName.value = props.nameee
    if (activeName.value == '1') {
      workRef.value.initData(detail.value)
    } else if (activeName.value == '2') {
      checkRef.value.initData(detail.value)
    }
  })
}
// function selectedUser(list: any) {
//   console.log(list, '拿到的组件')
//
//   formInline.value.relatedManager = list.map((item: any) => item.userName).join(',')
//   formInline.value.relatedManagerIds = list.map((item: any) => item.id).join(',')
// }

// async function onSubmit() {
//   ruleFormRef.value?.validate(async (valid: boolean) => {
//     if (valid) {
//       formInline.value.blFileId = YYZZList.value.map((item: any) => item.id).join(',')
//       formInline.value.spFileId = AQXYList.value.map((item: any) => item.id).join(',')
//       let res: any = await $API.post({
//         url: 'edu-inter-server/relatedPartner/updateRelatedInfo',
//         data: {
//           ...formInline.value,
//         },
//       })
//
//       if (res.code == 'success') {
//         ElMessage({
//           message: formInline.value.id ? '编辑成功' : '新增成功',
//           type: 'success',
//         })
//         handleClose()
//         emits('action')
//       } else {
//         // ElMessage.error(res.msg)
//       }
//     }
//   })
// }
// function getYYZZ(list: any) {
//   YYZZList.value = list
// }
// function getAQXY(list: any) {
//   AQXYList.value = list
// }
// async function getOptionsByXGFType() {
//   // options.value=
//   let res: any = await $API.get({
//     url: 'edu-inter-server/coop/getCoopTypeList',
//     data: {
//       pageNo: 1,
//       pageSize: -1,
//     },
//   })
//   if (res.code == 'success') {
//     options.value = res.data.rows
//   }
// }

// const handleInput = (value) => {
//   // formInline.value.phone = value.replace(/\D/g, '')
//   formInline.value.phone = value.replace(/\D/g, '')
// }
// // const handleInput2 = (value) => {
// //   formInline.value.relatedSceneTel = value.replace(/\D/g, '')
// // }
// const handleInput3 = (value) => {
//   formInline.value.registeredCapital = value.replace(/\D/g, '')
// }
// getOptionsByXGFType()

// function selectChange(id: any) {
//   console.log(id, 'item')
//
//   // formInline.value.relatedType
// }

// watch(
//   () => formInline.value.relatedType,
//   (newVal: any) => {
//     formInline.value.relatedTypeName = options.value?.find((item: any) => item.id == newVal)?.name
//   },
//   {
//     deep: true,
//   }
// )

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
    overflow: auto !important;
  }

  :deep(.el-tab-pane) {
    height: 100% !important;
    //overflow: auto !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  :deep(.el-drawer__close-btn) {
    position: fixed !important;
    top: 1% !important;
    right: 2% !important;
  }

  .w_add_plan_header {
    font-weight: 700;

    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 4px;
      height: 16px;
      background: #527cff;
      border-radius: 2px 2px 2px 2px;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_plan_con_box {
    font-size: 0.875rem;
    //background: yellowgreen;
    display: grid;
    height: 790px;
    grid-template-rows: 790px;
    grid-template-columns: 1fr;
    color: rgba(72, 74, 77, 1);

    .bg_text {
      color: rgba(48, 49, 51, 1);
      font-size: 16px;
    }

    .w_content_box {
      //background: red;
      width: 100%;
      height: 790px;

      .w_plan_con_one {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .div_con20 {
          height: 20px;
          line-height: 20px;
          display: flex;

          .w_flex-1 {
            flex: 1;
          }

          .w_flex-2 {
            flex: 2;
            overflow: auto;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .w_dialog_from_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .w_file_btn {
          width: 136px;
          height: 32px;
          border: 1px solid #527cff;
          text-align: center;
          color: #527cff;
          cursor: pointer;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
        }

        //background: red;
        .w_file_t {
          color: #a8abb2;
        }
      }

      .w_table_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 10px 0 16px;
        margin-bottom: 22px;

        .w_dialog_from_title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
        }

        .w_add_bor {
          width: 100px;
          border: 1px solid #527cff;
          color: #527cff;
          text-align: center;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
          cursor: pointer;
        }

        .w_span_cursor {
          color: #527cff;
          cursor: pointer;
        }

        :deep(.el-table__header .el-table__cell) {
          background-color: rgba(245, 246, 249, 1);
          /* 表头背景色 */
          color: #606266;
          /* 表头字体颜色 */
          font-size: 14px;
          /* 表头字体大小 */
          height: 48px;
        }
      }

      .w_page_box {
        margin-top: 16px;
        height: 32px;
        display: flex;
        justify-content: end;
        padding-right: 24px;
      }
    }
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;

    //background-color: red;
    //padding-right: 20px;
    .w_btn {
      height: 32px;
    }

    .w_btn_bg {
      background-color: rgba(82, 124, 255, 1) !important;
    }
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-tabs--card > .el-tabs__header) {
  border: none;
}

:deep(.el-tabs__nav-scroll) {
  // padding: 1000px;
  margin-left: 25%;
  // color: #6b778c;
}

:deep(.el-tabs--top) {
  flex-direction: column !important;
}

:deep(.el-tabs__item) {
  border-bottom: 1px solid rgb(237, 231, 231) !important;
}
</style>
