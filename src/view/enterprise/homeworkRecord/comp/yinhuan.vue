<template>
  <div class="detail">
    <div class="table">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column type="index" label="序号" width="70" align="center" />
        <el-table-column prop="hazardSourceName" label="来源" width="80" align="center" />
        <el-table-column prop="hazardDesc" label="内容描述" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="createByName" label="检查人" width="90" show-overflow-tooltip align="center">
        </el-table-column>
        <el-table-column prop="eventTime" label="提交时间点" width="120" align="center" />
        <el-table-column prop="files" label="图片" width="80" align="center">
          <template #default="scope">
            <el-image
              v-if="scope.row.files && scope.row.files.length > 0"
              :src="config.appServer + scope.row.files[0].fileUrlWithBucket"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[config.appServer + scope.row.files[0].fileUrlWithBucket]"
              :initial-index="4"
              fit="cover"
            />
            <div v-else>--</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:current-page="pageNo"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import $API from '@/common/api'
import { ref } from 'vue'
import config from '~/config'

const tableData = ref([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const id = ref<string>('')

const getListData = async (newId: string) => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/pageOperationHazardRecord',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      operationBaseId: newId
      // operationBaseId: 'bf9786f036e643b183e4afae9dcaf3f0',
    }
  })
  if (res.data) {
    tableData.value = res.data.rows
    total.value = res.data.total
  }
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  getListData(id.value)
}

const handleCurrentChange = (val: number) => {
  pageNo.value = val
  getListData(id.value)
}

const initData = (newId: string) => {
  id.value = newId
  getListData(newId)
}

defineExpose({
  initData
})

defineOptions({ name: 'homeWorkYinHuanCom' })
</script>

<style scoped lang="scss">
.detail {
  width: 100%;
  color: black;

  .text {
    margin-left: 5px;

    font-size: 12px;

    p {
      margin-top: 20px;
    }
  }

  .table {
    width: 100%;
    margin-top: 20px;

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.el-tooltip) {
  width: 100% !important;
}
</style>
