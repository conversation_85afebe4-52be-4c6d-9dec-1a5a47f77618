<template>
  <div class="detail">
    <h2>作业基本详情</h2>
    <div class="text">
      <p>作业类型：{{ detailObj.operationTypeName }}</p>
      <!--      <p>作业等级：特级动火作业</p>-->
      <p>作业等级：{{ detailObj.operationLevelName }}</p>
      <p>作业位置：{{ detailObj.operationLocationPart }}<span class="w_location" @click="changeGis">查看位置</span></p>
      <p>作业位置：{{ detailObj.buildingName }}{{ detailObj.floorName }}</p>
      <p>作业编号: {{ detailObj.operationNo }}<span class="w_location" @click="goUrl">查看作业票</span></p>
      <p>{{ ui.zhLogo === 'yanchang' ? '承包商企业' : '相关方企业' }}：{{ detailObj.operationUnitName }}</p>
      <p>作业申请人：{{ detailObj.applyPersonName }}</p>
      <p>作业负责人：{{ detailObj.operationPersonName }}</p>
      <p>作业监护人：{{ detailObj.operationSupervisorName }}</p>
      <p>作业实施时间：{{ detailObj.planStartTime }}</p>
    </div>
    <div class="table">
      <h2 class="mb-[10px]">普通作业人员</h2>
      <jing-table :data="normalTableData" :columns="normalColumns" border style="width: 100%" />

      <h2 class="mt-[20px] mb-[10px]">特殊作业人员</h2>
      <jing-table :data="specialTableData" :columns="specialColumns" border style="width: 100%" />
    </div>
  </div>
</template>

<script setup lang="ts">
import $API from '@/common/api'
import config from '@/config'
import { useUserInfo } from '@/store'
import type { TableColumnCtx } from 'element-plus'
import { ref } from 'vue'
import JingTable from './jingTable.vue'
interface TableColumn extends Partial<TableColumnCtx<any>> {
  slot?: any
}

const ui = useUserInfo()
const emits = defineEmits(['showgis'])
// import config from '~/config'
const isShowGis = ref(false)
const pointer = ref({
  x: '',
  y: ''
})

const floorData = ref({
  unitId: 'e3de3e5405664eacb434d8e2aafe6d37',
  buildingId: '110000DW1832753728612990976_009',
  floorId: '110000DW1832753728612990976_009_U001',
  floorAreaImg: ''
})

const addMark = (val: { x: number; y: number; text: string }) => {
  console.log('🚀 ~ addMark ~ val:', val)
}

const detailObj = ref({})
let url = 'src/assets/image/img.png'
const tableData = ref<any>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
// 分离普通和特殊作业人员数据
const normalTableData = ref<any>([])
const specialTableData = ref<any>([])

const goUrl = async () => {
  // let res: any = await $API.post({
  //   url: 'edu-inter-server/workManage/getPdfFile',
  //   params: {
  //     operationBaseId: detailObj.value.id
  //   }
  // })
  // if (res.data && res.code == '200') {
  // }
  let url = config.workPreview + detailObj.value.id
  window.open(url, '_target')
}

const getData = async (id: string) => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/queryOperBaseDetail',
    params: {
      operationBaseId: id
      // operationBaseId: '0e9c4f113573442591570d82de12fa90',
    }
  })
  if (res.data && res.code == '200') {
    detailObj.value = res.data
  }
}

const getListData = async (id: string) => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/queryOperBaseUserDetail',
    params: {
      operationBaseId: id
    }
  })
  if (res.data) {
    normalTableData.value = res.data
  }
}

const getSpecialListData = async (id: string) => {
  let res: any = await $API.post({
    url: 'edu-inter-server/workManage/queryOperBaseUserSpecDetail',
    params: {
      operationBaseId: id
    }
  })
  if (res.data) {
    specialTableData.value = res.data
  }
}

function changeGis() {
  // console.log('发送gis',ui.value.erecordUnitId)
  // return
  // pointer.value.y = detailObj.value.latitude
  // pointer.value.x = detailObj.value.longitude
  // floorData.value.buildingId = detailObj.value.buildingId
  // floorData.value.floorId = detailObj.value.floorId
  // floorData.value.unitId = ui.value.erecordUnitId

  let obj = {
    y: detailObj.value.latitude,
    x: detailObj.value.longitude,
    buildingId: detailObj.value.buildingId,
    floorId: detailObj.value.floorId,
    unitId: ui.value.erecordUnitId
  }
  emits('showgis', obj)
  // isShowGis.value = true

  // setTimeout(() => {
  //   BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
  //     type: BRI_EVENT_TYPE.SHOW_GIS_WORK_LOCATION,
  //     data: toRaw(detailObj.value),
  //   })
  // }, 3000)
}

function initData(id: string) {
  console.log('详情')
  getData(id)
  getListData(id)
  getSpecialListData(id)
}
// 表格列配置
const normalColumns = ref<TableColumn[]>([
  { type: 'index', label: '序号', width: 60, align: 'center' },
  { prop: 'name', label: '姓名', width: 80 },
  { prop: 'position', label: '岗位', width: 100 },
  { prop: 'unitName', label: '部门' },
  { prop: 'unitName', label: '单位' }
])

const specialColumns = ref<TableColumn[]>([
  { type: 'index', label: '序号', width: 60, align: 'center' },
  { prop: 'name', label: '姓名', width: 80 },
  { prop: 'unitName', label: '部门', width: 100 },
  { prop: 'unitName', label: '单位', width: 100 },
  { prop: 'qualificationName', label: '资格证书名称', width: 140 },
  {
    prop: 'qualificationImage',
    label: '资格证照片',
    width: 100,
    slot: 'image'
  }
])

defineExpose({
  initData
})

defineOptions({ name: 'homeWorkDetailCom' })
</script>

<style scoped lang="scss">
.detail {
  color: black;

  .text {
    margin-left: 5px;

    font-size: 12px;

    p {
      margin-top: 20px;
    }
  }

  .table {
    margin-top: 20px;
  }
}

.w_location {
  margin-left: 20px;
  color: rgb(2, 167, 240);
  cursor: pointer;
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}
</style>
