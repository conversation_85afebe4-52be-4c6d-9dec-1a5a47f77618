<template>
  <div class="htinfo" style="position:relative">
    <div class="title">
      <span></span>
      <el-button type="primary" class="mr-[20px]" @click="add">新增</el-button>
    </div>
    <div v-if="data.length === 0" style="color:#909399;position:absolute;left:47%;top:20%;">暂无数据</div>
    <div class="cardList" v-else>
      <li v-for="item in data" :key="item.id" @mouseleave="setFlag(null)">
    
          <div class="left_name" style="position:relative;">
            <div :title="item.contractName" style="font-size:20px;width:90%; color:#527CFF;font-weight: 600;  white-space: nowrap;   overflow: hidden;  text-overflow: ellipsis;  " >合同名称：{{ item.contractName }}</div>
            <div style="width:88px;position:absolute;cursor:pointer;right:-8%;" >
            <img src="./assets/sheng.png" class="point" @mouseenter="setFlag(item.id)"></img>
           
          </div>
          <div v-if="flag === item.id"  style="position:absolute;cursor:pointer;right:0;">
            <p>
              <el-button @click="handleDel(item)">删除</el-button>
            </p>

            <p>
              <el-button @click="handlerTopEdit(item)">编辑</el-button>
            </p>
          </div>
       
          </div>

          <p >
            合同有效期：{{ item.contractStartTime }} ~
            {{ item.contractEndTime }}
          </p>
          <p>合同附件：</p>
          <div :class="item.attachmentList.length === 1 || item.attachmentList.length === 2 ? 'fujianList2' : 'fujianList'" >
            <div v-for="i in item.attachmentList" :key="i.id" class="fujianList_li" >
              <div class="sp" @click="open(i)" style="" v-if="i.suffix === '.pdf'">
                <img style="" src="./assets/pdf.png" />
              <p :title="i.fileName">{{ i.fileName }}</p>
              </div>
            </div>
          </div>
          <swiper  :modules="[Navigation, A11y]" :slides-per-view="5" :space-between="5"    :pagination="{ clickable: true }"
    navigation>  
    <swiper-slide v-for="(i,index) in item.attachmentList" :key="i.id" >

        <el-image
      v-if="i.suffix !== '.pdf'"
      style="height:118px;width: 105px;"
      :src="config.downloadFileUrl +  i.filePath"

      @click="onShow(item,index)"
    ></el-image>
  
    </swiper-slide>  

  </swiper>  
            
          
        <!-- <div class="right">
         
        </div> -->
      </li>
    </div>
    <EditCom ref="dialogRef" @action="handleFun"></EditCom>
  </div>
  <el-image-viewer
      v-if="showViewer"
      :z-index="9999"
      @close="onClose"
      :zoom-rate="1.2"
      :max-scale="7"
      :min-scale="0.2"
      :initial-index="zindex"
      :url-list="imgList"
    ></el-image-viewer>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import config from '@/config'
import { useRoute } from 'vue-router'
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';

// Import Swiper Vue.js components
import { Swiper, SwiperSlide } from 'swiper/vue';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';

import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import EditCom from './comp/edit.vue'
import $API from '~/common/api'
const dialogRef = ref()
const imgList = ref([])
const flag = ref('')
const route = useRoute()
function handlerTopEdit(obj) {
  dialogRef.value.showDialog({ id: obj.id })
}
const showViewer = ref(false)
const zindex = ref(0)
function onShow(row,index){
  imgList.value = row.attachmentList.map(i => config.downloadFileUrl + i.filePath)
  zindex.value = index
  showViewer.value   = true;
}

function onClose(){
  
  showViewer.value = false;
}

function open(item){
  window.open(config.downloadFileUrl + item.filePath)
}
function handleFun() {
  // getDeatail()
  // $emits('actions')
  getList()
  console.log('发送了事件')
}
const add = () => {
  dialogRef.value.showDialog({})
}
const setFlag = (id) => {
  flag.value = id
}
const data = ref([

])

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/contract/list',
    params: {
      unitId: route.query.id
    }
  })
  // console.log(res, '>>>>>>')
  data.value = res.data
}
async function handleDel(row: any) {
  ElMessageBox({
    title: '提示',
    message: '确认删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        let res: any = await $API.post({
          url: 'edu-inter-server/relatedPartner/contract/delete',
          params: {
            id: row.id
          }
        })
        if (res.code == '200') {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getList()
          done()
        } else {
          ElMessage.error('删除失败')
        }
      } else {
        done()
      }
    }
  })
}
onMounted(() => {
  getList()
})
defineOptions({ name: 'ComHtInfo' })
</script>

<style lang="scss" scoped>
.htinfo {
  width: 98%;
  margin: 0 auto;
 
  height: 100%;
  .title {
    width: 100%;
    display: flex;
    margin-top:20px;
    justify-content: space-between;
    .el-button {
      width: 88px !important;
    }
  }
  .cardList {
    color:#222222;
    width: 100%;
    display: flex;
    list-style: none;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 10px;
    li {
      background-color: white;
      .left_name{
        width: 100%;
        display: flex;
        justify-content: space-between;
      
      }
      .el-button {
        width: 88px !important;
      }
      width: 49.5%;
      height: 285px;
      padding: 15px 5px 5px 15px;
      border: 1px solid rgb(202, 200, 200);
      border-radius:10px;
      
      margin-bottom: 20px;

      // display: flex;
      // justify-content: space-between;
      p {
        margin-top: 12px;
      }
      .fujianList2{
        // margin-top: 20px;
        width: 100%;
        display:flex;
        
        // justify-content: space-around;
        .fujianList_li{
          width:26%;
            margin-right: 69px;
          .sp{
           border: 1px solid #EDEDED;width:100%;height:118px;
            color:#527CFF;
            img{
              width:44px;height:43px;
              margin-left:33%;
              margin-top:12%;
            }
            p{
              width:80%;
              padding-top:5%;
            padding-left:28%;
            border-radius:8px;
            white-space: nowrap; /* 防止文本换行 */  
          overflow: hidden; /* 超出部分隐藏 */  
          text-overflow: ellipsis; /* 显示省略号 */  
            }
           
          }
          margin-top:15px;
          // width:33%;
          // margin-left:30px;
     
        }
        // overflow:hidden;
        // img{
        //   width: 128px;
        //   height: 118px;
        // }
      }
      .fujianList {
        // margin-top: 20px;
        width: 100%;
        display:flex;
        
        justify-content: space-between;
        .fujianList_li{
          width:26%;
            
          .sp{
           border: 1px solid #EDEDED;width:100%;height:118px;
            color:#527CFF;
            img{
              width:44px;height:43px;
              margin-left:33%;
              margin-top:12%;
            }
            p{
              width:80%;
              padding-top:5%;
      padding-left:28%;
            border-radius:8px;
            white-space: nowrap; /* 防止文本换行 */  
          overflow: hidden; /* 超出部分隐藏 */  
          text-overflow: ellipsis; /* 显示省略号 */  
            }
           
          }
          margin-top:15px;
          // width:33%;
          // margin-left:30px;
     
        }
        // overflow:hidden;
        // img{
        //   width: 128px;
        //   height: 118px;
        // }
      }


    }
  }
}

</style>
