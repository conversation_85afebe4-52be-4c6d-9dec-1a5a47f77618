<template>
  <div class="w_personnel_box">
    <div class="w_personnel_head_box">
      <div class="flex pb-[10px]">
        <div :class="['head_tab', { w_active: current == 'BaseInfo' }]" @click="ChangTab('BaseInfo')">基本信息</div>
        <div :class="['head_tab', { w_active: current == 'htinfo' }]" @click="ChangTab('htinfo')">合同记录</div>
      </div>
    </div>
    <div class="w_personnel_tabs_box">
      <el-scrollbar>
        <component :is="tabs[current]" @actions="actionsFetch"></component>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import BaseInfo from './BaseInfo.vue'
import htinfo from './htinfo.vue'
const emits = defineEmits(['actions'])

const tabs = {
  BaseInfo,
  htinfo
}

const current = ref('BaseInfo')

const ChangTab = (val: string) => {
  current.value = val
}
function actionsFetch() {
  console.log('接收了事件11')

  emits('actions')
}
// 创建计划
defineOptions({ name: 'ComEnterPrise' })
</script>

<style scoped lang="scss">
.w_personnel_box {
  height: 100%;
  width: 100%;
  color: #000;

  .w_personnel_head_box {
    //background-color: #0081ff;
    background-color: #dce4f4;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .w_personnel_tabs_box {
    height: 100%;
    width: 100%;

    background-color: #eef7ff;

    //background-color: red;
    //background: #9c9c9c;
    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
