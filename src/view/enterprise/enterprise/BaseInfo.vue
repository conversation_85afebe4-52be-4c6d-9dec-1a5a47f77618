<template>
  <div class="enterprise">
    <div class="top w-full">
      <div class="title">
        <span>基本信息</span>
        <el-button type="primary" class="mr-[20px]" @click="handlerTopEdit" color="#527CFF">编辑</el-button>
      </div>
      <div class="text-[20px] font-bold my-[5px] mx-[10px]">
        {{ info.deptName }}
      </div>
      <div class="cont w-full">
        <div class="onedt">
          <span>统一社会信用代码：</span>
          <span>{{ info.uscc }}</span>
        </div>
        <div class="onedt">
          <span> {{ ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型' }}： </span>
          <span>{{ info.relatedTypeName }}</span>
        </div>
        <div class="onedt">
          <span>企业负责人：</span>
          <span>{{ info.unitManager }}</span>
        </div>
        <div class="onedt">
          <span>企业负责人手机号：</span>
          <span>{{ info.phone }}</span>
        </div>
        <div class="onedt">
          <span>注册资金:</span>
          <span>{{ info.registeredCapital || '--' }}&emsp;（万元）</span>
        </div>
        <div class="onedt">
          <span> {{ ui.zhLogo === 'yanchang' ? '承包商监管人' : '相关方监管人' }}： </span>
          <span>{{ info.relatedManager || '--' }}</span>
        </div>
        <div class="onedt">
          <span>营业执照：</span>
          <span>
            <imgViewList :img-list="info.blFiles || []" />
          </span>
        </div>

        <div class="onedt">
          <span>营业执照有效期：</span>
          <span>{{ info.blExpireTime || '--' }}</span>
        </div>

        <!-- <div class="onedt">
          <span>安全协议：</span>
          <span>
            <imgViewList :img-list="info.spFiles || []" />
          </span>
        </div> -->
        <div class="onedt">
          <span>详细地址：</span>
          <span class="w-[200px]">{{ info.detailAddress || '--' }}</span>
        </div>
      </div>
    </div>
    <div class="bot-table-cont">
      <div class="title">
        <span>
          {{ ui.zhLogo === 'yanchang' ? '承包商资质证书' : '相关方资质证书' }}
        </span>
        <el-button type="primary" @click="handleAdd" class="mr-[20px]" color="#527CFF">+ 新增</el-button>
      </div>
      <div class="table-cont w-full">
        <el-table :data="tableData" :height="tableHeight" style="width: 100%">
          <el-table-column type="index" label="序号" width="60" />

          <el-table-column prop="certificateName" label="证书名称" align="center" show-overflow-tooltip width="190px" />
          <el-table-column prop="issueStartTime" label="证书有效期" width="200" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.issueStartTime || '--' }} - {{ scope.row.issueEndTime || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="certificateFiles" label="证书照片" align="center" show-overflow-tooltip width="120px">
            <template #default="scope">
              <imgViewList :img-list="scope.row.certificateFiles || []" />
            </template>
          </el-table-column>
          <el-table-column prop="isReview" label="需要复审" align="center" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.isReview == 1 ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="lastReviewTime"
            label="最近复审日期"
            align="center"
            show-overflow-tooltip
            width="140px"
          >
            <template #default="scope">
              <span>{{ scope.row.isReview == 1 ? scope.row.lastReviewTime || '--' : '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="nextReviewTime" label="下次复审日期" width="140" align="center" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.isReview == 1">
                <div>{{ scope.row.nextReviewTime || '--' }}</div>
                <div
                  :style="{
                    color:
                      scope.row.status === '正常' ? '#67c23a' : scope.row.status === '即将逾期' ? '#e6a23c' : '#f56c6c',
                    marginTop: '4px'
                  }"
                >
                  ({{ scope.row.status }})
                </div>
              </div>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="createName" label="创建人" align="center" show-overflow-tooltip width="140px" />
          <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip width="190px" />
          <el-table-column prop="status" label="证书状态" align="center" width="100" show-overflow-tooltip>
            <template #default="scope">
              <el-tag
                :type="scope.row.status === '正常' ? 'success' : scope.row.status === '即将逾期' ? 'warning' : 'danger'"
              >
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="earlyWarning" label="预警天数" align="center" width="120" show-overflow-tooltip>
            <template #default="scope">
              <span style="color: #f56c6c">{{ scope.row.earlyWarning || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" plain size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" plain size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="w_server_page_num">
          <el-pagination
            v-model:currentPage="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 编辑新增弹窗 -->
      <el-drawer v-model="dialogVisible" @closed="handleClose">
        <!-- <HeeadTitle :title="ruleForm.id ? '编辑相关方资质证书' : '新增相关方资质证书'"></HeeadTitle> -->
        <template #header>
          <div class="w_add_plan_header">
            <div class="mr-11px"></div>
            <div>
              {{
                ruleForm.id
                  ? ui.zhLogo === 'yanchang'
                    ? '编辑承包商资质证书'
                    : '编辑相关方资质证书'
                  : ui.zhLogo === 'yanchang'
                    ? '新增承包商资质证书'
                    : '新增相关方资质证书'
              }}
            </div>
          </div>
        </template>
        <el-form
          ref="ruleFormRef"
          style="width: 100%"
          :model="ruleForm"
          :rules="rules"
          label-width="auto"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="证书名称:" prop="certificateName">
            <el-input v-model="ruleForm.certificateName" placeholder="请输入证书名称" />
          </el-form-item>
          <el-form-item label="证书有效期起:" prop="issueStartTime">
            <el-date-picker
              v-model="ruleForm.issueStartTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              placeholder="请选择证书有效期起"
            />
          </el-form-item>
          <el-form-item label="证书有效期止:" prop="issueEndTime">
            <el-date-picker
              v-model="ruleForm.issueEndTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              placeholder="请选择证书有效期止"
            />
          </el-form-item>
          <el-form-item label="证书发证日期:" prop="issueTime">
            <el-date-picker
              v-model="ruleForm.issueTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              placeholder="请选择证书发证日期"
            />
          </el-form-item>
          <el-form-item label="证书照片:" prop="certificateFile">
            <uploadImgs @getImgList="getImgUpList" :img-list="ruleForm.certificateFiles || []" />
          </el-form-item>
          <el-form-item label="需要复审:" prop="isReview">
            <el-radio-group v-model="ruleForm.isReview">
              <el-radio value="1">是</el-radio>
              <el-radio value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="最近复审日期:" prop="lastReviewTime"> -->
          <el-form-item label="最近复审日期:" v-if="ruleForm.isReview == '1'">
            <el-date-picker
              v-model="ruleForm.lastReviewTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              placeholder="请选择最近复审日期"
            />
          </el-form-item>
          <el-form-item label="下次复审日期:" prop="nextReviewTime" v-if="ruleForm.isReview == '1'">
            <el-date-picker
              v-model="ruleForm.nextReviewTime"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              placeholder="请选择下次复审日期"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="w_dialog_foot">
            <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
            <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()">保存</el-button>
          </div>
        </template>
      </el-drawer>
    </div>
    <EditCom ref="dialogRef" @action="handleFun" :isEdit="true"></EditCom>
  </div>
</template>
<script setup lang="ts">
import HeeadTitle from '@/components/HT/index.vue'
import imgViewList from '@/components/imgViewList/index.vue'
import { ref, nextTick } from 'vue'
import mixTableHeight from '~/common/tableHeight.js'
import type { FormRules } from 'element-plus'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import uploadImgs from '@/components/uploadImg/index.vue'
import EditCom from '@/view/resumeManagement/comp/edit.vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import config from '@/config'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const info = ref<Record<string, any>>({})
const route = useRoute()
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const ruleFormRef = ref<FormInstance>()
const $emits = defineEmits(['actions'])
const { tableHeight } = mixTableHeight({ subtractHeight: 482 })
const dialogRef = ref()

const tableData = ref([{}])
const ruleForm = ref<Record<string, any>>({
  certificateFile: '',
  certificateFiles: [],
  certificateName: '',
  certificateNo: '',
  institution: '',
  isReview: '1',
  issueEndTime: '',
  issueStartTime: '',
  issueTime: '',
  lastReviewTime: '',
  nextReviewTime: '',
  staffId: '',
  staffName: '',
  type: '1'
})
const rules = ref<FormRules<any>>({
  certificateName: [{ required: true, message: '请输入证书名称', trigger: ['blur', 'change'] }],
  issueStartTime: [
    {
      required: true,
      message: '请选择证书有效期起',
      trigger: ['blur', 'change']
    }
  ],
  issueEndTime: [
    {
      required: true,
      message: '请选择证书有效期止',
      trigger: ['blur', 'change']
    }
  ],
  issueTime: [
    {
      required: true,
      message: '请选择发证日期',
      trigger: ['blur', 'change']
    }
  ],
  // certificateFile: [{ required: true, message: '请选择证书照片', trigger: ['blur', 'change'] }],
  certificateFile: [{ required: true, validator: validateImgUPloadCase, trigger: ['change'] }],
  isReview: [{ required: true, message: '请选择需要复审', trigger: ['blur', 'change'] }],
  lastReviewTime: [
    {
      required: true,
      message: '请选择最近复审日期',
      trigger: ['blur', 'change']
    }
  ],
  nextReviewTime: [
    {
      required: true,
      message: '请选择下次复审日期',
      trigger: ['blur', 'change']
    }
  ]
})

function validateImgUPloadCase(rule: any, value: any, callback: any) {
  if (value) {
    callback()
  } else {
    callback(new Error('请选择证书照片'))
  }
}
// 获取列表
const getData = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/certificate/queryList',
    params: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      sysCode: 'web',
      type: 1,
      staffId: route.query.id
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}
getData()

function handleEdit(row: any) {
  ruleForm.value = { ...row }
  if (ruleForm.value.certificateFiles) {
    ruleForm.value.certificateFiles = ruleForm.value.certificateFiles.map((item: any) => {
      return {
        name: item.fileName,
        url: config.downloadFileUrl + item.filePath,
        id: item.id
      }
    })

    console.log(ruleForm.value.certificateFiles, 'ruleForm.value.certificateFiles')
  }
  dialogVisible.value = true
}
async function handleDel(row: any) {
  ElMessageBox({
    title: '提示',
    message: '确认删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        let res: any = await $API.post({
          url: 'edu-app-server/api/workbench/certificate/delete',
          data: {
            id: row.id
          }
        })
        if (res.code == 'success') {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getData()
          done()
        } else {
          ElMessage.error('删除失败')
        }
      } else {
        done()
      }
    }
  })
}
const dialogVisible = ref(false)

function handleAdd() {
  ruleForm.value = {
    certificateFile: '',
    certificateFiles: [],
    certificateName: '',
    certificateNo: '',
    institution: '',
    isReview: '1',
    issueEndTime: '',
    issueStartTime: '',
    lastReviewTime: '',
    nextReviewTime: '',
    staffId: '',
    staffName: '',
    type: '1'
  }
  dialogVisible.value = true
}
const handleSizeChange = () => {
  pageNo.value = 1
  getData()
}

const handleCurrentChange = () => {
  getData()
}
async function onSubmit() {
  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // ruleForm.value.certificateFile = ruleForm.value.certificateFiles.map((item: any) => item.id).join(',')
      let res: any = await $API.post({
        url: ruleForm.value.id
          ? 'edu-app-server/api/workbench/certificate/edit'
          : 'edu-app-server/api/workbench/certificate/add',
        data: {
          ...ruleForm.value,
          type: '1',
          staffId: route.query.id,
          staffName: info.value.deptName
        }
      })

      if (res.code == 'success') {
        ElMessage({
          message: '操作成功',
          type: 'success'
        })
        getData()
        handleClose()
      } else {
        ElMessage.error('修改失败')
      }
    }
  })
}

const handleClose = () => {
  nextTick(() => {
    ruleFormRef.value?.resetFields()
  })
  dialogVisible.value = false
}
function handlerTopEdit() {
  dialogRef.value.showDialog({ id: route.query.id })
}
function handleFun() {
  getDeatail()
  $emits('actions')
  console.log('发送了事件')
}

// 查询企业基本信息
async function getDeatail() {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/unitInfo',
    params: {
      unitId: route.query.id
    }
  })

  info.value = res.data
}

getDeatail()

function getImgUpList(list: any) {
  ruleForm.value.certificateFiles = list
  nextTick(() => {
    ruleForm.value.certificateFile = ruleForm.value.certificateFiles.map((item: any) => item.id).join(',')
    if (ruleForm.value.certificateFile) {
      ruleFormRef.value?.clearValidate(['certificateFile'])
    }
    console.log(ruleForm.value.certificateFile, 'ruleForm.value.certificateFile')
  })
}

// 创建计划
defineOptions({ name: 'BaseInfoCom' })
</script>

<style scoped lang="scss">
.enterprise {
  @apply flex w-full flex-col;

  .top {
    @apply w-full bg-[#fff] min-h-[250px] p-[10px];
    justify-content: space-between;
  }

  .title {
    @apply flex w-full justify-between items-center border-b-[1px] border-[#f2f4fa] p-[9px];

    span {
      @apply text-[#333] text-[16px] font-bold;
    }

    .el-button {
      @apply bg-[#409eff] text-[#fff] text-[14px] font-bold;
    }
  }

  .cont {
    @apply flex flex-wrap p-[10px];

    .onedt {
      @apply w-[25%] flex my-[6px];

      span {
        display: inline-block;
        padding-right: 10px;

        // &:nth-of-type(2){
        //   width: 60%;
        // }
      }
    }
  }

  .bot-table-cont {
    @apply w-full bg-[#fff] p-[10px];

    .table-cont {
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 28px;
      grid-row-gap: 20px;
    }
  }
}

.w_add_plan_header {
  font-weight: 700;
  color: #333;
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: start;
  align-items: center;

  div:first-of-type {
    /* 样式规则 */
    width: 18px;
    height: 12px;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    background-size: 100% 100%;
  }
}

.span_cursor {
  color: #527cff;
  cursor: pointer;
}

.demo-ruleForm {
  @apply mt-[10px] p-[10px];
}

.w_server_page_num {
  display: flex;
  justify-content: end;
  padding-right: 50px;
  padding-top: 20px;
  //background-color: orange;
}

:deep(.el-table__header .el-table__cell) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}

:deep(.el-table__header-wrapper tr th.el-table-fixed-column--right) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding-top: 0;
  border-bottom: 1px solid rgb(235, 238, 245);
}

:deep(.el-drawer__body) {
  margin-top: -1·0px;
}
</style>
