<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" @closed="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>{{ formInline.id ? '编辑合同' : '新增合同' }}</div>
        </div>
      </template>
      <div class="w_plan_con_box">
        <div class="w_content_box">
          <div class="mb-3 w_dialog_from_box">
            <el-form :model="formInline" class="" ref="ruleFormRef" :rules="rules" label-width="150px">
              <el-form-item label="合同名称：" prop="contractName">
                <el-input
                  v-model="formInline.contractName"
                  placeholder="请输入合同名称"
                  maxlength="30"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="合同有效期：" prop="timeRange">
                <el-date-picker
                  v-model="formInline.timeRange"
                  placeholder="请选择合同证有效期"
                  :size="size"
                  style="width: 100%"
                  type="daterange"
                  clearable
                  value-format="YYYY-MM-DD"
                  range-separator="~"
                  start-placeholder="请选择开始日期"
                  end-placeholder="请选择结束日期"
                />
              </el-form-item>
              <div class="upload_file">
                <el-form-item label="合同附件：" prop="fileName">
                  <el-input v-model="formInline.fileName" v-show="false"></el-input>
                  <el-upload
                    :accept="fileType"
                    class="avatar-uploader"
                    :limit="fileType === '.pdf' ? 3 : 9"
                    v-model:file-list="file"
                    :on-exceed="handleExceed"
                    :on-success="handleSuccess"
                    :on-remove="handleRemove"
                    :before-upload="beforeUpload"
                    name="uploadfile"
                    :list-type="fileType === '.png,.jpeg,.jpg' ? 'picture-card ' : ''"
                    :action="url"
                  >
                    <el-icon v-if="fileType === '.png,.jpeg,.jpg'"><Plus /></el-icon>

                    <div>
                      <div class="w_file_btn" v-show="fileType === '.pdf' || fileType === '.jpeg,.pdf,.png,.jpg'">
                        + 上传附件
                      </div>
                      <div class="w_file_t">
                        <!-- 仅限PDF格式，最多上传三个，且每个文件小于10M -->
                      </div>
                    </div>
                    <template #file="{ file }" v-if="fileType !== '.pdf'">
                      <div>
                        <img
                          class="el-upload-list__item-thumbnail"
                          :src="
                            file &&
                            file.response &&
                            file.response.data &&
                            config.downloadFileUrl + file.response.data.filePath
                          "
                          alt=""
                        />
                        <span class="el-upload-list__item-actions">
                          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                            <el-icon><zoom-in /></el-icon>
                          </span>
                          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
                          </span>
                          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove2(file)">
                            <el-icon><Delete /></el-icon>
                          </span>
                        </span>
                      </div>
                    </template>
                  </el-upload>
                  <el-dialog v-model="dialogVisible">
                    <img w-full :src="dialogImageUrl" alt="Preview Image" />
                  </el-dialog>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { ElMessage } from 'element-plus'
import config from '@/config'
import UploadImg from '@/components/uploadImg/index.vue'
import { Delete, Download, Plus, ZoomIn } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'

const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const disabled = ref(false)

const handleRemove2 = (filee: UploadFile) => {
  console.log(file.value, '>>>>>>>>')
  console.log(filee, '<<<<<<<<<<<<<<')
  file.value = file.value.filter((item) => {
    return item.response.data.id !== filee.response.data.id
  })
  if (file.value.length == 0) {
    fileType.value = '.jpeg,.pdf,.png,.jpg'
  }
}

const handlePictureCardPreview = (file: UploadFile) => {
  console.log(file, 'file')
  dialogImageUrl.value = config.downloadFileUrl + file.response.data.filePath
  dialogVisible.value = true
}

const handleDownload = (file: UploadFile) => {
  console.log(file)
}
const route = useRoute()
// const timeRange = ref<any>([])
const url = ref(config.update_file + '/file/uploadfile') // 上传路径
const emits = defineEmits(['action', 'submit'])

const drawer = ref(false)
const options = ref<Record<string, any>[]>([])
// 试卷列表
// 日期
// const dataValue = ref('')
// const url = ref(config.update_file + '/file/uploadfile')

// 选中人员数据

const ruleFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  contractName: [
    {
      required: true,
      message: '请输入合同名称',
      trigger: ['blur', 'change']
    }
  ],
  timeRange: [{ required: true, message: '请选择合同有效期', trigger: ['blur', 'change'] }],
  fileName: [
    {
      required: true,
      message: '请上传合同附件',
      trigger: 'change'
    }
  ]
})
//超出文件
const handleExceed = () => {
  if (fileType.value === '.pdf') {
    ElMessage.error('最多只能上传三个')
  } else {
    ElMessage.error('最多只能上传九张')
  }
}
const fileType = ref('.jpeg,.pdf,.png,.jpg')
const allowedTypes = ref(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
const beforeUpload = (rawFile) => {
  // console.log(rawFile, '>>>>>>>>>.')
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const allowedTypes2 = ['application/pdf']
  if (
    fileType.value === '.jpeg,.pdf,.png,.jpg' &&
    rawFile.type !== 'application/pdf' &&
    rawFile.type !== 'image/jpeg' &&
    rawFile.type !== 'image/png' &&
    rawFile.type !== 'image/jpg'
  ) {
    // fileType.value = '.pdf'
    ElMessage.error('请上传PDF文件或者JPEG、PNG格式图片!')
    return false
  } else if (fileType.value === '.pdf' && rawFile.type !== 'application/pdf') {
    ElMessage.error('请上传PDF文件!')
    return false
  } else if (fileType.value === '.png,.jpeg,.jpg' && !allowedTypes.includes(rawFile.type)) {
    ElMessage.error('请上传PNG、JPEG、JPG格式图片!')
    return false
  } else if (allowedTypes.includes(rawFile.type) && rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('图片不能大于10MB!')
    return false
  } else if (allowedTypes2.includes(rawFile.type) && rawFile.size / 1024 / 1024 > 10) {
    ElMessage.error('文件不能大于10MB!')
    return false
  } else {
    return true
  }
}
// 文件上传列表
const file = ref<any>([])
const size = ref<'' | 'large' | 'small'>('')

const formInline = ref<any>({
  contractName: '',
  contractStartTime: null,
  contractEndTime: null,
  timeRange: [],
  fileName: ''
})

const handleClose = () => {
  drawer.value = false

  ruleFormRef.value?.resetFields()
}
const handleRemove = (filee, uploadFiles) => {
  // // console.log(filee)
  // file.value = file.value.filter((item) => {
  //   return item.id !== filee.response.data.id
  // })
  // console.log(file.value)
  if (file.value.length == 0) {
    fileType.value = '.jpeg,.pdf,.png,.jpg'
  }
}

const fileList: any = ref([])
async function getDeatail(id: string) {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/contract/detail',
    params: {
      id: id
    }
  })
  formInline.value = { ...res.data }
  formInline.value.timeRange = [res.data.contractStartTime, res.data.contractEndTime]

  res.data.attachmentList.forEach((item) => {
    fileType.value = item.suffix
    if (fileType.value !== '.pdf') {
      fileType.value = '.png,.jpeg,.jpg'
    }
    file.value.push({
      name: item.fileName,
      response: {
        data: {
          ...item
        }
      }
    })
  })
}
const showDialog = (row: any) => {
  fileType.value = '.jpeg,.pdf,.png,.jpg'
  formInline.value.fileName = ''
  file.value = []
  fileList.value = []
  // editId.value = row.id
  if (row.id) {
    formInline.value.id = row.id
    getDeatail(row.id)
  } else {
    formInline.value = {}
  }
  drawer.value = true
}
async function onSubmit() {
  file.value.forEach((item) => {
    fileList.value.push(item.response.data)
  })

  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (fileList.value.length === 0) {
        ElMessage.error('合同不能为空！')
        return
      }
      formInline.value.contractStartTime = formInline.value.timeRange[0]
      formInline.value.contractEndTime = formInline.value.timeRange[1]
      formInline.value.source = 1
      // delete formInline.value.timeRange

      // formInline.value.blFileId = YYZZList.value
      //   .map((item: any) => item.id)
      //   .join(',')
      // formInline.value.spFileId = AQXYList.value
      //   .map((item: any) => item.id)
      //   .join(',')
      if (!formInline.value.id) {
        let res: any = await $API.post({
          url: 'edu-inter-server/relatedPartner/contract/addContract',
          data: {
            ...formInline.value,
            file: fileList.value,
            unitId: route.query.id
          }
        })
        if (res.code == 200) {
          ElMessage({
            message: '新增成功',
            type: 'success'
          })
          handleClose()
          emits('action')
        } else {
          return
        }
      } else {
        // file.value = file.value.filter(
        //   (item1) => !fileList.value.some((item2) => item2.id === item1.id)
        // )
        // delete formInline.value.attachmentList
        let res: any = await $API.post({
          url: 'edu-inter-server/relatedPartner/contract/updateContract',
          data: {
            ...formInline.value,
            file: fileList.value
          }
        })
        if (res.code == 200) {
          ElMessage({
            message: '编辑成功',
            type: 'success'
          })
          handleClose()
          emits('action')
        } else {
          return
        }
      }
    }
  })
}
watch(
  () => file.value.length,
  (val) => {
    if (val > 0) {
      formInline.value.fileName = val + ''
    } else {
      formInline.value.fileName = ''
    }
  },
  { immediate: true }
)
// watch()
// () => formInline.value.relatedType,
// (newVal: any) => {
//   formInline.value.relatedTypeName = options.value?.find(
//     (item: any) => item.id == newVal
//   )?.name
// },
// {
//   deep: true
// }
// watch(
//   () => file.value.length,
//   (val) => {
//     if (val > 0) {
//       formInline.value.fileName = val + ''
//     } else {
//       formInline.value.fileName = ''
//     }
//   },
//   { immediate: true }
// )
const handleSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  // file.value.push({
  //   name: response.data.fileName,
  //   resoponse: {
  //     data: {
  //       id: response.data.fileName,
  //       filePath: response.data.filePath
  //     }
  //   }
  // })
  fileType.value = response.data.suffix
  if (fileType.value !== '.pdf') {
    fileType.value = '.png,.jpeg,.jpg'
  }
  ElMessage({
    message: '上传文件成功',
    type: 'success'
  })
}
defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.upload_file {
  :deep(.el-form-item__label::before) {
    color: #f56c6c;
    content: '*';
    margin-right: 4px;
  }
}

.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  .w_add_plan_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_plan_con_box {
    font-size: 0.875rem;
    //background: yellowgreen;
    display: grid;
    // height: 790px;
    // grid-template-rows: 790px;
    grid-template-columns: 1fr;
    color: rgba(72, 74, 77, 1);

    .bg_text {
      color: rgba(48, 49, 51, 1);
      font-size: 16px;
    }

    .w_content_box {
      //background: red;
      width: 100%;
      height: 100%;

      .w_plan_con_one {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .div_con20 {
          height: 20px;
          line-height: 20px;
          display: flex;

          .w_flex-1 {
            flex: 1;
          }

          .w_flex-2 {
            flex: 2;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .w_dialog_from_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .w_file_btn {
          width: 136px;
          height: 32px;

          border: 1px solid #527cff;
          text-align: center;
          color: #527cff;
          cursor: pointer;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
        }

        //background: red;
        .w_file_t {
          color: #a8abb2;
        }
      }

      .w_table_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 10px 0 16px;
        margin-bottom: 22px;

        .w_dialog_from_title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
        }

        .w_add_bor {
          width: 100px;
          border: 1px solid #527cff;
          color: #527cff;
          text-align: center;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
          cursor: pointer;
        }

        .w_span_cursor {
          color: #527cff;
          cursor: pointer;
        }

        :deep(.el-table__header .el-table__cell) {
          background-color: rgba(245, 246, 249, 1);
          /* 表头背景色 */
          color: #606266;
          /* 表头字体颜色 */
          font-size: 14px;
          /* 表头字体大小 */
          height: 48px;
        }
      }

      .w_page_box {
        margin-top: 16px;
        height: 32px;
        display: flex;
        justify-content: end;
        padding-right: 24px;
      }
    }
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;

    //background-color: red;
    //padding-right: 20px;
    .w_btn {
      height: 32px;
    }

    .w_btn_bg {
      background-color: rgba(82, 124, 255, 1) !important;
    }
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-upload-list__item-file-name) {
  width: 250px !important;
  text-align: left;
}
:deep(.el-upload-list__item-thumbnail) {
  position: absolute;
}
:deep(.el-upload--picture-card) {
  margin-bottom: 10px;
}
</style>
