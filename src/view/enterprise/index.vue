<template>
  <div class="w_enterprise_box" style="position: relative">
    <el-scrollbar>
      <el-button
        type="primary"
        style="position: absolute; right: 20px; top: 20px"
        @click="exit"
        v-if="ui.orgRes === '2'"
        >退出</el-button
      >
      <div class="w_enterprise_head bg-title">
        <div class="w_enterprise_head_bg">
          <div class="w_enterprise_head_title">{{ info.deptName }}</div>
          <div class="yyzz-cont">
            <imgViewList :img-list="info.blFiles || []" height="100px" width="auto" />
            <div v-if="info.blFiles && info.blFiles?.length > 0">营业执照</div>
          </div>
        </div>

        <div class="w_enterprise_head_cont">
          <div>企业系统账号：{{ info.systemAccount }}</div>
          <div>{{ ui.zhLogo === 'yanchang' ? '承包商类型' : '相关方类型' }}：{{ info.relatedTypeName }}</div>
        </div>
      </div>

      <div class="w_enterprise_tab_box">
        <div :class="{ w_active: current == 'safeScore' }" @click="ChangTab('safeScore')">安全积分</div>
        <div :class="{ w_active: current == 'resumeCom' }" @click="ChangTab('resumeCom')">履历总览</div>
        <div :class="{ w_active: current == 'enterpriseCom' }" @click="ChangTab('enterpriseCom')">企业信息</div>
        <div v-if="!route.query.projId" :class="{ w_active: current == 'projectMgr' }" @click="ChangTab('projectMgr')">
          项目管理
        </div>
        <div :class="{ w_active: current == 'homeworkRecord' }" @click="ChangTab('homeworkRecord')">作业记录</div>
        <div :class="{ w_active: current == 'personnelCom' }" @click="ChangTab('personnelCom')">人员管理</div>
        <div :class="{ w_active: current == 'accountabilityCom' }" @click="ChangTab('accountabilityCom')">
          责任制签署
        </div>
        <div :class="{ w_active: current == 'regulationCom' }" @click="ChangTab('regulationCom')">制度规程</div>
        <div :class="{ w_active: current == 'trainCom' }" @click="ChangTab('trainCom')">安全培训</div>
        <div :class="{ w_active: current == 'planCom' }" @click="ChangTab('planCom')">应急预案</div>
        <!-- <div :class="{ w_active: current == 'violationCom' }" @click="ChangTab('violationCom')">违规记录</div> -->
      </div>
      <div class="w_enterprise_tabs_content">
        <el-scrollbar>
          <component :is="tabs[current]" @actions="getDeatail"></component>
        </el-scrollbar>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { provide, ref, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import accountabilityCom from './accountability/index.vue'
import enterpriseCom from './enterprise/index.vue'
import homeworkRecord from './homeworkRecord/index.vue'
import personnelCom from './personnel/index.vue'
import planCom from './plan/index.vue'
import projectMgr from './projectMgr/index.vue'
import regulationCom from './regulation/index.vue'
import resumeCom from './resume/index.vue'
import safeScore from './safeScore/index.vue'
import trainCom from './trainCom/index.vue'
import violationCom from './violation/index.vue'

const ui = useUserInfo()
const route = useRoute()
const router = useRouter()
const tabs = {
  safeScore,
  resumeCom,
  enterpriseCom,
  personnelCom,
  accountabilityCom,
  regulationCom,
  trainCom,
  planCom,
  violationCom,
  homeworkRecord,
  projectMgr
}
const current = ref('safeScore')
const info = ref<Record<string, any>>({})
provide('unitName', info)

const ChangTab = (val: string) => {
  if (val == 'violationCom') {
    ElMessage({
      message: '正在研发中，待上线',
      type: 'warning'
    })
    return
  }
  current.value = val
  if (sessionStorage.getItem('tabNum')) sessionStorage.removeItem('tabNum')
}

async function getDeatail() {
  let res: any = await $API.get({
    url: 'edu-inter-server/relatedPartner/unitInfo',
    params: {
      unitId: route.query.id
    }
  })

  info.value = res.data
}
getDeatail()
function exit() {
  // console.log('退出成功')
  // const val = ui.value
  ElMessageBox({
    title: '提示',
    message: h('span', null, '确认退出登录吗?'),
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = '请稍候...'
        $API
          .post({
            method: 'post',
            url: 'train-server/login/logOut',
            params: {
              client: 'WEB',
              orgCode: ui.value.orgCode,
              userId: ui.value.id,
              sysCode: ui.value.sysCode,
              userToken: ui.value.token
            }
          })
          .then((res: any) => {
            instance.confirmButtonLoading = false
            instance.confirmButtonText = '确定'
            if (res && res.code === 'success') {
              // ui.value = undefined as any
              // ElMessage.success('退出成功')
              sessionStorage.removeItem('@@web_userInfo')
              localStorage.removeItem('@@web_userInfo')
              sessionStorage.removeItem('@@web_userInfo_partner')
              localStorage.removeItem('@@web_userInfo_partner')
              done()
              window.location.replace(ui.value.zhPlatformUrl.replace('/staging', '/login'))
            }
          })
      } else {
        done()
      }
    }
  })
}

watch(
  () => route.query,
  (newV: any) => {
    current.value = newV.tab || 'safeScore'
  },
  { immediate: true, deep: true }
)
watch(
  () => ui.value,
  (newV: any) => {
    if (!Object.keys(newV).length) {
      ui.value = JSON.parse(localStorage.getItem('@@web_userInfo_partner') || '{}').userInfo
    }
  }
)
// const addDialog = () => {
//   console.log('xinzeng')
// }
// 创建计划
defineOptions({ name: 'planDetailIndex' })
</script>

<style scoped lang="scss">
.w_enterprise_box {
  // display: grid;
  width: 100%;
  height: 100%;
  background: url('./assets/<EMAIL>') no-repeat center center;
  background-size: cover;

  // grid-template-columns: 1fr;
  // grid-template-rows: 174px 56px 1fr;
  // overflow-y: auto;
  //grid-row-gap: 20px;
  .crumbs_box {
    //background-color: yellowgreen;
    background-color: white;
  }

  .bg-title {
    background: url('./assets/<EMAIL>') no-repeat center center;
    background-size: cover;
    /* 确保图片覆盖整个元素 */
  }

  .w_enterprise_head {
    .w_enterprise_head_bg {
      position: relative;
      text-align: left;
      height: 107px;

      width: 70%;
      color: #fff;
      margin: 0 auto;
      // background-image: url('./assets/bg.png');
      // background-size: cover; /* 确保图片覆盖整个元素 */
      background-position: center;
      /* 将图片居中显示 */
      display: flex;
      justify-content: space-between;
      align-items: center;

      .w_enterprise_head_title {
        height: 53px;
        font-weight: 700;
        font-size: 38px;
        color: #fff;
        line-height: 53px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .w_enterprise_head_cont {
      display: flex;
      height: 48px;
      width: 70%;
      margin: 0 auto;
      color: #fff;
      // justify-content: space-between;
      align-items: center;

      // border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
      > div {
        text-align: left;
        margin-right: 5%;
        margin-bottom: 18px;
      }

      background-color: transparent;
    }
  }

  .w_enterprise_tab_box {
    margin: 0 auto;
    width: 70%;
    margin-top: 8px;
    background-color: #ffffff;
    height: 56px;
    display: flex;
    justify-content: space-between;
    //margin-bottom: 20px;
    align-items: center;
    cursor: pointer;

    > div {
      line-height: 56px;
      left: 56px;
      flex: 1;
      font-weight: 400;
      font-size: 16px;
      color: rgba(77, 77, 77, 1);
      text-align: center;
    }

    .head_tab {
      //line-height: 54px;
      ////background: red;
      //margin-right: 42px;
      //color: #484a4d;
      &:hover {
        color: rgba(255, 255, 255, 1);
        background: linear-gradient(180deg, #527cff 0%, #2e5ced 100%);
      }
    }

    .w_active {
      color: rgba(255, 255, 255, 1);
      background: linear-gradient(180deg, #527cff 0%, #2e5ced 100%);
    }
  }

  .w_enterprise_tabs_content {
    height: calc(100% - 20px);
    width: 70%;
    margin: 0 auto;
    display: grid;
    margin-top: 20px;

    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    .bg-conts {
      width: 70%;
      margin: 0 auto;
    }
  }

  :deep(.el-scrollbar__view) {
    height: 100%;
  }

  .yyzz-cont {
    @apply w-[auto] flex justify-center bg-[#7eb2ff] flex flex-col justify-center items-center;
    position: absolute;
    font-size: 12px;
    right: 0%;
    top: 20px;
    // height: 127px;
    border-radius: 0 0 5px 5px;
  }
}
</style>
