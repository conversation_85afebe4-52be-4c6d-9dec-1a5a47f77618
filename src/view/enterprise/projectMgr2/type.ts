export interface IProject {
  projectId: string
  orgCode: string
  projectNo: string
  projectName: string
  responsibleUserName: string
  responsibleUserContactNo: string
  startDate: string
  endDate: string
  projectStatus: string
  projectStatusName: string
  zhId: string
  orgName: string
  relateOrgName: string
  relateOrgCode: string
  enterDate: string
  leaveDate: string
  personNum: number
  sceneUserName: string
  sceneUserContactNo: string
}

export interface IPersonLi {
  constructScheme: string
  emergencyPlan: string
  personNum: number
  projectId: string
  relateOrgCode: string
  relateOrgName: string
  responsibleUserContactNo: string
  responsibleUserName: string
  sceneUserContactNo: string
  sceneUserName: string
  securityProtocol: string
  userType: string
  userId: string
}
