<template>
  <div class="project-mgr-wrap">
    <div class="static-content">
      <div class="static-item status_0">
        <span>待开始项目</span>
        <p>{{ staticData.toStartProjectNum || 0 }}</p>
      </div>
      <div class="static-item status_1">
        <span>进行中项目</span>
        <p>{{ staticData.processingProjectNum || 0 }}</p>
      </div>
      <div class="static-item status_2">
        <span>已结束项目</span>
        <p>{{ staticData.endProjectNum || 0 }}</p>
      </div>
      <div class="static-item status_3">
        <span>已作废项目</span>
        <p>{{ staticData.cancelProjectNum || 0 }}</p>
      </div>
    </div>
    <!-- 搜索 -->
    <Filter @action="actionFn" />

    <TableComp ref="tableCompRef" @action="actionFn" />
    <!--详情-->
    <DetailComp v-if="showDetail" v-model="showDetail" />
    <!-- 配置 -->
    <popup-side v-model="checkVisible" width="80%" popupTitle="管理" class="add-person-dialog">
      <ModifyComp @action="actionFn" />
    </popup-side>
    <popup-side v-model="checkVisible2" width="80%" popupTitle="管理" class="add-person-dialog">
      <Modify2Comp @action="actionFn" />
    </popup-side>
  </div>
</template>

<script setup lang="ts">
import { onMounted, provide, Ref, ref } from 'vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import ModifyComp from '~/view/enterprise/projectMgr/comp/Modify.vue'
import Modify2Comp from '~/view/enterprise/projectMgr/comp/Modify2.vue'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/enterprise/projectMgr/constant'
import DetailComp from './comp/Detail.vue'
import Filter from './comp/Filter.vue'
import TableComp from './comp/Table.vue'

const route = useRoute()
const ui = useUserInfo()

const tableCompRef = ref()

const checkVisible = ref(false)
const checkVisible2 = ref(false)
const showDetail = ref(false)

const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} })
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction)

function actionFn(data: IActionData) {
  // 查询
  if (data.action === ACTION.SEARCH) {
    return tableCompRef.value?.getTableWrap(data.data)
  }

  currentAction.value = data
  // 详情
  if (data.action === ACTION.DETAIL) {
    console.log(data.data)
    return (showDetail.value = true)
  }
  // 配置
  if (data.action === ACTION.CONF) {
    return (checkVisible.value = true)
  }
  if (data.action === ACTION.CONF2) {
    return (checkVisible2.value = true)
  }
  // 保存
  if (data.action === ACTION.SAVE) {
    tableCompRef.value?.getTableWrap({})
    return (checkVisible.value = false)
  }
  // 关闭
  if (data.action === ACTION.CLOSE) {
    return (checkVisible.value = false)
  }
  // 关闭
  if (data.action === ACTION.CLOSE2) {
    return (checkVisible2.value = false)
  }
}

const staticData = ref<Record<string, any>>({})
async function getStatics() {
  const url = '/atomic-upms-service/relate/project/statisticsProjectCount'
  const res: any = await $API.get({
    url,
    params: {
      orgCode: route.query.orgCode || ui.value.serverUnitId,
      relateOrgCode: route.query.id || ui.value.relateOrgCode
    }
  })
  if (res.code === 200) {
    staticData.value = res.data
  }
}

onMounted(async () => await getStatics())

defineOptions({ name: 'projectMgrComp' })
</script>
<style scoped lang="scss">
.project-mgr-wrap {
  height: 100%;
  padding: 20px;
  background-color: #fff;
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: 20px;

  .static-content {
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .static-item {
      width: 218px;
      height: 80px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20px 0;
      background: #fff;
      padding-left: 86px;

      span {
        color: #333333;
      }

      p {
        font-size: 20px;
      }
    }

    .status_0 {
      background: url('./assets/status_0.png') center / 100% no-repeat;

      p {
        color: #00cfe8;
      }
    }
    .status_1 {
      background: url('./assets/status_1.png') center / 100% no-repeat;

      p {
        color: #527cff;
      }
    }
    .status_2 {
      background: url('./assets/status_2.png') center / 100% no-repeat;

      p {
        color: #409d13;
      }
    }
    .status_3 {
      background: url('./assets/status_3.png') center / 100% no-repeat;

      p {
        color: #fd8410;
      }
    }
  }

  .project-table {
    background: deeppink;
  }

  :deep(.el-drawer__body) {
    padding-top: 0;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 15px;
  }

  :deep(.add-person-dialog) {
    .el-dialog__header {
      padding-right: 0 !important;
      padding: 0 !important;
      border-bottom: 0 !important;
    }
  }
}
</style>
