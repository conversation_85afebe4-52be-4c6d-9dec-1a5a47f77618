<template>
  <div class="relative pb-[20px]">
    <div class="cus-card">
      <p class="ml-[20px]">项目管理：</p>
      <div class="custom-content">
        <el-row style="width: 100%">
          <el-col :span="12">
            <span>项目名称：</span>
            <span style="flex: 1">{{ detailData.projectName }}</span>
          </el-col>
          <el-col :span="12">
            <span>项目负责人：</span>
            <span>{{ detailData.responsibleUserName }}</span>
          </el-col>
        </el-row>
        <el-row style="width: 100%">
          <el-col :span="12">
            <span>项目起止日期：</span>
            <span>{{ detailData.startDate }} ~ {{ detailData.endDate }}</span>
          </el-col>
          <el-col :span="12">
            <span>项目状态：</span>
            <span>{{ detailData.projectStatusName }}</span>
          </el-col>
        </el-row>
        <el-row>
          <div class="flex-box">
            <span>项目描述：</span>
            <span>{{ detailData.projectDesc || '--' }}</span>
          </div>
        </el-row>
        <el-row>
          <div class="flex-box">
            <span>施工方案：</span>
            <div class="file-list" v-if="detailData.constructSchemeList?.length">
              <span
                class="attachment-item"
                v-for="item in detailData.constructSchemeList"
                :key="item.id"
                @click="handleFileClick(item.filePath)"
              >
                {{ item.fileName }}
              </span>
            </div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-box">
            <span>应急预案：</span>
            <div class="file-list" v-if="detailData.emergencyPlanList?.length">
              <span
                class="attachment-item"
                v-for="item in detailData.emergencyPlanList"
                :key="item.id"
                @click="handleFileClick(item.filePath)"
              >
                {{ item.fileName }}
              </span>
            </div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-box">
            <span>安全协议：</span>
            <div class="file-list" v-if="detailData.securityProtocolList?.length">
              <span
                class="attachment-item"
                v-for="item in detailData.securityProtocolList"
                :key="item.id"
                @click="handleFileClick(item.filePath)"
              >
                {{ item.fileName }}
              </span>
            </div>
          </div>
        </el-row>
      </div>
    </div>
    <div class="cus-card">
      <p class="ml-[20px]">人员管理：</p>
      <div class="custom-content">
        <div class="search-box flex items-center justify-between">
          <el-form :model="searchForm" inline>
            <!-- <el-form-item label="员工姓名">
              <el-input v-model="searchForm.name" placeholder="请输入员工姓名" clearable @input="debouncedSearch" />
            </el-form-item> -->
          </el-form>
          <el-button type="primary" @click="addPerson" class="bg-[#2468f2]" style="margin-bottom: 15px"
            >新增人员</el-button
          >
        </div>
        <el-scrollbar height="380px">
          <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
            <el-table-column type="index" width="60" label="序号" align="center" />
            <el-table-column prop="name" label="姓名" align="center" width="100" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.name || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" align="center" width="140" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.phone || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" align="center" width="140" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.account || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="depart" label="部门" align="center" width="140" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.depart || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="post" label="岗位" align="center" width="140" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.post || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="insureFiles"
              label="投保信息（工伤）"
              align="center"
              width="100"
              show-overflow-tooltip
            >
              <template #default="scope">
                <imgViewList :img-list="scope.row.insureFiles || []" />
              </template>
            </el-table-column>
            <el-table-column prop="idFiles" label="身份证照片" align="center" width="160" show-overflow-tooltip>
              <template #default="scope">
                <imgViewList :img-list="scope.row.idFiles || []" />
              </template>
            </el-table-column>
            <el-table-column
              prop="documentFiles"
              label="证件照（免冠照）"
              align="center"
              width="160"
              show-overflow-tooltip
            >
              <template #default="scope">
                <imgViewList :img-list="scope.row.documentFiles || []" />
              </template>
            </el-table-column>
            <el-table-column prop="idNumber" label="身份证号" align="center" width="180" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.idNumber || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="idPeriod" label="身份证有效期" align="center" width="200" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.idPeriod || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="age" label="年龄（岁）" align="center" width="100" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.age || '--' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="specialOperation"
              label="否是特种作业人员"
              align="center"
              width="140"
              show-overflow-tooltip
            >
              <template #default="scope">
                <span v-if="scope.row.specialOperation == 1">是</span>
                <span v-else-if="scope.row.specialOperation == 0">否</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="createName" label="创建人" align="center" width="100" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.createName || '--' }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="200" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.createTime || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="260" fixed="right" align="center">
              <template #default="scope">
                <div class="flex items-center">
                  <div class="mr-4 flex items-center">
                    <span class="text-[#2468f2] mr-2">设为项目现场负责人</span>
                    <el-popconfirm
                      :title="scope.row.userType === '1' ? '确定设为项目现场负责人吗?' : '确定取消项目现场负责人吗?'"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      @confirm="confirmSetLeader(scope.row)"
                      width="260"
                      :icon="QuestionFilled"
                      icon-color="#f56c6c"
                    >
                      <template #reference>
                        <!-- <el-switch
                          v-model="scope.row.userType"
                          inactive-color="#DCDFE6"
                          active-color="#2468f2"
                          inline-prompt
                          active-text="开"
                          inactive-text="关"
                          active-value="2"
                          inactive-value="1"
                          @change="preventSwitchChange($event, scope.row)"
                        /> -->
                      </template>
                    </el-popconfirm>
                  </div>
                  <div>
                    <el-button type="danger" @click="showDelDialog(scope.row)">删除</el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
        <div class="w_page_num">
          <el-pagination
            class="page_r"
            v-model:currentPage="pageNo"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- <div class="cus-card flex justify-end pr-[20px]">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save" :loading="isUploading">确定</el-button>
    </div> -->
  </div>
  <del-dialog-com ref="delRef" @action="handleDel" title="删除人员信息"></del-dialog-com>
  <!-- <PersonInfo ref="dialogRef" @action="handleFun" /> -->
  <AddPerson
    v-model:addPersonVisible="addPersonVisible"
    :selected-persons="selectedPersons"
    @update="handleAddPersons"
  />
</template>

<script setup lang="ts">
import { CircleClose, QuestionFilled } from '@element-plus/icons-vue'
import { computed, inject, onMounted, ref, Ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import config from '~/config'
import PersonInfo from './personInfo.vue'
import { useRoute, useRouter } from 'vue-router'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/enterprise/projectMgr/constant'
import delDialogCom from './delDialog.vue'
import { IPersonLi } from '~/view/enterprise/projectMgr/type'
import { ElMessage } from 'element-plus'
import AddPerson from './addPerson.vue'
import { useUserInfo } from '~/store'
const { tableHeight } = mixTableHeight({ subtractHeight: 465 })
const route = useRoute()
const router = useRouter()
const visible = ref(false)
const formData = ref<Record<string, any>>({
  cPlanFile: '',
  ePlanFile: '',
  protocolFile: '',
  cPlanFileIds: '',
  ePlanFileIds: '',
  protocolFileIds: ''
})

const addPersonVisible = ref(false)
const cPlanLi = ref<any[]>([])
const ePlanLi = ref<any[]>([])
const protocolLi = ref<any[]>([])
const isUploading = ref(false)
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>
const emits = defineEmits(['action'])
// 人员管理相关
const searchForm = ref({
  name: '',
  phone: ''
})
const handleFun = () => {
  getList()
}

const handleAddPersons = (persons) => {
  const previousLeaderId = personList.value.find((person) => person.userType === '2')?.userId
  selectedPersons.value = persons
  personList.value = []
  originalPersonList.value = []
  persons.forEach((person) => {
    personList.value.push({
      ...person,
      userType: person.userId === previousLeaderId ? '2' : '1'
    })
    originalPersonList.value = [...personList.value]
  })
}

const addPerson = () => {
  addPersonVisible.value = true
}

const loading = ref(false)
const personList = ref<Partial<IPersonLi>[]>([])
const detailData = ref<Record<string, any>>({})
const originalPersonList = ref<Partial<IPersonLi>[]>([])
const switchPreviousStates = ref({})
const preventSwitchChange = (val, row) => {
  switchPreviousStates.value[row.userId] = val
  row.userType = val === '2' ? '1' : '2'
}
const confirmSetLeader = (row) => {
  const newValue = switchPreviousStates.value[row.userId]
  row.userType = newValue
  handleSetLeader(newValue, row)
}
const delRef = ref()
// 删除弹框
const showDelDialog = (row) => {
  // deleteId.value = row.id
  delRef.value.showDialog(row.id)
}
const handleDel = (id) => {
  sendDel(id)
}
const sendDel = async (id) => {
  let res: any = await $API.post({
    url: 'edu-app-server/api/workbench/staff/delete',
    data: {
      id,
      unitId: route.query.id
    }
  })
  if (res.code == 'success') {
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
    getList()
  }
}
const dialogRef = ref()
// 新增人员信息
const addPersonInfo = () => {
  dialogRef.value.showDialog({})
}
const handleSetLeader = (val, row) => {
  if (val === '2') {
    personList.value.forEach((item) => {
      if (item.userId !== row.userId) {
        item.userType = '1'
      }
    })
  }
}
// 处理文件点击
const handleFileClick = (fileName) => {
  if (!fileName) return
  const suffix = fileName.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + fileName
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    window.open(config.downloadFileUrl + fileName, '_blank')
  }
}
const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}
// 获取详情
async function getDetail() {
  const url = '/atomic-upms-service/relate/project/detail'
  const params = {
    projectId: actionData.value.projectId,
    relateOrgCode: route.query.id || ''
  }
  const res: any = await $API.get({ url, params })
  if (res.code === 200) {
    detailData.value = res.data
    console.log(res.data)
  }
}

// 清除文件
const clearFile = () => {
  formData.value.cPlanFile = ''
  formData.value.ePlanFile = ''
  formData.value.protocolFile = ''
  formData.value.cPlanFileIds = ''
  formData.value.ePlanFileIds = ''
  formData.value.protocolFileIds = ''
  cPlanLi.value = []
  ePlanLi.value = []
  protocolLi.value = []
}

const close = () => {
  // clearFile()
  // visible.value = false
  emits('action', { action: ACTION.CLOSE2 })
}
const selectedPersons = ref<Partial<IPersonLi>[]>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const tableData = ref([])
const unitId = ref<any>('')
const ui: Record<string, any> = useUserInfo()

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/staff/queryListWithProj',
    params: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      sysCode: 'web',
      isBlack: '0',
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

const deletedPersons = ref<any[]>([])

const deleteEmployee = (row) => {
  deletedPersons.value.push(row)
  originalPersonList.value = originalPersonList.value.filter((item) => item.userId !== row.userId)
  personList.value = personList.value.filter((item) => item.userId !== row.userId)
  selectedPersons.value = selectedPersons.value.filter((item) => item.userId !== row.userId)
}

const save = async () => {
  emits('action', { action: ACTION.CLOSE2 })
}
watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
      getDetail()
    }
  },
  { immediate: true }
)
defineOptions({ name: 'ModifyComp' })
</script>

<style scoped lang="scss">
.cus-card {
  color: #484a4d;
  p {
    font-size: 16px;
    font-weight: bold;
  }

  .custom-content {
    padding: 20px;
  }
}
:deep(.el-drawer__body) {
  padding-top: 0;
}

.dialog-content {
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.w_page_num {
  display: flex;
  justify-content: right;
  margin-top: 20px;
}
</style>
