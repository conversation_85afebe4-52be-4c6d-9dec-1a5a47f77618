<template>
  <el-form ref="formRef" inline :model="filterForm" class="cus-form" inline-message>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="项目名称" path="projectName">
          <el-input v-model="filterForm.projectName" placeholder="请输入" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="项目状态" path="projectStatus">
          <el-select v-model="filterForm.projectStatus" placeholder="全部" clearable>
            <el-option label="待开始" :value="0" />
            <el-option label="进行中" :value="1" />
            <el-option label="已结束" :value="2" />
            <el-option label="已作废" :value="3" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-if="+ui.orgRes === 2">
        <!-- 相关方单位不需要 -->
        <el-form-item label="归属单位" path="orgName">
          <el-input v-model="filterForm.orgName" placeholder="请输入" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="+ui.orgRes === 2 ? 6 : 12">
        <el-form-item style="width: 100%">
          <div class="btn-group">
            <el-button type="primary" @click="doHandle(ACTION.SEARCH)">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ACTION } from '../constant'
import { useUserInfo } from '~/store'

const ui = useUserInfo()

const emits = defineEmits(['action'])

const filterForm = ref<Record<string, any>>({
  projectName: '',
  projectStatus: '',
  orgName: ''
})

function doHandle(action: ACTION) {
  emits('action', { action, data: { ...filterForm.value } })
}

function resetSearch() {
  filterForm.value = {}
  doHandle(ACTION.SEARCH)
}

onMounted(() => {
  watch(
    () => filterForm.value,
    () => doHandle(ACTION.SEARCH),
    { immediate: true, deep: true }
  )
})
</script>
<style scoped lang="scss">
.cus-form {
  .el-form-item {
    margin-bottom: 0;
    margin-right: 0;
  }

  .el-input {
    --el-input-width: 220px;
  }

  .el-select {
    --el-select-width: 220px;
  }

  .btn-group {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
