<template>
  <el-drawer v-model="visible" direction="rtl" size="85%">
    <template #header>
      <HeadTitle title="项目详情" />
    </template>
    <div class="drawer-box">
      <div class="cus-card">
        <p>项目信息：</p>
        <div class="custom-content">
          <el-row style="width: 100%">
            <el-col :span="12">
              <span>项目名称：</span>
              <span style="flex: 1">{{ detailData.projectName }}</span>
            </el-col>
            <el-col :span="12">
              <span>项目负责人：</span>
              <span>{{ detailData.responsibleUserName }}</span>
            </el-col>
          </el-row>
          <el-row style="width: 100%">
            <el-col :span="12">
              <span>项目起止日期：</span>
              <span>{{ detailData.startDate }} ~ {{ detailData.endDate }}</span>
            </el-col>
            <el-col :span="12">
              <span>项目状态：</span>
              <span>{{ detailData.projectStatusName }}</span>
            </el-col>
          </el-row>
          <el-row>
            <div class="flex-box">
              <span>项目描述：</span>
              <span>{{ detailData.projectDesc || '--' }}</span>
            </div>
          </el-row>
          <el-row>
            <div class="flex-box">
              <span>施工方案：</span>
              <div class="file-list" v-if="detailData.constructSchemeList?.length">
                <span
                  class="attachment-item"
                  v-for="item in detailData.constructSchemeList"
                  :key="item.id"
                  @click="handleFileClick(item.filePath)"
                >
                  {{ item.fileName }}
                </span>
              </div>
            </div>
          </el-row>
          <el-row>
            <div class="flex-box">
              <span>应急预案：</span>
              <div class="file-list" v-if="detailData.emergencyPlanList?.length">
                <span
                  class="attachment-item"
                  v-for="item in detailData.emergencyPlanList"
                  :key="item.id"
                  @click="handleFileClick(item.filePath)"
                >
                  {{ item.fileName }}
                </span>
              </div>
            </div>
          </el-row>
          <el-row>
            <div class="flex-box">
              <span>安全协议：</span>
              <div class="file-list" v-if="detailData.securityProtocolList?.length">
                <span
                  class="attachment-item"
                  v-for="item in detailData.securityProtocolList"
                  :key="item.id"
                  @click="handleFileClick(item.filePath)"
                >
                  {{ item.fileName }}
                </span>
              </div>
            </div>
          </el-row>
        </div>
      </div>
      <div class="cus-card person-li">
        <p>人员管理：</p>
        <el-table height="100%" :data="tableData" stripe>
          <el-table-column label="员工姓名" prop="userName" align="center" />
          <el-table-column label="手机号" prop="phoneNo" align="center" />
          <el-table-column label="证件名称" prop="cardName" align="center" />
          <el-table-column label="证件编号" prop="cardNo" align="center" />
          <el-table-column label="作业次数" prop="workNum" align="center">
            <template #default="scope">
              {{ scope.row.workNum || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="作业违规次数" prop="workViolateNum" align="center">
            <template #default="scope">
              {{ scope.row.workViolateNum || 0 }}
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-box">
          <el-pagination
            v-model:currentPage="query.pageNo"
            v-model:page-size="query.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { computed, inject, onMounted, ref, Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '~/common/api'
import config from '~/config'
import { IActionData, PROVIDE_KEY } from '~/view/enterprise/projectMgr/constant'
import { IPersonLi } from '~/view/enterprise/projectMgr/type'

const route = useRoute()
const router = useRouter()
const visible = ref(false)

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>

const query = ref<any>({
  pageNo: 1,
  pageSize: 20
})
const total = ref(0)

function handleSizeChange(val: number) {
  query.value.pageSize = val
  query.value.pageNo = 1
  getTableData()
}
function handleCurrentChange(page: number) {
  query.value.pageNo = page
  getTableData()
}

const tableData = ref<Partial<IPersonLi>[]>([])

async function getTableData() {
  const url = '/atomic-upms-service/relate/project/queryProjectUserList'
  const params = { ...query.value, projectId: actionData.value.projectId }
  const res: any = await $API.post({ url, data: params })
  if (res.code === 200) {
    total.value = res.data.total
    tableData.value = res.data.rows
  }
}

const detailData = ref<Record<string, any>>({})
// 获取详情
async function getDetail() {
  const url = '/atomic-upms-service/relate/project/detail'
  const params = {
    projectId: actionData.value.projectId,
    relateOrgCode: route.query.id || ''
  }
  const res: any = await $API.get({ url, params })
  if (res.code === 200) {
    detailData.value = res.data
    console.log(res.data)
  }
}

// 处理文件点击
const handleFileClick = (fileName) => {
  if (!fileName) return
  const suffix = fileName.split('.').pop()
  if (['doc', 'docx'].includes(suffix)) {
    const newUrl = config.downloadFileUrl + fileName
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    window.open(config.downloadFileUrl + fileName, '_blank')
  }
}

onMounted(() => {
  if (actionData.value.projectId) {
    getDetail()
    getTableData()
  }
})

defineOptions({ name: 'DetailComp' })
</script>

<style scoped lang="scss">
.drawer-box {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  color: #484a4d;

  .cus-card {
    p {
      font-size: 16px;
      font-weight: bold;
    }

    .custom-content {
      padding: 20px;
    }

    .flex-box {
      display: flex;

      span {
        flex-shrink: 0;

        &:last-child {
          flex: 1;
        }
      }

      div {
        flex: 1;
      }
    }
  }

  .person-li {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: 15px;

    .pagination-box {
      display: flex;
      justify-content: flex-end;
    }
  }

  .file-list {
    display: flex;
    align-items: center;
    gap: 15px;

    .attachment-item {
      color: #409eff;
      cursor: pointer;
      line-height: 1.5;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
