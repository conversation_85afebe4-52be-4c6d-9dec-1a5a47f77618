<template>
  <div class="relative pb-[20px]">
    <div class="cus-card">
      <p class="ml-[20px]">项目管理：</p>
      <el-form ref="formRef" class="custom-content" :model="formData" :rules="rules">
        <el-form-item label="施工方案" prop="cPlanFile">
          <el-input v-model="formData.cPlanFile" v-show="false"></el-input>
          <file-uploader
            ref="cPlanRef"
            v-model="cPlanLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'cPlanFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
        <el-form-item label="应急预案" prop="ePlanFile">
          <el-input v-model="formData.ePlanFile" v-show="false"></el-input>
          <file-uploader
            ref="ePlanRef"
            v-model="ePlanLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'ePlanFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
        <el-form-item label="安全协议" prop="protocolFile">
          <el-input v-model="formData.protocolFile" v-show="false"></el-input>
          <file-uploader
            ref="protocolRef"
            v-model="protocolLi"
            :accept="accept"
            :maxCount="3"
            @change="handleFileChange($event, 'protocolFile')"
            @uploading="updateUploadingStatus"
          />
        </el-form-item>
      </el-form>
    </div>
    <div class="cus-card">
      <p class="ml-[20px]">人员管理：</p>
      <div class="custom-content">
        <div class="search-box flex items-center justify-between">
          <el-form :model="searchForm" inline>
            <el-form-item label="员工姓名">
              <el-input v-model="searchForm.name" placeholder="请输入员工姓名" clearable @input="debouncedSearch" />
            </el-form-item>
            <el-form-item label="员工手机号">
              <el-input v-model="searchForm.phone" placeholder="请输入员工手机号" clearable @input="debouncedSearch" />
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="addPerson" class="bg-[#2468f2]">新增人员</el-button>
        </div>
        <el-scrollbar height="380px">
          <el-table :data="personList" stripe style="width: 100%" v-loading="loading" height="380">
            <el-table-column label="员工姓名" prop="userName" align="center" show-overflow-tooltip />
            <el-table-column label="手机号" prop="phoneNo" align="center" show-overflow-tooltip />
            <el-table-column label="证件名称" prop="cardName" align="center" show-overflow-tooltip />
            <el-table-column label="证件编号" prop="cardNo" align="center" show-overflow-tooltip />
            <el-table-column label="作业次数" prop="workNum" align="center" show-overflow-tooltip />
            <el-table-column label="作业违规次数" prop="workViolateNum" align="center" show-overflow-tooltip />
            <el-table-column label="操作" width="260" fixed="right" align="center">
              <template #default="scope">
                <div class="flex items-center">
                  <div class="mr-4 flex items-center">
                    <span class="text-[#2468f2] mr-2">设为项目现场负责人</span>
                    <el-popconfirm
                      :title="scope.row.userType === '1' ? '确定设为项目现场负责人吗?' : '确定取消项目现场负责人吗?'"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      @confirm="confirmSetLeader(scope.row)"
                      width="260"
                      :icon="QuestionFilled"
                      icon-color="#f56c6c"
                    >
                      <template #reference>
                        <el-switch
                          v-model="scope.row.userType"
                          inactive-color="#DCDFE6"
                          active-color="#2468f2"
                          inline-prompt
                          active-text="开"
                          inactive-text="关"
                          active-value="2"
                          inactive-value="1"
                          @change="preventSwitchChange($event, scope.row)"
                        />
                      </template>
                    </el-popconfirm>
                  </div>
                  <el-popconfirm
                    title="确定要删除这位员工吗?"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="deleteEmployee(scope.row)"
                    width="220"
                    :icon="CircleClose"
                    icon-color="#ff4949"
                  >
                    <template #reference>
                      <el-button type="danger" link>删除</el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </div>
    </div>
    <div class="cus-card flex justify-end pr-[20px]">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save" :loading="isUploading">确定</el-button>
    </div>
  </div>
  <AddPerson
    v-model:addPersonVisible="addPersonVisible"
    :selected-persons="selectedPersons"
    @update="handleAddPersons"
  />
</template>

<script setup lang="ts">
import { CircleClose, QuestionFilled } from '@element-plus/icons-vue'
import { useDebounceFn } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { computed, inject, onMounted, ref, Ref, watch } from 'vue'
import $API from '~/common/api'
import FileUploader from '~/components/upload/FileUploader.vue'
import { ACTION, IActionData, PROVIDE_KEY } from '~/view/enterprise/projectMgr/constant'
import { IPersonLi } from '~/view/enterprise/projectMgr/type'
import AddPerson from './addPerson.vue'

const visible = ref(false)
const accept = '.pdf,.doc,.docx'
const formData = ref<Record<string, any>>({
  cPlanFile: '',
  ePlanFile: '',
  protocolFile: '',
  cPlanFileIds: '',
  ePlanFileIds: '',
  protocolFileIds: ''
})
const cPlanLi = ref<any[]>([])
const ePlanLi = ref<any[]>([])
const protocolLi = ref<any[]>([])
const addPersonVisible = ref(false)
const isUploading = ref(false)
const cPlanRef = ref<any>(null)
const ePlanRef = ref<any>(null)
const protocolRef = ref<any>(null)
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>
const actionData = computed(() => currentAction.value?.data) as Ref<Record<string, any>>
const emits = defineEmits(['action'])
// 人员管理相关
const searchForm = ref({
  name: '',
  phone: ''
})

const loading = ref(false)
const personList = ref<Partial<IPersonLi>[]>([])
const formRef = ref()
// 添加表单验证规则
const rules = {
  cPlanFile: [
    {
      required: true,
      message: '请上传施工方案',
      trigger: 'change'
    }
  ],
  ePlanFile: [
    {
      required: true,
      message: '请上传应急预案',
      trigger: 'change'
    }
  ],
  protocolFile: [
    {
      required: true,
      message: '请上传安全协议',
      trigger: 'change'
    }
  ]
}

const isInitializing = ref(true)
const originalPersonList = ref<Partial<IPersonLi>[]>([])
const switchPreviousStates = ref({})
const preventSwitchChange = (val, row) => {
  switchPreviousStates.value[row.userId] = val
  row.userType = val === '2' ? '1' : '2'
}
const confirmSetLeader = (row) => {
  const newValue = switchPreviousStates.value[row.userId]
  row.userType = newValue
  handleSetLeader(newValue, row)
}

const handleSetLeader = (val, row) => {
  if (val === '2') {
    personList.value.forEach((item) => {
      if (item.userId !== row.userId) {
        item.userType = '1'
      }
    })
  }
}

// 获取详情
async function getDetail() {
  const url = '/atomic-upms-service/relate/project/detail'
  const params = { projectId: actionData.value.projectId }
  const res: any = await $API.get({ url, params })

  if (res.code === 200) {
    const { constructSchemeList, emergencyPlanList, securityProtocolList } = res.data

    const processFileList = (fileList) => {
      if (!fileList?.length) return []
      return fileList.map((file) => ({
        ...file,
        name: file.fileName,
        url: file.fileUrl || file.filePath,
        uid: file.id || file.fileId || Date.now() + Math.random(),
        status: 'success',
        fileId: file.id || file.fileId,
        isOriginal: true,
        originalData: { ...file }
      }))
    }

    const cFiles = processFileList(constructSchemeList)
    const eFiles = processFileList(emergencyPlanList)
    const pFiles = processFileList(securityProtocolList)

    cPlanLi.value = [...cFiles]
    ePlanLi.value = [...eFiles]
    protocolLi.value = [...pFiles]

    formData.value = {
      ...formData.value,
      cPlanFile: cFiles.length ? cFiles.length + '' : '',
      ePlanFile: eFiles.length ? eFiles.length + '' : '',
      protocolFile: pFiles.length ? pFiles.length + '' : '',
      cPlanFileIds: cFiles.map((file) => file.fileId || file.id).join(','),
      ePlanFileIds: eFiles.map((file) => file.fileId || file.id).join(','),
      protocolFileIds: pFiles.map((file) => file.fileId || file.id).join(',')
    }
  }
}

// 修改文件变化处理函数
const handleFileChange = (files: any[], key: string) => {
  const fileArray = files || []

  const originalFiles = fileArray.filter((file) => file.isOriginal)
  const newFiles = fileArray.filter((file) => !file.isOriginal)
  const originalFileIds = originalFiles.map((file) => file.fileId || file.id).filter(Boolean)
  const newFileIds = newFiles.map((file) => file.fileId || file.response?.data?.id).filter(Boolean)
  const allFileIds = [...originalFileIds, ...newFileIds].join(',')
  switch (key) {
    case 'cPlanFile':
      formData.value.cPlanFile = fileArray.length > 0 ? fileArray.length + '' : ''
      formData.value.cPlanFileIds = allFileIds
      break
    case 'ePlanFile':
      formData.value.ePlanFile = fileArray.length > 0 ? fileArray.length + '' : ''
      formData.value.ePlanFileIds = allFileIds
      break
    case 'protocolFile':
      formData.value.protocolFile = fileArray.length > 0 ? fileArray.length + '' : ''
      formData.value.protocolFileIds = allFileIds
      break
  }
}

// 更新上传状态
const updateUploadingStatus = (status: boolean) => {
  isUploading.value = status
}

const checkUploadStatus = async () => {
  const refs = [cPlanRef, ePlanRef, protocolRef]
  for (const ref of refs) {
    if (ref.value && typeof ref.value.checkUploadStatus === 'function') {
      const status = await ref.value.checkUploadStatus()
      if (!status) {
        return false
      }
    }
  }
  return true
}
// 清除文件
const clearFile = () => {
  formData.value.cPlanFile = ''
  formData.value.ePlanFile = ''
  formData.value.protocolFile = ''
  formData.value.cPlanFileIds = ''
  formData.value.ePlanFileIds = ''
  formData.value.protocolFileIds = ''
  cPlanLi.value = []
  ePlanLi.value = []
  protocolLi.value = []
}

const searchPerson = () => {
  const { name, phone } = searchForm.value

  if (!name && !phone) {
    personList.value = [...originalPersonList.value]
    return
  }
  personList.value = originalPersonList.value.filter((item: any) => {
    const matchName = !name || (item.userName && item.userName.toLowerCase().includes(name.toLowerCase()))
    const matchPhone = !phone || (item.phoneNo && item.phoneNo.includes(phone))

    return matchName && matchPhone
  })
}

const debouncedSearch = useDebounceFn(() => {
  searchPerson()
}, 300)

const close = () => {
  clearFile()
  visible.value = false
  emits('action', { action: ACTION.CLOSE })
}
const selectedPersons = ref<Partial<IPersonLi>[]>([])

const getPerson = async () => {
  try {
    loading.value = true

    await getDetail()

    const url = '/atomic-upms-service/relate/project/queryProjectUserList'
    const params = { projectId: actionData.value.projectId || '', pageNo: 1, pageSize: -1 }
    const res: any = await $API.post({ url, data: params })

    if (res.code === 200) {
      originalPersonList.value = [...res.data.rows]
      personList.value = [...res.data.rows]
      selectedPersons.value = [...res.data.rows]
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

const addPerson = () => {
  addPersonVisible.value = true
}

const handleAddPersons = (persons) => {
  const previousLeaderId = personList.value.find((person) => person.userType === '2')?.userId
  selectedPersons.value = persons
  personList.value = []
  originalPersonList.value = []
  persons.forEach((person) => {
    personList.value.push({
      ...person,
      userType: person.userId === previousLeaderId ? '2' : '1'
    })
    originalPersonList.value = [...personList.value]
  })
}

const deletedPersons = ref<any[]>([])

const deleteEmployee = (row) => {
  deletedPersons.value.push(row)
  originalPersonList.value = originalPersonList.value.filter((item) => item.userId !== row.userId)
  personList.value = personList.value.filter((item) => item.userId !== row.userId)
  selectedPersons.value = selectedPersons.value.filter((item) => item.userId !== row.userId)
}

const save = async () => {
  try {
    await formRef.value.validate()
    if (!personList.value.length) {
      ElMessage.warning('请新增项目人员!')
      return
    }
    const hasLeader = personList.value.some((person) => person.userType === '2')
    if (!hasLeader) {
      ElMessage.warning('请先设置项目现场负责人!')
      return
    }
    const uploadComplete = await checkUploadStatus()
    if (!uploadComplete) {
      ElMessage.warning('部分文件未上传完成，请等待上传完成后再保存')
      return
    }

    const saveData = {
      projectId: actionData.value.projectId || '',
      constructScheme: formData.value.cPlanFileIds,
      emergencyPlan: formData.value.ePlanFileIds,
      securityProtocol: formData.value.protocolFileIds,
      projectUserList: personList.value,
      deleteUserList: deletedPersons.value.map((item) => item.projectUserId)
    }

    console.log('最终提交数据:', saveData)

    const url = '/atomic-upms-service/relate/project/manageProjectUser'
    const res: any = await $API.post({ url, data: saveData })
    if (res.code === 200) {
      // 发送保存请求
      emits('action', { action: ACTION.SAVE })
      ElMessage.success('保存成功')
    }
    visible.value = false
  } catch (error) {
    console.error('保存失败:', error)
  }
}
// 监听搜索表单变化
watch(
  searchForm,
  () => {
    debouncedSearch()
  },
  { deep: true }
)

onMounted(() => {
  getPerson()
})

defineExpose({
  visible
})

defineOptions({ name: 'ModifyComp' })
</script>

<style scoped lang="scss">
.cus-card {
  color: #484a4d;
  p {
    font-size: 16px;
    font-weight: bold;
  }

  .custom-content {
    padding: 20px;
  }
}
:deep(.el-drawer__body) {
  padding-top: 0;
}

.dialog-content {
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
