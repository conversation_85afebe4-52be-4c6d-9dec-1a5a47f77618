<template>
  <div class="table-wrap">
    <el-table height="100%" :data="tableData" stripe>
      <!--      <el-table-column type="index" width="60" label="序号" align="center" />-->
      <el-table-column label="项目名称" prop="projectName" align="center" show-overflow-tooltip />
      <el-table-column label="归属单位" prop="orgName" align="center" show-overflow-tooltip />
      <el-table-column width="120" label="现场负责人" prop="sceneUserName" align="center" show-overflow-tooltip />
      <el-table-column width="140" label="现场负责人手机号" prop="sceneUserContactNo" align="center" />
      <el-table-column width="80" label="项目状态" prop="status" align="center">
        <template #default="scope">
          <el-badge
            v-if="!!scope.row.projectStatusName"
            is-dot
            :offset="[-50, 12]"
            :color="statusColor(scope.row.projectStatus)"
          >
            {{ scope.row.projectStatusName }}
          </el-badge>
        </template>
      </el-table-column>
      <el-table-column width="80" label="项目成员" prop="personNum" align="center" />
      <el-table-column width="120" label="入场时间" prop="enterDate" align="center" />
      <el-table-column width="120" label="离场时间" prop="leaveDate" align="center" />

      <el-table-column width="200" label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button plain @click="doHandle(ACTION.DETAIL, scope.row)">详情</el-button>
          <el-button plain type="primary" @click="doHandle(ACTION.CONF2, scope.row)"> 管理 </el-button>
          <!-- 相关方单位 有管理按钮-->
          <el-button plain type="primary" v-if="ui.orgRes === '2'" @click="doHandle(ACTION.CONF, scope.row)">
            管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-box">
      <el-pagination
        v-model:currentPage="query.pageNo"
        v-model:page-size="query.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'
import { ACTION } from '~/view/enterprise/projectMgr/constant'
import { IProject } from '~/view/enterprise/projectMgr/type'

const route = useRoute()
const ui = useUserInfo()

const emits = defineEmits(['action'])

const statusColor = (status: string) => {
  switch (status) {
    case '0':
      return '#00cfe8'
    case '1':
      return '#527cff'
    case '2':
      return '#409d13'
    case '3':
      return '#fd8410'
    default:
      return
  }
}

const query = ref<any>({
  pageNo: 1,
  pageSize: 20
})
const total = ref(0)
const handleSizeChange = (val: number) => {
  query.value.pageSize = val
  query.value.pageNo = 1
  getTableList()
}

const handleCurrentChange = (page: number) => {
  query.value.pageNo = page
  getTableList()
}

const tableData = ref<IProject[]>([])
let filterData: Record<string, any> = {} // 搜索条件

async function getTableList() {
  const params = {
    ...query.value,
    ...filterData,
    orgCodes: route.query.orgCode || '',
    relateOrgCode: route.query.id || ui.value.serverUnitId
  }
  const url = '/atomic-upms-service/relate/project/list'
  const res: Record<string, any> = await $API.post({ url, data: { ...params } })

  if (res.code === 200) {
    total.value = res.data.total
    tableData.value = res.data.rows || []
  }
}

function getTableWrap(data: Record<string, any>) {
  filterData = Object.assign({ ...query.value }, { ...data })
  getTableList()
}

function doHandle(action: ACTION, data: Record<string, any>) {
  emits('action', { action, data })
}

defineExpose({ getTableList, getTableWrap })
</script>

<style scoped lang="scss">
.table-wrap {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: 1fr auto;

  // 自定义表头
  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .pagination-box {
    display: flex;
    justify-content: flex-end;
    padding: 16px;
    padding-bottom: 0;
  }
}
</style>
