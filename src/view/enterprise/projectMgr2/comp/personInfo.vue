<template>
  <div class="add_plan_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer" @closed="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div class="title-info">{{ formInline.id ? '编辑人员信息' : '新增人员信息' }}</div>
        </div>
      </template>
      <div class="w_plan_con_box">
        <div class="w_content_box">
          <el-scrollbar>
            <div class="mb-3 w_dialog_from_box">
              <el-form :model="formInline" class="" ref="ruleFormRef" :rules="rules" label-width="180px">
                <el-form-item label="人员姓名：" prop="name">
                  <el-input v-model="formInline.name" placeholder="请输入人员姓名" maxlength="5" />
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                  <el-input v-model="formInline.phone" placeholder="请输入手机号" maxlength="200" />
                </el-form-item>
                <el-form-item label="否是特种作业人员：">
                  <el-radio-group v-model="formInline.specialOperation">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="投保信息（工伤）：">
                  <UploadImg @getImgList="getInsure" :img-list="insureList" />
                </el-form-item>
                <el-form-item label="上传证件照（免冠照）：">
                  <UploadImg @getImgList="getCertif" :img-list="certifList" />
                </el-form-item>
                <el-form-item label="身份证（正面）：">
                  <UpIDCard
                    @getImgList="getIDfrontList"
                    :img-list="IDfrontList"
                    @automatic="handleAutoCard"
                    :auto="2"
                  />
                </el-form-item>
                <el-form-item label="身份证（反面）：">
                  <UpIDCard @getImgList="getID" :img-list="IDList" :auto="1" @automatic="handleAutoDate" />
                </el-form-item>
                <el-form-item label="身份证号码：" prop="idNumber">
                  <el-input v-model="formInline.idNumber" placeholder="请输入身份证号码" maxlength="200" />
                </el-form-item>
                <el-form-item label="身份证有效期：">
                  <el-date-picker
                    v-model="timeRange"
                    placeholder="请选择身份证有效期"
                    :size="size"
                    style="width: 100%"
                    type="daterange"
                    clearable
                    value-format="YYYY/MM/DD"
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="onSubmit()" :loading="loading">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import $API from '~/common/api'
import config from '@/config'
import { ElMessage } from 'element-plus'
import UploadImg from '@/components/uploadImg/index.vue'
import UpIDCard from '@/components/uploadImg/IDCardIndex.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const emits = defineEmits(['action', 'submit'])
const drawer = ref(false)
const loading = ref(false)
let insureList = ref<Record<string, any>[]>([])
let certifList = ref<Record<string, any>[]>([])
let IDfrontList = ref<Record<string, any>[]>([])
let IDList = ref<Record<string, any>[]>([])
const timeRange = ref<any>([])

// 选中人员数据
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: '请输入人员姓名',
      trigger: ['blur', 'change']
    },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
          callback(new Error('姓名必须为汉字'))
        } else if (value.length > 5) {
          callback(new Error('姓名不可超过5字'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  phone: [
    {
      required: true,
      message: '请输入手机号',
      trigger: ['blur', 'change']
    },
    {
      validator: (rule, value, callback) => {
        const regex = /^1[3-9]\d{9}$/
        if (!regex.test(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change']
    }
  ],
  idNumber: [
    {
      required: true,
      message: '请输入正确的身份证号码',
      trigger: ['blur', 'change']
    }
  ]
})
const size = ref<'' | 'large' | 'small'>('')

const formInline = ref<any>({
  unitId: route.query.id,
  id: '',
  name: '', // 人员姓名
  phone: '', //
  insureFile: '', // 投保照片
  insureFiles: [], // 投保数组
  documentFile: '', // 证件照id
  documentFiles: [], // 证件照
  idNumber: '', // 身份证号码
  idStartPeriod: '', // 开始有效期
  idEndPeriod: '', // 结束有效期
  idFile: '', // 身份证照片id
  idFiles: [], // 身份证数组
  specialOperation: ''
})

const handleClose = () => {
  drawer.value = false
  insureList.value = []
  certifList.value = []
  IDList.value = []
}

async function getDeatail(id: string) {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/staff/detail',
    params: {
      id
    }
  })
  formInline.value = { ...res.data }
  timeRange.value = [res.data.idStartPeriod, res.data.idEndPeriod]
  if (!formInline.value.blExpireTime) {
    formInline.value.blExpireTime = null
  }
  if (res.data.insureFiles) {
    insureList.value = res.data.insureFiles.map((item: any) => {
      return {
        name: item.fileName,
        url: config.downloadFileUrl + item.filePath,
        id: item.id
      }
    })
  }

  if (res.data.documentFiles) {
    certifList.value = res.data.documentFiles.map((item: any) => {
      return {
        name: item.fileName,
        url: config.downloadFileUrl + item.filePath,
        id: item.id
      }
    })
  }
  if (res.data.idFiles) {
    if (res.data.idFiles[0]) {
      IDfrontList.value = [
        {
          name: res.data.idFiles[0].fileName,
          url: config.downloadFileUrl + res.data.idFiles[0].filePath,
          id: res.data.idFiles[0].id
        }
      ]
    }

    if (res.data.idFiles[1]) {
      IDList.value = [
        {
          name: res.data.idFiles[1].fileName,
          url: config.downloadFileUrl + res.data.idFiles[1].filePath,
          id: res.data.idFiles[1].id
        }
      ]
    }
    // IDList.value = res.data.idFiles.map((item: any) => {
    //   return {
    //     name: item.fileName,
    //     url: config.downloadFileUrl + item.filePath,
    //     id: item.id,
    //   }
    // })
  }
}
const showDialog = (row: any) => {
  // editId.value = row.id
  IDfrontList.value = []
  IDList.value = []
  certifList.value = []
  insureList.value = []
  timeRange.value = []
  if (row.id) {
    formInline.value.id = row.id
    getDeatail(row.id)
  } else {
    ruleFormRef.value?.resetFields()
    formInline.value = {
      unitId: route.query.id,
      id: '',
      name: '', // 人员姓名
      phone: '', //
      insureFile: '', // 投保照片
      insureFiles: [], // 投保数组
      documentFile: '', // 证件照id
      documentFiles: [], // 证件照
      idNumber: '', // 身份证号码
      idStartPeriod: '', // 开始有效期
      idEndPeriod: '', // 结束有效期
      idFile: '', // 身份证照片id
      idFiles: [], // 身份证数组
      specialOperation: ''
    }
  }
  drawer.value = true
}

function handleAutoCard(data: any) {
  if (data.data.id_number) {
    // sendFrom.value.idNo = data.data.id_number;
    formInline.value.idNumber = data.data.id_number
  }
}

function handleAutoDate(data: any) {
  if (data.data.start_date) {
    timeRange.value = [data.data.start_date.split('.').join('-'), data.data.end_date.split('.').join('-')]
  }
}

async function onSubmit() {
  loading.value = true
  // if (IDfrontList.value.length == 0) {
  //   ElMessage({
  //     message: '请上传身份证正面照片',
  //     type: 'warning',
  //   })
  //   loading.value = false
  //   return
  // } else if (IDList.value.length == 0) {
  //   ElMessage({
  //     message: '请上传身份证反面照片',
  //     type: 'warning',
  //   })
  //   loading.value = false
  //   return
  // }

  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      formInline.value.insureFile = insureList.value.map((item: any) => item.id).join(',')
      formInline.value.documentFile = certifList.value.map((item: any) => item.id).join(',')
      // formInline.value.idFile = IDList.value.map((item: any) => item.id).join(',')
      if (IDfrontList.value.length > 0 && IDList.value.length > 0) {
        formInline.value.idFile = [IDfrontList.value[0].id, IDList.value[0].id].join(',')
      } else {
        formInline.value.idFile = ''
      }

      formInline.value.idStartPeriod = timeRange.value[0]
      formInline.value.idEndPeriod = timeRange.value[1]
      let res: any = await $API.post({
        url: formInline.value.id ? 'edu-app-server/api/workbench/staff/edit' : 'edu-app-server/api/workbench/staff/add',
        data: {
          ...formInline.value
        }
      })

      if (res.code == 'success') {
        ElMessage({
          message: formInline.value.id ? '编辑成功' : '新增成功',
          type: 'success'
        })
        handleClose()
        emits('action')
        loading.value = false
      } else {
        loading.value = false
      }
    } else {
      loading.value = false
    }
  })
}
function getInsure(list: any) {
  insureList.value = list
}
function getCertif(list: any) {
  certifList.value = list
}

function getIDfrontList(list: any) {
  IDfrontList.value = list
}

function getID(list: any) {
  IDList.value = list
}

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.add_plan_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
  }

  .w_add_plan_header {
    font-weight: 700;
    color: #333;
    width: 100%;
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }

    .title-info {
      font-size: 16px;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
  }

  .w_plan_con_box {
    font-size: 0.875rem;
    //background: yellowgreen;
    display: grid;
    height: 100%;
    grid-template-rows: 100%;
    grid-template-columns: 1fr;
    color: rgba(72, 74, 77, 1);

    .bg_text {
      color: rgba(48, 49, 51, 1);
      font-size: 16px;
    }

    .w_content_box {
      //background: red;
      width: 100%;
      //height: 790px;
      height: 100%;

      .w_plan_con_one {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .div_con20 {
          height: 20px;
          line-height: 20px;
          display: flex;

          .w_flex-1 {
            flex: 1;
          }

          .w_flex-2 {
            flex: 2;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .w_dialog_from_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 20px;

        .w_file_btn {
          width: 136px;
          height: 32px;
          border: 1px solid #527cff;
          text-align: center;
          color: #527cff;
          cursor: pointer;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
        }

        //background: red;
        .w_file_t {
          color: #a8abb2;
        }
      }

      .w_table_box {
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #e4e5eb;
        padding: 10px 0 16px;
        margin-bottom: 22px;

        .w_dialog_from_title {
          height: 46px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
        }

        .w_add_bor {
          width: 100px;
          border: 1px solid #527cff;
          color: #527cff;
          text-align: center;
          border-radius: 4px;
          height: 32px;
          line-height: 32px;
          cursor: pointer;
        }

        .w_span_cursor {
          color: #527cff;
          cursor: pointer;
        }

        :deep(.el-table__header .el-table__cell) {
          background-color: rgba(245, 246, 249, 1);
          /* 表头背景色 */
          color: #606266;
          /* 表头字体颜色 */
          font-size: 14px;
          /* 表头字体大小 */
          height: 48px;
        }
      }

      .w_page_box {
        margin-top: 16px;
        height: 32px;
        display: flex;
        justify-content: end;
        padding-right: 24px;
      }
    }
  }

  :deep(.el-drawer__footer) {
    padding: 0 24px !important;
    //padding-top: 0!important;
    border-top: 1px solid rgba(235, 238, 245, 1) !important;
  }

  .w_dialog_foot {
    height: 72px;
    display: flex;
    justify-content: end;
    align-items: center;

    //background-color: red;
    //padding-right: 20px;
    .w_btn {
      height: 32px;
    }

    .w_btn_bg {
      background-color: rgba(82, 124, 255, 1) !important;
    }
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
