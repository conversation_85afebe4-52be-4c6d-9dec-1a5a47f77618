<template>
  <div class="w_train_box">
    <div class="w_train_head_box">
      <div class="flex">
        <div
          :class="['head_tab', { w_active: current == 'trainingCourseIndexCom' }]"
          @click="ChangTab('trainingCourseIndexCom')"
        >
          安全教育培训课程学习
        </div>
        <div
          :class="['head_tab', { w_active: current == 'projectEntryCourseIndexCom' }]"
          @click="ChangTab('projectEntryCourseIndexCom')"
        >
          项目进场课程学习
        </div>
        <div
          :class="['head_tab', { w_active: current == 'newEmployeeCourseIndexCom' }]"
          @click="ChangTab('newEmployeeCourseIndexCom')"
        >
          新人必学课程学习
        </div>
        <div
          :class="['head_tab', { w_active: current == 'trainingRecordsIndexCom' }]"
          @click="ChangTab('trainingRecordsIndexCom')"
        >
          自主上传培训记录
        </div>
        <div
          :class="['head_tab', { w_active: current == 'safetyRecordIndexCom' }]"
          @click="ChangTab('safetyRecordIndexCom')"
        >
          三级安全培训记录
        </div>
      </div>
    </div>
    <div class="w_train_tabs_box">
      <el-scrollbar>
        <component :is="tabs[current]"></component>
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import trainingCourseIndexCom from './comp/trainingCourseIndex.vue'
import projectEntryCourseIndexCom from './comp/projectEntryCourseIndex.vue'
import newEmployeeCourseIndexCom from './comp/newEmployeeCourseIndex.vue'
import trainingRecordsIndexCom from './comp/trainingRecordsIndex.vue'
import safetyRecordIndexCom from './comp/safetyRecordIndex.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

const tabs = {
  trainingCourseIndexCom,
  projectEntryCourseIndexCom,
  newEmployeeCourseIndexCom,
  trainingRecordsIndexCom,
  safetyRecordIndexCom
}

const current = ref('trainingCourseIndexCom')

const ChangTab = (val: string) => {
  current.value = val
}

watch(
  () => route.query.tab,
  (val) => {
    let num = sessionStorage.getItem('tabNum')
    if (val && val === 'trainCom' && num == '3') ChangTab('safetyRecordIndexCom')
  },
  { immediate: true }
)

// 创建计划
defineOptions({ name: 'trainComIndex' })
</script>

<style scoped lang="scss">
.w_train_box {
  //height: 100%;
  //width: 100%;
  //display: grid;
  //grid-template-columns: 1fr;
  //grid-template-rows: 54px 1fr;
  //grid-row-gap: 20px;
  //background: #28a458;
  .w_train_head_box {
    //background-color: #0081ff;
    background-color: white;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .w_train_tabs_box {
    height: 100%;
    width: 100%;
    margin-top: 20px;
    background-color: #fff;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }
}
</style>
