<template>
  <div class="w_train_text_box">
    <div class="w_head_table_div">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="trainTopic" label="培训主题" align="center" show-overflow-tooltip />
        <el-table-column prop="trainTime" label="培训日期" align="center" show-overflow-tooltip />
        <el-table-column prop="userNum" label="参训对象（人）" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span class="span_cursor" @click="handlePerFun(scope.row)">{{ scope.row.userNum || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="trainRecordFiles" label="培训记录" align="center" show-overflow-tooltip>
          <template #default="scope">
            <imgViewList :img-list="scope.row.trainRecordFiles || []" />
          </template>
        </el-table-column>

        <el-table-column prop="examRecordFiles" label="考核记录" align="center" show-overflow-tooltip>
          <template #default="scope">
            <imgViewList :img-list="scope.row.examRecordFiles || []" />
          </template>
        </el-table-column>

        <el-table-column prop="otherFiles" label="其他附件" align="center" show-overflow-tooltip>
          <template #default="scope">
            <imgViewList :img-list="scope.row.otherFiles || []" />
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="上传人" align="center" show-overflow-tooltip />
        <el-table-column prop="createTime" label="上传时间" align="center" width="180" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createTime || '--' }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <course-per-com ref="detailRef"></course-per-com>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import coursePerCom from './coursePersonnelDialog.vue'
import imgViewList from '@/components/imgViewList/index.vue'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'

const detailRef = ref()

const { tableHeight } = mixTableHeight({ subtractHeight: 465 })

const route = useRoute()
const ui: Record<string, any> = useUserInfo()

const unitId = ref<any>('')

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const tableData = ref([])

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

const handlePerFun = (row) => {
  detailRef.value.showDialog(row.id)
}

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/record/queryListWithProj',
    params: {
      sysCode: 'web',
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
    }
    console.log('参数变化了1', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)

// 自主上传培训记录
defineOptions({ name: 'trainingRecordsIndex' })
</script>

<style scoped lang="scss">
.w_train_text_box {
  padding: 24px;
  font-size: 14px;
  background-color: white;

  //background: chocolate;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .w_head_info {
    padding: 0 24px 0px 24px;

    > div {
      height: 44px;
      line-height: 44px;
    }

    .w_div_test_info {
      display: flex;
      justify-content: start;

      //background: yellowgreen;
      > div {
        width: 260px;
        //background: red;
        margin-right: 10px;
      }
    }
  }

  .head_box {
    //background-color: #0081ff;
    background-color: white;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .table_box {
    background-color: chocolate;
    background-color: white;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 50px;
  }

  .w_head_table_div {
    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    .w_head_table_input {
      width: 100%;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  .span_cursor {
    color: #527cff;
    cursor: pointer;
  }

  :deep(.el-table .el-table__cell) {
    z-index: revert-layer;
  }
}
</style>
