<template>
  <div class="w_sign_box">
    <div class="w_head_table_div">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" width="60" label="序号" align="center" />
        <el-table-column prop="staffName" label="人员姓名" align="center" show-overflow-tooltip />
        <el-table-column prop="depart" label="部门" align="center" show-overflow-tooltip />
        <!-- <el-table-column prop="post" label="岗位" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.post || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="trainFiles" label="三级培训考核记录" align="center" show-overflow-tooltip>
          <template #default="scope">
            <imgViewList :img-list="scope.row.trainFiles || []" />
          </template>
        </el-table-column>
        <el-table-column prop="isUpload" label="是否上传" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.isUpload === 1">是</span>
            <span v-else-if="scope.row.isUpload === 0">否</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createName" label="上传人" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.createTime || '--' }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import imgViewList from '@/components/imgViewList/index.vue'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'

const { tableHeight } = mixTableHeight({ subtractHeight: 465 })

const route = useRoute()
const ui: Record<string, any> = useUserInfo()

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const tableData = ref([])

const unitId = ref<any>('')

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/threelevel/queryListWithProj',
    params: {
      sysCode: 'web',
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      unitId: unitId.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '' // 项目id - 来源于项目详情
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
    }
    console.log('参数变化了1', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)

// 创建计划
defineOptions({ name: 'safetyRecordIndex' })
</script>

<style scoped lang="scss">
.w_sign_box {
  padding: 24px;
  font-size: 14px;

  //background-color: white;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .w_head_info {
    padding: 0 24px 0px 24px;

    > div {
      height: 44px;
      line-height: 44px;
    }

    .w_div_test_info {
      display: flex;
      justify-content: start;

      //background: yellowgreen;
      > div {
        width: 260px;
        //background: red;
        margin-right: 10px;
      }
    }
  }

  .head_box {
    //background-color: #0081ff;
    background-color: white;
    height: 54px;
    display: flex;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;

    .head_tab {
      line-height: 54px;
      //background: red;
      margin-right: 42px;
      color: #484a4d;

      &:hover {
        color: rgba(82, 124, 255, 1);
      }
    }

    .w_active {
      color: rgba(82, 124, 255, 1);
      border-bottom: 3px solid #527cff;
    }
  }

  .table_box {
    background-color: chocolate;
    background-color: white;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 50px;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }
  }

  .w_head_table_div {
    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    .w_head_table_input {
      width: 100%;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  .w_red_bg {
    height: 8px;
    width: 8px;
    background-color: #f62a2a;
    border-radius: 50%;
    margin-right: 6px;
  }

  .w_green_bg {
    height: 8px;
    width: 8px;
    background-color: #16ac3e;
    border-radius: 50%;
    margin-right: 6px;
  }

  :deep(.el-table .el-table__cell) {
    z-index: revert-layer;
  }
}
</style>
