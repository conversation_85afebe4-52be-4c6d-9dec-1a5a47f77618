<template>
  <div class="w_train_course_box">
    <div class="w_train_course_table_div">
      <el-table :data="tableData" :height="tableHeight" stripe style="width: 100%">
        <el-table-column type="index" label="序号" align="center" width="60" />
        <el-table-column prop="userName" label="姓名" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="className" label="课程名称" align="center" width="120" show-overflow-tooltip />

        <el-table-column prop="deptName" label="部门" align="center" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.deptName || '--' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="postName" label="岗位" align="center" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.postName || '--' }}
          </template>
        </el-table-column> -->
        <el-table-column prop="finishStatusName" label="完成状态" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div class="flex justify-center items-center">
              <!--              <div :class="[scope.row.signIn == '0' ? 'w_red_bg' : 'w_green_bg']"></div>-->
              <div>{{ scope.row.finishStatusName || '--' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="studyDuration" label="学习时间(分钟)" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.studyDuration || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="学习进度" align="center" width="90" show-overflow-tooltip>
          <template #default="scope">
            {{
              scope.row.progress === 0 ? scope.row.progress + '%' : scope.row.progress ? scope.row.progress + '%' : '--'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="lastStudyTime" label="最近学习时间" align="center" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.lastStudyTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="signPhoto" label="参训人签名" align="center" width="120">
          <template #default="scope">
            <el-image
              v-if="scope.row.signPhoto"
              class="w_img_base"
              :src="scope.row.signPhoto"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[scope.row.signPhoto]"
            />
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="signTime" label="参训人签名时间" width="130" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.signTime || '---' }}
          </template>
        </el-table-column>
        <el-table-column prop="examStatusName" label="是否参考" width="80" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.examStatusName || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="试卷提交时间" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.submitTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="examUseTime" label="考试用时（分钟）" width="140" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.examUseTime || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="score" label="考试分值" align="center" width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.score || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="examResultName" label="考试结果" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.examResultName || '---' }}
          </template>
        </el-table-column>
        <el-table-column prop="trainResultName" label="培训结果" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.trainResultName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right" width="100">
          <template #default="scope">
            <el-button type="primary" plain size="small" class="span_cursor mr-1" @click="handleDetail(scope.row)"
              >学习详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="w_page_num">
      <el-pagination
        v-model:currentPage="pageNo"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <detailCom ref="detailRef"></detailCom>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import $API from '~/common/api'
import mixTableHeight from '~/common/tableHeight.js'
import { useRoute } from 'vue-router'
import detailCom from './detail.vue'
import { useUserInfo } from '~/store'

const route = useRoute()
const ui: Record<string, any> = useUserInfo()

const { tableHeight } = mixTableHeight({ subtractHeight: 465 })

const detailRef = ref()

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const tableData = ref([])

const unitId = ref<any>('')

const getList = async () => {
  let res: any = await $API.post({
    url: 'edu-inter-server/safeTrain/pageListNew',
    data: {
      unitId: unitId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      orgCode: route.query.orgCode || ui.value.serverUnitId, // 业主-企业单位id 来源用户信息
      projId: route.query.projId || '', // 项目id - 来源于项目详情
      type: 1 // 项目进场课程学习
    }
  })

  tableData.value = res.data.rows
  total.value = res.data.total
}

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

const handleDetail = (row) => {
  detailRef.value.showDialog(row.id, 1)
}

watch(
  () => route.query.id,
  (newParams) => {
    if (newParams) {
      unitId.value = newParams
      getList()
    }
    console.log('参数变化了1', newParams)
    // 在这里处理参数变化后的逻辑
  },
  { immediate: true }
)

//项目进场课程学习
defineOptions({ name: 'projectEntryCourseIndex' })
</script>

<style scoped lang="scss">
.w_train_course_box {
  padding: 24px;
  font-size: 14px;

  //background-color: white;
  .w_head_bg {
    background-color: rgba(235, 238, 245, 0.5);
  }

  .w_train_course_table_div {
    //background-color: red;
    width: 100%;

    :deep(.el-form-item__content) {
      width: 299px;
      height: 32px;
    }

    :deep(.el-table .el-table__cell) {
      z-index: revert-layer;
    }
  }

  :deep(.el-table__header .el-table__cell) {
    background-color: rgba(245, 246, 249, 1);
    /* 表头背景色 */
    color: #606266;
    /* 表头字体颜色 */
    font-size: 14px;
    /* 表头字体大小 */
    height: 48px;
  }

  .w_page_num {
    display: flex;
    justify-content: right;
    margin-top: 20px;
  }

  .w_font_w_700 {
    font-weight: 600;
    //background-color: red;
    height: 22px;
    line-height: 22px;
    color: #606266;
  }

  .w_red_bg {
    height: 8px;
    width: 8px;
    background-color: #f62a2a;
    border-radius: 50%;
    margin-right: 6px;
  }

  .w_green_bg {
    height: 8px;
    width: 8px;
    background-color: #16ac3e;
    border-radius: 50%;
    margin-right: 6px;
  }

  .w_img_base {
    height: 44px;
    width: 64px;
  }

  .w_span_cursor {
    color: #527cff;
    cursor: pointer;
  }

  .span_cursor {
    color: #527cff;
    cursor: pointer;
  }
}
</style>