<template>
  <div class="w_detail_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer">
      <template #header>
        <div class="w_detail_header_box">
          <div class="mr-11px"></div>
          <div>课程学习人员</div>
        </div>
      </template>
      <div class="w_detail_box">
        <div class="mb-4">
          <el-input v-model="name" placeholder="请输入姓名查询" :clearable="true">
            <template #prefix>
              <el-icon class="el-input__icon">
                <search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <div class="w_scrollbar">
          <el-scrollbar height="100%">
            <div v-if="tableList.length > 0">
              <div class="w_detail_info_box mb-4" v-for="(item, index) in tableList" :key="index">
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">姓名：</div>
                  <div>{{ item.name || '--' }}</div>
                </div>
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">加入时间：</div>
                  <div>{{ item.createTime || '---' }}</div>
                </div>
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">手机号：</div>
                  <div>{{ item.phone || '--' }}</div>
                </div>
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">账号：</div>
                  <div>{{ item.account || '--' }}</div>
                </div>
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">部门：</div>
                  <div>{{ item.depart || '--' }}</div>
                </div>
                <div class="mb-4 w_detail_title flex">
                  <div class="mr-4">岗位：</div>
                  <div>{{ item.post || '--' }}</div>
                </div>
                <div class="w_detail_title flex">
                  <div class="mr-4">身份证号：</div>
                  <div>{{ item.idNumber || '--' }}</div>
                </div>
              </div>
            </div>
            <div v-else class="w_detail_info_box empty">暂无数据</div>
          </el-scrollbar>
        </div>
        <div class="w_page_num mt-7">
          <el-pagination v-model:currentPage="pageNo" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import $API from '~/common/api'

const drawer = ref(false)

const name = ref('')

const tableList = ref<any>([])

const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)

const idNum = ref('')

const getList = async () => {
  let res: any = await $API.get({
    url: 'edu-app-server/api/workbench/record/queryStaffList',
    params: {
      id: idNum.value,
      name: name.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value
    }
  })
  console.log(res, 'res---------------')
  if (res.code == 'success') {
    tableList.value = res.data.rows
    total.value = res.data.total
  }
}

const handleSizeChange = () => {
  pageNo.value = 1
  getList()
}
const handleCurrentChange = () => {
  getList()
}

// const handleClose = () => {
//   drawer.value = false
// }

const showDialog = (id = '') => {
  name.value = ''
  idNum.value = id
  getList()

  drawer.value = true
}

watch(name, () => {
  getList()
})

defineExpose({
  showDialog
})
// 创建计划
defineOptions({ name: 'coursePersonnelDialog' })
</script>

<style scoped lang="scss">
.w_detail_dialog_box {
  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
    font-size: 16px;
    font-weight: 700;
    color: #333;
  }

  .w_detail_header_box {
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .w_detail_box {

    //display: grid;
    //height: 100%;
    //grid-template-rows: 1fr;
    //grid-template-columns: 1fr;
    .w_scrollbar {
      height: calc(100vh - 204px);
      //background-color: red;
    }

    .empty {
      // height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .w_detail_info_box {
      height: 100%;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;
      padding: 20px 24px;

      .w_img_detail_base {
        width: 200px;
        height: 100px;
        //background: red;
      }
    }
  }

  .w_detail_class_box {
    margin-top: 20px;
    padding-left: 18px;
    padding-bottom: 20px;
    border-left: 1px dashed #527cff;
    position: relative;

    .w_detail_class_bg {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      text-align: center;
      color: white;
      position: absolute;
      top: 0;
      left: -10px;
      background: #527cff;
    }

    .w_detail_class_item {
      padding: 0 18px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;

      //> div {
      //  border-bottom: 1px solid #d8d8d8;
      //}
      >div:not(:last-child) {
        border-bottom: 1px solid #d8d8d8;
      }

      .w_detail_div_box {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        //background: #9a6e3a;
        //border: 1px solid #D8D8D8;

        .w_detail_div_l {
          width: 380px;
          //background: #28a458;
          display: flex;

          .w_detail_div_l_hide {
            width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            //display: inline-block;
            //background: red;
          }
        }

        .w_detail_div_r {
          width: 66px;
          height: 32px;
          border: 1px solid #527cff;
          line-height: 32px;
          text-align: center;
          color: #527cff;
        }
      }
    }
  }

  .w_detail_title {
    height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }

  .w_page_num {
    display: flex;
    justify-content: end;
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
