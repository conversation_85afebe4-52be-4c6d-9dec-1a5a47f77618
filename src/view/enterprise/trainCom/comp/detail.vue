<template>
  <div class="w_detail_dialog_box">
    <el-drawer v-model="drawer" modal-class="my-drawer">
      <template #header>
        <div class="w_detail_header_box">
          <div class="mr-11px"></div>
          <div>培训详情</div>
        </div>
      </template>
      <div class="w_detail_box">
        <div class="w_detail_info_box">
          <div class="mb-4 w_detail_title">{{ formInline.titleName || '--' }}</div>
          <div class="flex mb-4">
            <div class="mr-[140px]">发布人：{{ formInline.createName || '--' }}</div>
            <div>发布时间：{{ formInline.createTime || '--' }}</div>
          </div>
          <div class="mb-4 w_detail_progress">
            <el-progress :percentage="formInline.learningRate - 0" :stroke-width="16" :color="customColors" />
          </div>
          <div class="flex">
            <div>签名</div>
            <div class="w_img_detail_base ml-10">
              <el-image v-if="formInline.trainSignature" class="w_img_detail_base" :src="formInline.trainSignature"
                :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[formInline.trainSignature]" />
              <span v-else>--</span>
            </div>
          </div>
        </div>

        <div :class="[formInline.isNeedExam == 1 ? 'w_detail_class_box w_detail_border' : 'w_detail_class_box']">
          <div class="w_detail_class_bg">学</div>
          <div class="flex justify-between">
            <div class="w_detail_title mb-2">课程学习</div>
            <div class="mb-2">
              <el-popover placement="top-start" :title="Title" :width="450" trigger="hover" :content="text">
                <template #reference>
                  <div class="flex justify-between items-center">
                    <div class="w_description">培训通过说明</div>
                    <img class="w_description_img" src="../../assets/wen.png" />
                  </div>
                </template>
              </el-popover>
            </div>
          </div>
          <div class="w_detail_class_item">
            <div v-if="formInline.trainingMaterialFiles && formInline.trainingMaterialFiles.length > 0">
              <div :class="[
                formInline.trainingMaterialFiles.length - 1 === index
                  ? 'w_detail_div_box w_detail_no_border'
                  : 'w_detail_div_box',
              ]" v-for="(item, index) in formInline.trainingMaterialFiles" :key="index">
                <div class="w_detail_div_l">
                  <div>课件{{ index + 1 }}：</div>
                  <div class="w_detail_div_l_hide text-[#527cff]">{{ item.fileName }}</div>
                </div>
                <div class="w_detail_div_r">{{ item?.taskStaffFile?.studyState == 1 ? '已学习' : '未学习' }}</div>
              </div>
            </div>
            <div v-else class="mt-2 mb-2">暂无数据</div>
          </div>
        </div>

        <div class="w_detail_comment_box" v-if="formInline.isNeedExam == '1'">
          <div class="w_detail_class_bg">评</div>
          <div class="w_detail_title mb-2">在线考试</div>
          <div class="w_detail_c_head_box">
            <div class="w_detail_head_item">
              <div class="w_detail_head_title">{{ formInline.ksExamPaper.examName || '--' }}</div>
              <div class="w_detail_comment_con">
                <div>总题数：<span class="ml-1.5">{{ formInline.totalCount || '--' }}</span>题</div>
                <div>通过分数线：<span class="ml-1.5">{{ formInline.passScore || '--' }}</span>分</div>
                <div>考试时长：<span class="ml-1.5">{{ formInline.examPaperTime || '--' }}</span>分钟</div>
              </div>
            </div>

            <div class="w_detail_examination_box">
              <div class="w_detail_title mb-3 mt-3">考试记录</div>
              <div v-if="formInline.examList && formInline.examList.length > 0">
                <div class="w_detail_examination_item mb-1" v-for="(item, index) in formInline.examList" :key="index">
                  <div class="w_detail_item_l">
                    <div class="w_detail_item_round mr-2"></div>
                    <div class="mr-2">第{{ index + 1 }}次考试</div>
                    <div :class="[item.isPass == 1 ? 'w_detail_green' : 'w_detail_red']">
                      {{ item.isPassName }} （ {{ item.score }}分 ）
                    </div>
                  </div>
                  <div class="w_detail_item_r">交卷时间：{{ item.paperSubmitTime }}</div>
                </div>
              </div>
              <div v-else class="mt-2 mb-2 ml-2">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

import $API from '~/common/api'
import { Warning } from '@element-plus/icons-vue'
const drawer = ref(false)

const customColors = ref([
  {
    color: '#0B81FE',
  },
])

const formInline = ref<any>({
  createName: '', // 发布人
  createTime: '', // 发布时间
  learningRate: 0, // 学习进度
  trainSignature: '', // 参训人签名

  titleName: '', // 试卷名称
  totalCount: '', //题目总数
  passScore: '', //通过分数线
  examTime: '', // 考试时长分钟
  examList: [], //考试结果列表
  fileList: [], //学习文件列表
})

const Title = ref('培训完成标准：')

const text = ref(
  '1、课件学习完成度为100% + 提交理论考试；  \n' +
  '\n' +
  '2、考核结果通过标准：课件学习完成度为100% + 考试成绩达到评估标准； \n' +
  '\n' +
  '3、若无需考试的培训，则考核结果通过标准即为课件学习完成度为100%。'
)

const getDetail = async (id: string, type: number) => {
  let res: any = await $API.get({
    url: 'edu-inter-server/safeTrain/safeLearningDetail',
    params: {
      id,
      type
    },
  })
  console.log(res, 'res---------------')
  if (res.code == 'success') {
    formInline.value = res.data
  }
}

const initFormat = () => {
  formInline.value = {
    titleName: '', // 培训/课程名称
    createName: '', // 发布人
    createTime: '', // 发布时间
    learningRate: 0, // 学习进度
    trainSignature: '', // 参训人签名
    totalCount: '', //题目总数
    score: '', //分数
    examTime: '', // 考试时长分钟
    examList: [], //考试结果列表
    fileList: [], //学习文件列表
  }
}

const handleClose = () => {
  drawer.value = false
}

const showDialog = (id = '', type: number) => {
  initFormat()

  getDetail(id, type)

  drawer.value = true
}

defineExpose({
  showDialog,
})
// 创建计划
defineOptions({ name: 'AddDialogTrainingPlan' })
</script>

<style scoped lang="scss">
.w_detail_dialog_box {
  :deep(.el-drawer.rtl) {
    width: 648px !important;
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgba(235, 238, 245, 1);
    padding-top: 0 !important;
    font-size: 16px;
    font-weight: 700;
    color: #333;
  }

  .w_detail_header_box {
    height: 54px;
    display: flex;
    justify-content: start;
    align-items: center;

    div:first-of-type {
      /* 样式规则 */
      width: 18px;
      height: 12px;
      background: url('@/assets/image/drawer_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .w_detail_box {
    //display: grid;
    //height: 100%;
    //grid-template-rows: 1fr;
    //grid-template-columns: 1fr;
    color: #484a4d;
    font-size: 14px;

    .w_detail_info_box {
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;
      padding: 20px 24px;

      .w_img_detail_base {
        width: 200px;
        height: 100px;
        //background: red;
      }

      .w_detail_progress {
        width: 500px;
      }
    }
  }

  .w_detail_border {
    border-left: 1px dashed #527cff;
  }

  .w_detail_class_box {
    margin-top: 20px;
    padding-left: 18px;
    padding-bottom: 20px;
    position: relative;

    .w_detail_class_bg {
      height: 22px;
      width: 22px;
      line-height: 22px;
      border-radius: 50%;
      text-align: center;
      color: white;
      position: absolute;
      top: 0;
      left: -10px;
      background: #527cff;
    }

    .w_detail_class_item {
      padding: 0 18px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;

      .w_detail_div_box {
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        //background: #9a6e3a;
        //border: 1px solid #D8D8D8;
        border-bottom: 1px solid #d8d8d8;

        .w_detail_div_l {
          width: 380px;
          //background: #28a458;
          display: flex;

          .w_detail_div_l_hide {
            width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            //display: inline-block;
            //background: red;
          }
        }

        .w_detail_div_r {
          width: 66px;
          height: 32px;
          border: 1px solid #527cff;
          line-height: 32px;
          text-align: center;
          color: #527cff;
        }
      }

      .w_detail_no_border {
        border-bottom: none;
      }
    }
  }

  .w_detail_comment_box {
    //background: #0081ff;
    //padding: 0 18px;

    padding-left: 18px;
    padding-bottom: 20px;

    position: relative;

    .w_detail_class_bg {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      text-align: center;
      color: white;
      position: absolute;
      top: 0;
      left: -10px;
      background: #527cff;
    }

    .w_detail_c_head_box {
      padding: 20px 18px;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #e4e5eb;

      .w_detail_head_item {
        border-bottom: 1px solid #e4e5eb;

        .w_detail_head_title {
          height: 22px;
          font-weight: 600;
          font-size: 16px;
          color: #303133;
          line-height: 22px;
          width: 460px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 16px;
        }

        .w_detail_comment_con {
          display: flex;

          >div {
            width: 160px;
            height: 20px;
            line-height: 20px;
            margin-bottom: 20px;
          }
        }
      }

      .w_detail_examination_box {
        .w_detail_examination_item {
          display: flex;
          justify-content: space-between;

          .w_detail_item_l {
            display: flex;
            align-items: center;

            .w_detail_item_round {
              width: 6px;
              height: 6px;
              background: #527cff;
              border-radius: 50%;
            }

            .w_detail_red {
              color: rgba(209, 0, 0, 1);
            }

            .w_detail_green {
              color: rgba(36, 182, 105, 1);
            }
          }
        }
      }
    }
  }

  .w_detail_title {
    height: 22px;
    font-weight: 600;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
  }

  .w_description {
    height: 20px;
    line-height: 20px;
    margin-right: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  .w_description_img {
    height: 14px;
    width: 14px;
  }
}

:deep(.my-drawer.el-overlay) {
  background-color: rgba(0, 0, 0, 0);
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}
</style>
