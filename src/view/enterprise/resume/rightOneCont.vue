<template>
  <div class="lcont w-full">
    <div class="l-sm-t">
      <span class="lcont-title">{{ title }}</span>
      <span class="all-t pr-[30px] cursor-pointer" @click="toGoALL">全部</span>
    </div>
    <div class="r-center-cont w-full">
      <div class="line-color">
        <div class="one-line">
          <div>
            {{ type == '1' ? ' 未通过' : type == '2' ? '未查阅' : '未上传' }}
          </div>
          <div>
            <span class="font-bold text-[19px]">{{ computedOption.unfinished.count }}</span>
            <span>人次</span>
            <span>/</span>
            <span class="font-bold text-[19px]">{{ computedOption.unfinished.percentage }}</span>
            %
          </div>
        </div>
        <div class="onprogress w-full">
          <el-progress
            :percentage="computedOption.unfinished.percentage"
            color="#EDA24A"
            stroke-width="10"
            :format="format"
          />
        </div>
      </div>

      <div class="line-color">
        <div class="one-line">
          <div>{{ type == '1' ? ' 已通过' : type == '2' ? '已查阅' : '已上传' }}</div>
          <div>
            <span class="font-bold text-[19px]">{{ computedOption.finished.count }}</span>
            <span>人次</span>
            <span>/</span>
            <span class="font-bold text-[19px]">{{ computedOption.finished.percentage }}</span>
            %
          </div>
        </div>
        <div class="onprogress w-full">
          <el-progress
            :percentage="computedOption.finished.percentage"
            color="#527cff"
            stroke-width="10"
            :format="format"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import router from '~/router'
import { useRoute } from 'vue-router'
import { watch, computed } from 'vue'

const route = useRoute()
const props = defineProps({
  option: {
    type: Object,
    required: true,
    default: () => ({
      unfinished: { count: 0, percentage: 0 },
      finished: { count: 0, percentage: 0 }
    })
  },
  title: {
    type: String,
    default: '入场培训课程完成情况'
  },
  type: {
    type: String,
    default: '1'
  }
})
const format = () => ''
const computedOption = computed(() => ({
  unfinished: {
    count: props.option?.unfinished?.count ?? 0,
    percentage: props.option?.unfinished?.percentage ?? 0
  },
  finished: {
    count: props.option?.finished?.count ?? 0,
    percentage: props.option?.finished?.percentage ?? 0
  }
}))

function toGoALL() {
  let t = props.title
  if (t == '入场培训课程完成情况') {
    routerTo('/enterprise', 'trainCom', 'trainingCourseIndexCom')
  } else if (t == '规章制度查阅情况') {
    routerTo('/enterprise', 'regulationCom', 'regulationCom')
  } else if (t == '三级安全记录上传情况') {
    routerTo('/enterprise', 'trainCom', 'safetyRecordIndexCom')
    sessionStorage.setItem('tabNum', '3')
  }
}
function routerTo(path: any, tab: any, code: any) {
  if (path) {
    router.push({ path: path, query: { tab: tab, code: code, ids: Date.now(), id: route.query.id } })
  }
}

// 可以添加 watch 来监听数据变化
watch(
  () => props.option,
  (newVal) => {
    console.log('培训数据更新：', newVal)
  },
  { deep: true }
)

// 创建计划
defineOptions({ name: 'resumeCom' })
</script>

<style scoped lang="scss">
.l-sm-t {
  @apply flex justify-between items-center px-[10px] h-[50px];
  border-bottom: 1px solid #ccc;
}

.lcont {
  @apply m-[20px] h-[250px] w-full bg-[#fff] box-border;
}

.lcont-title {
  font-weight: 700;
}

.all-t {
  @apply text-[#527cff] text-[16px];
}

.one-line {
  @apply flex justify-between items-center h-[50px] px-[10px] text-[12px] text-[#666] w-full px-[40px];
}

.line-color {
  @apply px-[20px] my-[12px];
}
</style>
