<template>
  <div class="lcont w-full">
    <div class="l-sm-t">
      <span class="lcont-title">应急预案情况</span>
      <span class="all-t pr-[30px] cursor-pointer" @click="toGoALL">全部</span>
    </div>

    <div class="r-center-cont w-full">
      <div class="one-cont flex">
        <div><img src="./img/1.png" alt="" width="50" /></div>
        <div class="flex flex-col justify-s w-[120px]">
          <span class="font-bold text-[30px]">{{ option?.onSite?.total || 0 }}</span>
          <span>现场处置预案</span>
        </div>
      </div>
      <div class="one-cont flex">
        <div><img src="./img/1.png" alt="" width="50" /></div>
        <div class="flex flex-col justify-s w-[120px]">
          <span class="font-bold text-[30px]">{{ option?.comprehensive?.total || 0 }}</span>
          <span>综合应急预案</span>
        </div>
      </div>
      <div class="one-cont flex">
        <div><img src="./img/1.png" alt="" width="50" /></div>
        <div class="flex flex-col justify-s w-[120px]">
          <span class="font-bold text-[30px]">{{ option?.special?.total || 0 }}</span>
          <span>专项应急预案</span>
        </div>
      </div>
      <div class="one-cont flex">
        <div><img src="./img/1.png" alt="" width="50" /></div>
        <div class="flex flex-col justify-s w-[120px]">
          <span class="font-bold text-[30px]">{{ option?.other?.total || 0 }}</span>
          <span>其他类别预案</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import router from '~/router'
import { useRoute } from 'vue-router'

const route = useRoute()
const props = defineProps({
  option: {
    type: Object,
    default: () => ({
      comprehensive: { total: 0 },
      special: { total: 0 },
      onSite: { total: 0 },
      other: { total: 0 }
    })
  }
})
function routerTo(path: any, tab: any, code: any) {
  if (path) {
    router.push({ path: path, query: { tab: tab, code: code, ids: Date.now(), id: route.query.id } })
  }
}
function toGoALL() {
  routerTo('/enterprise', 'planCom', '')
}
// 创建计划
defineOptions({ name: 'resumeCom' })
</script>

<style scoped lang="scss">
.l-sm-t {
  @apply flex justify-between items-center px-[10px] h-[50px];
  border-bottom: 1px solid #ccc;
}

.lcont {
  @apply m-[20px] h-[250px] w-full bg-[#fff] box-border;
}

.lcont-title {
  font-weight: 700;
}

.all-t {
  @apply text-[#527cff] text-[16px];
}
.one-line {
  @apply flex justify-between items-center h-[50px] px-[10px] text-[12px] text-[#666] w-full px-[40px];
}
.line-color {
  @apply px-[20px] my-[12px];
}
.r-center-cont {
  @apply flex justify-evenly items-center h-[180px];
}
</style>
