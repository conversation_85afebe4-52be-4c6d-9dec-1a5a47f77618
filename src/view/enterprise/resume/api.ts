import $API from '~/common/api'

export class ResumeAPI {
  // 相关方资质证书
  static getCertificate(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/queryRelateCertificateInfo',
      params
    })
  }

  // 责任签署
  static getResponsibilitySign(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/querySigningStaticInfo',
      params
    })
  }

  // 入场培训
  static getEntryTraining(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/queryTrinFinishInfo',
      params
    })
  }

  // 应急预案
  static getEmergencyPlan(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/queryEmergencyPlanSituationInfo',
      params
    })
  }

  // 三级安全记录
  static getThreeLevelSafetyRecord(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/queryThreeLevelInfo',
      params
    })
  }

  // 规章制度查阅
  static getRegulationReading(params: any): Promise<any> {
    return $API.get({
      url: '/edu-app-server/resume/overview/queryRegulationsInfo',
      params
    })
  }
}
