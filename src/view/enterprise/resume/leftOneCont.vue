<template>
  <div class="lcont w-full">
    <div class="l-sm-t">
      <span class="lcont-title">{{ title }}</span>
      <span class="all-t pr-[30px] cursor-pointer" @click="toGoALL">全部</span>
    </div>
    <div class="center-l-cont">
      <div class="one-sm-num-t" v-for="(item, index) in centerList" :key="index">
        <div class="AA">{{ item.value }}</div>
        <div class="BB">{{ item.name }}</div>
      </div>
    </div>
    <div class="three-t bg-[#f5f6f7]">
      <div class="SD" v-for="(item, index) in lastList" :key="index">
        <span>{{ item.name }}</span>
        <span :class="'cc' + index" class="ml-[8px]">{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import router from '~/router'
import { useRoute } from 'vue-router'
import { useTransition } from '@vueuse/core'
const route = useRoute()

const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => {
      return { label: [], data: [] }
    }
  },
  title: {
    type: String,
    default: () => {
      return ['rgba(0, 244, 230, 1)', 'rgba(0, 146, 249, 1)']
    }
  },
  type: {
    type: Number,
    default: 1
  },
  option: {
    type: Object,
    required: true
  }
})

const centerList = ref([])
const lastList = ref([])

watch(
  () => props.option,
  (newVal) => {
    centerList.value = newVal.centerList
    lastList.value = newVal.lastList
  },
  { immediate: true, deep: true }
)

function toGoALL() {
  let t = props.title
  console.log('t', t)
  if (t == '相关方资质证书情况' || t == '承包商资质证书情况') {
    routerTo('/enterprise', 'enterpriseCom')
  } else if (t == '人员证书复审情况') {
    routerTo('/enterprise', 'personnelCom')
    sessionStorage.setItem('tabNum', '2')
  } else if (t == '责任制签署情况') {
    routerTo('/enterprise', 'accountabilityCom')
  } else if (t == '违规记录') {
    ElMessage({
      message: '此正在研发中，待上线',
      type: 'warning'
    })
  }
}
function routerTo(path: any, tab: any) {
  if (path) {
    router.push({ path: path, query: { tab: tab, ids: Date.now(), id: route.query.id } })
  }
}
// 创建计划
defineOptions({ name: 'resumeCom' })
</script>

<style scoped lang="scss">
.l-sm-t {
  @apply flex justify-between items-center px-[10px] h-[50px];
  border-bottom: 1px solid #ccc;
}

.lcont {
  @apply m-[20px] ml-[0] h-[250px] w-full bg-[#fff] box-border;
}

.lcont-title {
  font-weight: 700;
}

.all-t {
  @apply text-[#527cff] text-[16px];
}

.center-l-cont {
  @apply flex justify-evenly items-center px-[10px] h-[150px] w-full;

  .one-sm-num-t {
    @apply flex flex-col justify-center items-center w-[120px];

    .AA {
      @apply text-[30px] font-bold text-center w-[120px];
    }

    .BB {
      @apply text-[14px] text-center w-full w-[120px];
    }
  }
}

.three-t {
  @apply flex justify-around items-center h-[50px] w-full;

  .SD {
    @apply ml-[5%] flex justify-around items-center;
  }
}

.cc0 {
  @apply text-[#1a9c41] font-bold text-[20px];
}

.cc1 {
  @apply text-[#f19d29] font-bold text-[20px];
}

.cc2 {
  @apply text-[red] font-bold text-[20px];
}
</style>
