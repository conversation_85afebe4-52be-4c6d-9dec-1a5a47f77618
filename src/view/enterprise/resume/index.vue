<template>
  <div class="resume w-full flex">
    <div class="left h-full w-[35%]">
      <leftOneCont :title="ui.zhLogo === 'yanchang' ? '承包商资质证书情况' : '相关方资质证书情况'" :option="option1" />
      <leftOneCont title="人员证书复审情况" :option="option2" />
      <leftOneCont title="责任制签署情况" :option="option3" />
      <leftOneCont title="违规记录" :option="option4" />
    </div>
    <div class="right">
      <div class="onr-cont">
        <rightOneCont title="入场培训课程完成情况" :option="trainingOption" type="1" />
        <rightOneCont title="规章制度查阅情况" :option="rulesOption" type="2" />
        <rightOneCont title="三级安全记录上传情况" :option="safetyOption" type="3" />
        <riglastCont title="应急预案情况" :option="emergencyOption" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import leftOneCont from './leftOneCont.vue'
import rightOneCont from './rightOneCont.vue'
import riglastCont from './riglastCont.vue'
import { ResumeAPI } from './api'
import { useRoute } from 'vue-router'
import { useUserInfo } from '~/store'

const route = useRoute()
const ui = useUserInfo()

const option1 = ref({
  centerList: [
    { name: '证书总数', value: 0 },
    { name: '需复审总数', value: 0 }
  ],
  lastList: [
    { name: '正常', value: 0 },
    { name: '即将逾期', value: 0 },
    { name: '逾期', value: 0 }
  ]
})
const option2 = ref({
  centerList: [
    { name: '人员证书总数', value: 0 },
    { name: '需复审总数', value: 0 }
  ],
  lastList: [
    { name: '正常', value: 0 },
    { name: '即将逾期', value: 0 },
    { name: '逾期', value: 0 }
  ]
})

const option3 = ref({
  centerList: [{ name: '需签署', value: 0 }],
  lastList: [
    { name: '已签署', value: 0 },
    { name: '未签署', value: 0 }
  ]
})
const option4 = ref({
  centerList: [{ name: '违规上报总数', value: 0 }],
  lastList: [
    { name: '已处理', value: 0 },
    { name: '待处理', value: 0 }
  ]
})

// 添加入场培训数据的响应式对象
const trainingOption = ref({
  unfinished: {
    count: 0,
    percentage: 0
  },
  finished: {
    count: 0,
    percentage: 0
  }
})

// 添加规章制度数据
const rulesOption = ref({
  unfinished: {
    count: 0,
    percentage: 0
  },
  finished: {
    count: 0,
    percentage: 0
  }
})

// 添加三级安全记录数据
const safetyOption = ref({
  unfinished: {
    count: 0,
    percentage: 0
  },
  finished: {
    count: 0,
    percentage: 0
  }
})

const emergencyOption = ref({
  comprehensive: { total: 0 }, // 综合应急类
  special: { total: 0 }, // 专项应急预案
  onSite: { total: 0 }, // 现场处置预案
  other: { total: 0 } // 其他类别预案
})

// 相关方资质证书
const getCertificateData = async () => {
  let params = {
    relId: route.query.id,
    type: '1'
  }
  const res = await ResumeAPI.getCertificate(params)
  option1.value = {
    centerList: [
      { name: '证书总数', value: res.data.certificateTotal || 0 },
      { name: '需复审总数', value: res.data.certificateReexamineNum || 0 }
    ],
    lastList: [
      { name: '正常', value: res.data.certificateNormalNum || 0 },
      { name: '即将逾期', value: res.data.certificateApproachingOverdueNum || 0 },
      { name: '逾期', value: res.data.certificateOverdueNum || 0 }
    ]
  }
}
// 人员证书
const getPersonCertificateData = async () => {
  let params = {
    relId: route.query.id,
    type: '0'
  }
  const res = await ResumeAPI.getCertificate(params)
  option2.value = {
    centerList: [
      { name: '人员证书总数', value: res.data.certificateTotal || 0 },
      { name: '需复审总数', value: res.data.certificateReexamineNum || 0 }
    ],
    lastList: [
      { name: '正常', value: res.data.certificateNormalNum || 0 },
      { name: '即将逾期', value: res.data.certificateApproachingOverdueNum || 0 },
      { name: '逾期', value: res.data.certificateOverdueNum || 0 }
    ]
  }
}

// 责任制签署
const getResponsibilitySignData = async () => {
  const res = await ResumeAPI.getResponsibilitySign({ relId: route.query.id })
  option3.value = {
    centerList: [{ name: '需签署', value: res.data.total || 0 }],
    lastList: [
      { name: '已签署', value: res.data.signingNum || 0 },
      { name: '未签署', value: res.data.unSigningNum || 0 }
    ]
  }
}

// 入场培训
const getEntryTrainingData = async () => {
  const res = await ResumeAPI.getEntryTraining({ relId: route.query.id })
  if (res.data) {
    // 找到未完成和已完成的数据
    const unfinishedData = res.data.find((item) => item.code === '0') || { total: 0, rate: '0%' }
    const finishedData = res.data.find((item) => item.code === '1') || { total: 0, rate: '0%' }

    trainingOption.value = {
      unfinished: {
        count: unfinishedData.total,
        percentage: parseInt(unfinishedData.rate) || 0
      },
      finished: {
        count: finishedData.total,
        percentage: parseInt(finishedData.rate) || 0
      }
    }
  }
}

// 获取规章制度查阅数据
const getRulesData = async () => {
  const res = await ResumeAPI.getRegulationReading({ relId: route.query.id })
  if (res.data) {
    const unfinishedData = res.data.find((item) => item.code === '0') || { total: 0, rate: '0%' }
    const finishedData = res.data.find((item) => item.code === '1') || { total: 0, rate: '0%' }

    rulesOption.value = {
      unfinished: {
        count: unfinishedData.total,
        percentage: parseInt(unfinishedData.rate) || 0
      },
      finished: {
        count: finishedData.total,
        percentage: parseInt(finishedData.rate) || 0
      }
    }
  }
}

// 获取三级安全记录数据
const getSafetyData = async () => {
  const res = await ResumeAPI.getThreeLevelSafetyRecord({ relId: route.query.id })
  if (res.data) {
    const unfinishedData = res.data.find((item) => item.code === '0') || { total: 0, rate: '0%' }
    const finishedData = res.data.find((item) => item.code === '1') || { total: 0, rate: '0%' }

    safetyOption.value = {
      unfinished: {
        count: unfinishedData.total,
        percentage: parseInt(unfinishedData.rate) || 0
      },
      finished: {
        count: finishedData.total,
        percentage: parseInt(finishedData.rate) || 0
      }
    }
  }
}

// 应急预案
const getEmergencyPlanData = async () => {
  const res = await ResumeAPI.getEmergencyPlan({ relId: route.query.id })
  if (res.data) {
    const dataMap = {
      '1': 'comprehensive', // 综合应急类
      '2': 'special', // 专项应急预案
      '3': 'onSite', // 现场处置预案
      '4': 'other' // 其他类别预案
    }

    res.data.forEach((item) => {
      const key = dataMap[item.code]
      if (key) {
        emergencyOption.value[key] = {
          total: item.total || 0
        }
      }
    })
  }
}

// 修改初始化方法，添加新的数据获取
const initData = async () => {
  await Promise.all([
    getCertificateData(),
    getPersonCertificateData(),
    getResponsibilitySignData(),
    getEntryTrainingData(),
    getRulesData(),
    getSafetyData(),
    getEmergencyPlanData()
  ])
}

// 创建计划
defineOptions({ name: 'resumeCom' })
onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.resume {
  min-height: 100vh;
  color: #000;

  .right {
    width: calc(100% - 35%);
  }
}
</style>
