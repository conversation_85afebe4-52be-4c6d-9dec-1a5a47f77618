<template>
  <div class="w_personnel_box_com">
    <div class="w_personnel_tabs_com_box">
      <responsibility />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import responsibility from './comp/responsibility.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

// 创建计划
defineOptions({ name: 'responsibility' })
</script>

<style scoped lang="scss">
</style>
