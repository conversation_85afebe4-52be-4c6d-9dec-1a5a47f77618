<template>
  <div class="responsebility-contaier">
    <el-table :data="tableData" class="table-box" stripe>
      <el-table-column type="index" width="60" label="序号" align="center" />
      <el-table-column prop="name" width="130" label="姓名" align="center" />
      <el-table-column prop="phone" width="160" label="手机号" align="center" />
      <el-table-column prop="account" width="160" label="账号" align="center" />
      <el-table-column prop="depart" width="220" label="部门" align="center" />
      <!-- <el-table-column prop="post" width="200" label="岗位" align="center">
        <template #default="scope">
          <span>{{ scope.row.post ? scope.row.post : '--' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column width="200" label="签字文件" align="center">
        <template #default="scope">
          <div v-if="scope.row.fileInfo.length" class="file-link">
            <div v-if="isImage(scope.row.fileInfo[0].suffix)">
              <!--是图片-->
              <imgViewList :img-list="scope.row.fileInfo || []" />
            </div>
            <div v-else>
              <!--不是图片-->
              <el-tooltip
                v-for="item in scope.row.fileInfo"
                class="box-item"
                effect="dark"
                :content="item.fileName"
                placement="top"
              >
                <span
                  v-html="
                    generateHTML(
                      item.fileName,
                      item.filePath,
                      `${item.fileName < 13 ? item.fileName : item.fileName.slice(0, 13) + '...'}`
                    )
                  "
                ></span>
              </el-tooltip>
            </div>
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column width="130" label="上传状态" align="center">
        <template #default="scope">
          <span v-if="scope.row.status === '0'" style="color: red">未上传</span>
          <span v-else style="color: #70c995">已上传</span>
        </template>
      </el-table-column>
      <el-table-column width="220" label="上传时间" align="center">
        <template #default="scope">
          {{ scope.row.upLoadTime ? scope.row.upLoadTime : '--' }}
        </template>
      </el-table-column>
    </el-table>
    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="params.pageNo"
        v-model:page-size="params.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNumber"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { formatDate } from '~/common/utils/tools.ts'
import $API from '~/common/api'
import { useRoute } from 'vue-router'
interface IParams {
  unitId: string
  isBlack: string
  sysCode: string
  pageNo: number
  pageSize: number
}

const params = reactive<IParams>({
  sysCode: 'web',
  isBlack: '0',
  unitId: '',
  roleMarks: 'qyfzr',
  pageNo: 1,
  pageSize: 10
})
const route = useRoute()
const tableData: any[] = ref([])
const totalNumber: number = ref(1)

const getTableList = async () => {
  let res: any = await $API.post({
    url: '/edu-inter-server/responsibilitySystemSign/queryResponsibilitySystemSigning',
    data: params
  })

  if (res.code === 'success') {
    const { total, rows } = res.data
    nextTick(() => {
      totalNumber.value = total
      tableData.value = rows || []
    })
  }
}

const isImage = (ext: string) => {
  const imageTypes = ['.jpg', '.jpeg', '.png', '.gif']
  if (imageTypes.includes(ext)) {
    return true
  }
  return false
}

const handleSizeChange = (val: number) => {
  params.pageSize = val
  params.pageNo = 1
  getTableList()
}

const handleCurrentChange = (page: number) => {
  params.pageNo = page
  getTableList()
}

onMounted(() => {
  const { id } = route.query
  params.unitId = id
  getTableList()
})
</script>

<style lang="scss" scoped>
.responsebility-contaier {
  padding: 20px;
  background-color: #fff;

  .file-link {
    span {
      display: block;
      color: #1890ff;
      font-size: 14px;
      line-height: 18px;
      cursor: pointer;
    }
  }

  .table-box {
    margin-bottom: 20px;
  }

  .w_page_num {
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table__header),
:deep(.el-table__body) {
  width: 100% !important;
}
</style>
