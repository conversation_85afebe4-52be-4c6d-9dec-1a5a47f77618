<template>
  <div class="responsebility-contaier">
    <el-table :data="tableData" class="table-box" stripe>
      <el-table-column type="index" width="60" label="序号" align="center" fixed="left" />
      <el-table-column prop="ruleName" width="160" label="制度/规程名称" align="center">
        <template #default="scope">
          <el-tooltip class="box-item" effect="dark" :content="scope.row.ruleName" placement="top">
            {{ scope.row.ruleName.length > 20 ? scope.row.ruleName.slice(0, 20) + '...' : scope.row.ruleName }}
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column width="140" label="所属类型" align="center">
        <template #default="scope">
          <span>{{ scope.row.ruleType == '1' ? '操作规程' : '规章制度' }}</span>
        </template>
      </el-table-column>
      <el-table-column width="220" label="签发版" align="center">
        <template #default="scope">
          <div v-if="scope.row.fileType == '1'">
            <!--文档-->
            <div v-if="scope.row.fileList.length" class="file-link cursor-pointer">
              <el-tooltip
                v-for="item in scope.row.fileList"
                class="box-item"
                effect="dark"
                :key="item.id"
                :content="item.fileName"
                placement="top"
              >
                <span
                  @click="preview(scope.row)"
                  v-html="
                    generateHTML(
                      item.fileName,
                      item.filePath,
                      `${item.fileName.length < 13 ? item.fileName : item.fileName.slice(0, 13) + '...'}`
                    )
                  "
                ></span>
              </el-tooltip>
            </div>
          </div>
          <imgViewList v-else :img-list="scope.row.fileList || []" @click="preview(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column prop="shouldLookCount" width="140" label="应查阅总人次" align="center" />
      <el-table-column prop="finishedLookCount" width="140" label="已查阅人次" align="center" />
      <el-table-column prop="unFinishedLookCount" width="160" label="未查阅人次" align="center" />
      <el-table-column prop="createName" width="100" label="上传人" align="center" />
      <el-table-column width="200" label="上传时间" align="center">
        <template #default="scope">
          <span>{{ formatDate(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" plain @click="preview(scope.row)">预览</el-button>
          <el-button plain color="red" @click="deleteItem(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="params.pageNo"
        v-model:page-size="params.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNumber"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!--图片展示-->
    <div class="demo-image__preview">
      <el-image
        ref="imageRef"
        id="image-comp"
        style="width: 0; height: 0"
        :src="url"
        :preview-src-list="urls"
        :initial-index="4"
        teleported
        fit="cover"
        lazy
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import imgViewList from '@/components/imgViewList/index.vue'
import config from '@/config'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { defineExpose, nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import $API from '~/common/api'
import { generateHTML } from '~/common/utils/preview'
import { formatDate } from '~/common/utils/tools'

interface IParams {
  unitId: string
  pageNo: number
  pageSize: number
}

const params = reactive<IParams>({
  unitId: '',
  pageNo: 1,
  pageSize: 10
})
const tableData = ref<any>([])
const totalNumber = ref<number>(1)
const route = useRoute()
const router = useRouter()
const imageRef = ref(null)
const url = ref<string>('')
const urls = ref<string[]>([])

const handleCurrentChange = (page: number) => {
  params.pageNo = page
  getTableList()
}

const handleSizeChange = (val: number) => {
  params.pageSize = val
  params.pageNo = 1
  getTableList()
}

const getTableList = async () => {
  const unitId = route.query.id
  params.unitId = unitId
  let res: any = await $API.post({
    url: '/edu-inter-server/regulations/infoPage',
    data: params
  })

  if (res.code === 'success') {
    const { total, rows } = res.data
    totalNumber.value = total
    tableData.value = rows || []
  }
}

const deleteItem = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        let res: any = await $API.get({
          url: `/edu-inter-server/regulations/infoDel?id=${id}`
        })
        if (res.code == 'success') {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getTableList()
          done()
        }
      } else {
        done()
      }
    }
  })
}

function preViewPdf(file) {
  const { fileList } = file
  const newList = cloneDeep(fileList)
  const newUrl = config.downloadFileUrl + newList[0].filePath
  window.open(
    router.resolve({
      path: '/preview',
      query: { value: newUrl }
    }).href,
    '_blank'
  )
}

const preview = (obj: any) => {
  console.log('obj', obj)
  const { fileList } = obj

  // 统计
  // statistics(obj)

  if (fileList.length > 0) {
    const imageList = ['.jpg', '.jpeg', '.png', '.gif']
    const ext = fileList[0].suffix?.toLowerCase()
    console.log('ext', imageList.includes(ext))
    if (imageList.includes(ext)) {
      // 图片
      const newList = cloneDeep(fileList)
      url.value = config.downloadFileUrl + newList[0].filePath
      urls.value = newList.map((item: any) => {
        return config.downloadFileUrl + item.filePath
      })

      nextTick(() => {
        const elem: any = imageRef.value
        if (elem) {
          elem.showPreview() // 调用 el-image 的预览方法
        }
      })
    } else if (['.doc', '.docx', '.ppt', '.pptx'].includes(ext)) {
      // 文档
      preViewPdf(obj)
    } else {
      // 其他文件格式比如pdf、图片、html
      window.open(config.downloadFileUrl + fileList[0].filePath)
    }
  } else {
    // 没有文件可以预览
  }
}

onMounted(() => {
  // 获取数据列表信息
  getTableList()
})

defineExpose({
  getTableList
})
</script>

<style lang="scss" scoped>
.responsebility-contaier {
  background-color: #fff;

  .table-box {
    margin-bottom: 20px;
  }

  .w_page_num {
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table__header),
:deep(.el-table__body) {
  width: 100% !important;
}
</style>
