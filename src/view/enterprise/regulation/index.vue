<template>
  <div class="regulation-container">
    <div class="common-header-nav">
      <span></span>
      <el-button type="primary" @click="addRegulation" color="#527CFF">+ 新增</el-button>
    </div>
    <div>
      <regulationTable ref="regulationRef" />
    </div>

    <!--抽屉弹出框-->
    <el-drawer v-model="visible" :direction="right" :before-close="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>新增制度规程</div>
        </div>
      </template>
      <el-form ref="ruleFormRef" :model="params" :rules="rules" label-width="120px" class="demo-ruleForm" status-icon>
        <el-form-item label="所属类型" prop="ruleType">
          <el-select-v2 v-model="params.ruleType" placeholder="请选择所属类型" :options="options" />
        </el-form-item>
        <el-form-item label="制度规程名称" prop="ruleName">
          <el-input v-model="params.ruleName" maxlength="30" placeholder="请输入制度规程名称" />
        </el-form-item>
        <el-form-item label="上传签发文件" prop="fileList" ref="fileRefs">
          <uploadFiles @eventFile="eventFile" :list="params.fileList" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import uploadFiles from '@/components/uploadFiles/cormal.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import regulationTable from './comp/regulationTable.vue'
const visible = ref<boolean>(false)
const ruleFormRef = ref<FormInstance>()
const regulationRef = ref(null)
const fileRefs = ref(null)
const route = useRoute()
const userInfo = useUserInfo()
const options = ref([
  { label: '操作规程', value: '1' },
  { label: '规章制度', value: '0' }
])

const params = reactive({
  id: '',
  ruleType: '',
  fileType: '1',
  ruleName: '',
  unitId: '',
  userId: '',
  userName: '',
  fileList: []
})

const rules = reactive<FormRules>({
  ruleType: [{ required: true, trigger: 'blur', message: '请选择所属类型' }],
  ruleName: [{ required: true, trigger: 'blur', message: '请输入制度规程名称' }],
  fileList: [{ required: true, message: '请上传签发文件' }]
})

const eventFile = (fileList) => {
  params.fileList = fileList

  if (fileList.length > 0) {
    fileRefs.value.clearValidate()
  }
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      const extension = params.fileList[0]?.suffix
      const imageTypes = ['.jpg', '.jpeg', '.png', '.gif']
      if (imageTypes.includes(extension)) {
        params.fileType = '0'
      } else {
        params.fileType = '1'
      }

      // 创建规章制度
      createRegulation()
      console.log(params, 'params')
    }
  })
}

const createRegulation = async () => {
  let res: any = await $API.post({
    url: '/edu-inter-server/regulations/infoAdd',
    data: params
  })

  if (res.code === 'success') {
    ElMessage({
      message: `新增规章制度成功`,
      type: 'success'
    })

    // 重置表单
    resetForm()
    params.fileList = []
    visible.value = false
    // 调用获取规章制度列表接口
    regulationRef.value.getTableList()
  } else {
    ElMessage({
      message: res.message,
      type: 'error'
    })
  }
}

const resetForm = () => {
  nextTick(() => {
    params.id = ''
    params.ruleType = ''
    params.fileType = '1'
    params.ruleName = ''
    params.fileList = []
    ruleFormRef.value.clearValidate()
  })
}

const addRegulation = () => {
  visible.value = true
  resetForm()
}

const handleClose = () => {
  visible.value = false
}

onMounted(() => {
  const { id } = route.query
  params.unitId = id
  params.userId = userInfo.value.id
  params.userName = userInfo.value.userName
})
// 创建计划
defineOptions({ name: 'regulationCom' })
</script>

<style scoped lang="scss">
.demo-ruleForm {
  border: 1px solid #e4e5eb;
  padding: 20px;
  border-radius: 3px;
}

.regulation-container {
  padding: 20px;
  background-color: #fff;

  .common-header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
}

.w_add_plan_header {
  font-weight: 700;
  color: #333;
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: start;
  align-items: center;

  div:first-of-type {
    /* 样式规则 */
    width: 18px;
    height: 12px;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    background-size: 100% 100%;
  }
}

.w_dialog_foot {
  height: 72px;
  display: flex;
  justify-content: end;
  align-items: center;

  .w_btn {
    height: 32px;
  }

  .w_btn_bg {
    background-color: rgba(82, 124, 255, 1) !important;
  }
}

:deep(.el-table__header .el-table__cell) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}

:deep(.el-table__header-wrapper tr th.el-table-fixed-column--right) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding-top: 0;
  border-bottom: 1px solid rgb(235, 238, 245);
}

:deep(.el-drawer__footer) {
  padding: 0 10px;
  border-top: 1px solid rgba(235, 238, 245, 1) !important;
}

:deep(.el-drawer__body) {
  margin-top: -1·0px;
}
</style>
