<template>
  <div class="responsebility-contaier">
    <el-table :data="tableData" class="table-box" stripe>
      <el-table-column type="index" width="60" label="序号" align="center" fixed="left" />
      <el-table-column width="200" label="应急预案名称" align="center">
        <template #default="scope">
          <el-tooltip class="box-item" effect="dark" :content="scope.row.planName" placement="top">
            <span>{{
              scope.row.planName.length <= 13 ? scope.row.planName : scope.row.planName.slice(0, 13) + '...'
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="fileType" width="140" label="文件类型" align="center" />
      <el-table-column width="220" label="文件" align="center">
        <template #default="scope">
          <div v-if="scope.row.fileInfo.length" class="file-link">
            <div v-if="isImage(scope.row.fileInfo[0].suffix)">
              <!--是图片-->
              <imgViewList :img-list="scope.row.fileInfo || []" />
            </div>
            <div v-else>
              <!--不是图片-->
              <el-tooltip
                v-for="(item, index) in scope.row.fileInfo"
                class="box-item"
                effect="dark"
                :content="item.fileName"
                placement="top"
              >
                <span
                  @click="preview(scope.row, index)"
                  v-html="
                    generateHTML(
                      item.fileName,
                      item.filePath,
                      `${item.fileName.length < 13 ? item.fileName : item.fileName.slice(0, 13) + '...'}`
                    )
                  "
                ></span>
              </el-tooltip>
            </div>
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column width="240" label="适用范围" align="center">
        <template #default="scope">
          <el-tooltip class="box-item" effect="dark" :content="scope.row.scopeOfApplication" placement="top">
            <span>{{
              scope.row.scopeOfApplication.length <= 13
                ? scope.row.scopeOfApplication
                : scope.row.scopeOfApplication.slice(0, 13) + '...'
            }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="createName" width="140" label="上传人" align="center" />
      <el-table-column width="200" label="上传时间" align="center">
        <template #default="scope">
          {{ scope.row.upLoadTime ? scope.row.upLoadTime : '--' }}
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button type="primary" plain @click="editEvent(scope.row)">编辑</el-button>
          <el-button plain color="red" @click="deleteItem(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="w_page_num">
      <el-pagination
        class="page_r"
        v-model:currentPage="params.pageNo"
        v-model:page-size="params.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        :total="totalNumber"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import $API from '~/common/api'
import { ref, reactive, onMounted, defineExpose, nextTick, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import imgViewList from '@/components/imgViewList/index.vue'
import { generateHTML } from '~/common/utils/preview'
import { useRoute, useRouter } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import config from '@/config'

const route = useRoute()
const router = useRouter()
interface IParams {
  pageNo: number
  pageSize: number
  unitId: string
}

const params = reactive<IParams>({
  unitId: '',
  pageNo: 1,
  pageSize: 10
})
const tableData: any[] = ref([])
const totalNumber: number = ref(1)
const emits = defineEmits(['editEvent'])

const handleCurrentChange = (page: number) => {
  params.pageNo = page
  getTableList()
}
const getTableList = async () => {
  let res: any = await $API.post({
    url: '/edu-inter-server/responsibilitySystemSign/queryEmergencyPlanPage',
    data: params
  })

  if (res.code === 'success') {
    const { total, rows } = res.data
    nextTick(() => {
      totalNumber.value = total
      tableData.value = rows || []
    })
  }
}

const deleteItem = (id: string) => {
  ElMessageBox({
    title: '提示',
    message: '确认删除吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        let res: any = await $API.get({
          url: `/edu-inter-server/responsibilitySystemSign/deleteInfo?id=${id}`
        })
        if (res.code == 'success') {
          ElMessage({
            message: '删除成功',
            type: 'success'
          })
          getTableList()
          done()
        } else {
          ElMessage.error('删除失败')
        }
      } else {
        done()
      }
    }
  })
}

const handleSizeChange = (val: number) => {
  params.pageSize = val
  params.pageNo = 1
  getTableList()
}

const isImage = (ext: string) => {
  const imageTypes = ['.jpg', '.jpeg', '.png', '.gif']
  if (imageTypes.includes(ext)) {
    return true
  }
  return false
}

function preview(file, index) {
  const { fileInfo } = file
  const newList = cloneDeep(fileInfo)
  const newUrl = config.downloadFileUrl + newList[index].filePath
  if (['.doc', '.docx', '.ppt', '.pptx', 'doc', 'docx', 'ppt', 'pptx'].includes(newList[index].suffix)) {
    window.open(
      router.resolve({
        path: '/preview',
        query: { value: newUrl }
      }).href,
      '_blank'
    )
  } else {
    //其他文件格式比如pdf、图片、html
    window.open(config.downloadFileUrl + newList[index].filePath)
  }
}

const editEvent = (row: any) => {
  emits('editEvent', row)
}

onMounted(() => {
  const { id } = route.query
  params.unitId = id
  getTableList()
})

defineExpose({
  getTableList
})
</script>

<style lang="scss" scoped>
.responsebility-contaier {
  background-color: #fff;

  .table-box {
    margin-bottom: 20px;
  }

  .file-link {
    span {
      display: block;
      color: #1890ff;
      font-size: 14px;
      line-height: 18px;
      cursor: pointer;
      margin: 0 5px;
    }
  }

  .w_page_num {
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table__header),
:deep(.el-table__body) {
  width: 100% !important;
}
</style>
