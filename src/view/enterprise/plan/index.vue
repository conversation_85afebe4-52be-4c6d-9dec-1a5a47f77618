<template>
  <div class="regulation-container">
    <div class="common-header-nav">
      <span></span>
      <el-button type="primary" @click="addPlan" color="#527CFF">+ 新增</el-button>
    </div>
    <!--应急预案表格-->
    <planTable ref="plantableRefs" @editEvent="editEvent" />

    <!--新增应急预案抽屉-->
    <el-drawer v-model="visible" :direction="right" :before-close="handleClose">
      <template #header>
        <div class="w_add_plan_header">
          <div class="mr-11px"></div>
          <div>{{ title }}</div>
        </div>
      </template>
      <el-form ref="ruleFormRef" class="demo-ruleForm" :model="params" :rules="rules" label-width="120px" status-icon>
        <el-form-item label="文件类型" prop="fileType">
          <div class="type-label">
            <el-select v-model="params.fileType" placeholder="请选择文件类型" style="width: 302px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="应急预案名称" prop="planName">
          <el-input type="text" v-model="params.planName" placeholder="请输入应急预案名称" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="应急预案附件" prop="fileInfo" ref="fileRefs">
          <uploadFilesCormal :list="params.fileInfo" @eventFile="eventFile" />
        </el-form-item>
        <el-form-item label="适用范围" prop="scopeOfApplication">
          <el-input v-model="params.scopeOfApplication" rows="5" type="textarea" maxlength="100" placeholder="请输入适用范围"
            show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="w_dialog_foot">
          <el-button class="w_btn" @click="handleClose" plain>取消</el-button>
          <el-button class="w_btn w_btn_bg" type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, nextTick, onMounted } from 'vue'
import $API from '~/common/api'
import { useUserInfo } from '@/store'
import planTable from './comp/planTable.vue'
import type { FormRules, FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import uploadFilesCormal from '@/components/uploadFiles/cormal.vue'
// image/jpg,image/jpeg,image/png,image/gif,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf
// 只能上传doc，docx, pdf
const accept = ref<string>(
  'application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf'
)
const visible = ref<boolean>(false)
const ruleFormRef = ref<FormInstance>()
const route = useRoute()
const userInfo = useUserInfo()
const plantableRefs = ref(null)
const fileRefs = ref(null)
const title = ref('新增应急预案')
const options = reactive([
  { value: '综合应急类', label: '综合应急类' },
  { value: '专项应急预案', label: '专项应急预案' },
  { value: '现场处置预案', label: '现场处置预案' },
  { value: '其他类别预案', label: '其他类别预案' },
])
const params = reactive({
  id: '',
  fileType: '',
  planName: '',
  createId: '',
  createName: '',
  updateId: '',
  updateName: '',
  unitId: '',
  scopeOfApplication: '',
  fileInfo: [],
})

const rules = reactive<FormRules>({
  fileType: [{ required: true, trigger: 'change', message: '请选择文件类型' }],
  planName: [{ required: true, trigger: 'blur', message: '请输入应急预案名称' }],
  fileInfo: [{ required: true, message: '请上传应急预案附件' }],
  scopeOfApplication: [{ required: true, trigger: 'blur', message: '请输入适用范围' }],
})

const eventFile = (fileList) => {
  params.fileInfo = fileList
  if (fileList.length > 0) {
    fileRefs.value.clearValidate()
  }
}

const addPlan = () => {
  visible.value = true
  title.value = '新增应急预案'
  resetForm()
}

const createAndUpdate = async () => {
  let res: any = await $API.post({
    url: '/edu-inter-server/responsibilitySystemSign/saveOrUpdate',
    data: params,
  })

  if (res.code === 'success') {
    ElMessage({
      message: `${title.value}成功`,
      type: 'success',
    })

    // 重置表单
    resetForm()
    visible.value = false
    // 调用获取应急预案列表接口
    plantableRefs.value.getTableList()
  } else {
    ElMessage({
      message: res.message,
      type: 'error',
    })
  }
}

const resetForm = () => {
  nextTick(() => {
    params.id = ''
    params.fileType = ''
    params.planName = ''
    params.scopeOfApplication = ''
    params.fileInfo = []
    ruleFormRef.value.clearValidate()
  })
}

const submitForm = async (formEl: FormInstance | undefined) => {
  console.log(userInfo.value, 'userInfo')
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      const { id, userName } = userInfo.value
      if (params.id) {
        params.updateId = id
        params.updateName = userName
      } else {
        params.createId = id
        params.createName = userName
      }
      // 本地模拟数据
      // params.fileInfo = [
      //   {
      //     fileName: "9月反恐应急演练（王亭）.pdf",
      //     filePath: "https://agjp.tanzervas.com/aqsc/v1/ycsy/file1/files/ehs/null/ehs-emergency/2024-10-23/236c586fec0a42e190bd0fc62032595e.pdf",
      //     fileSize: "1108418",
      //     id: "1848756678711656449",
      //     suffix: ".pdf"
      //   },
      //   {
      //     fileName: "测试doc预览.docx",
      //     filePath: "https://agjp.tanzervas.com/aqsc/v1/ycsy/file1/files/ehs/null/ehs-emergency/2024-10-25/9e2e1cd002c14f65a4ff74df00fa339b.docx",
      //     fileSize: "1108418",
      //     id: "633348438923623633",
      //     suffix: ".docx"
      //   }
      // ]
      // 创建或者编辑
      createAndUpdate()
      console.log(params, 'params')
    }
  })
}

const handleClose = () => {
  // 重置表单
  ruleFormRef.value.resetFields()
  visible.value = false
}

const editEvent = (row: any) => {
  title.value = '编辑应急预案'
  visible.value = true
  const { id, createId, fileType, planName, createName, scopeOfApplication } = row
  params.fileInfo = []
  nextTick(() => {
    params.id = id
    params.createId = createId
    params.createName = createName
    params.fileType = fileType
    params.planName = planName
    params.scopeOfApplication = scopeOfApplication
    params.fileInfo = row.fileInfo.map((item) => {
      return {
        name: item.fileName,
        url: item.filePath,
        size: item.fileSize,
        ...item,
      }
    })
  })
}

onMounted(() => {
  const { id } = route.query
  params.unitId = id
})

// 创建计划
defineOptions({ name: 'planCom' })
</script>

<style scoped lang="scss">
.demo-ruleForm {
  border: 1px solid #e4e5eb;
  padding: 20px;
  border-radius: 3px;
}

.regulation-container {
  padding: 20px;
  background-color: #fff;

  .common-header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .type-label {
    display: flex;
    flex-wrap: wrap;

    label {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 10px;

      input {
        margin-right: 4px;
      }
    }
  }
}

.w_add_plan_header {
  font-weight: 700;
  color: #333;
  width: 100%;
  height: 54px;
  display: flex;
  justify-content: start;
  align-items: center;

  div:first-of-type {
    /* 样式规则 */
    width: 18px;
    height: 12px;
    background: url('@/assets/image/drawer_bg.png') no-repeat;
    background-size: 100% 100%;
  }
}

.w_dialog_foot {
  height: 72px;
  display: flex;
  justify-content: end;
  align-items: center;

  .w_btn {
    height: 32px;
  }

  .w_btn_bg {
    background-color: rgba(82, 124, 255, 1) !important;
  }
}

:deep(.el-table__header .el-table__cell) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-table .el-table__cell) {
  z-index: revert-layer;
}

:deep(.el-table__header-wrapper tr th.el-table-fixed-column--right) {
  background-color: rgba(245, 246, 249, 1);
  /* 表头背景色 */
  color: #606266;
  /* 表头字体颜色 */
  font-size: 14px;
  /* 表头字体大小 */
  height: 48px;
}

:deep(.el-drawer) {
  border-radius: 10px 0 0 10px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding-top: 0;
  border-bottom: 1px solid rgb(235, 238, 245);
}

:deep(.el-drawer__footer) {
  padding: 0 10px;
  border-top: 1px solid rgba(235, 238, 245, 1) !important;
}

:deep(.el-drawer__body) {
  margin-top: -1·0px;
}
</style>
