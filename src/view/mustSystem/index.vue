<template>
  <div class="w_main_box">
    <div class="w_server_title_box flex items-center pl-[80px]">
      <span class="text-[#527CFF] font-bold italic">温馨提示</span>
      <span class="ml-[13px] must">
        {{
          ui.zhLogo === 'yanchang'
            ? '下方列表的必遵制度将根据承包商类型自动发送到承包商企业员工端，以便员工进行查阅学习，在承包商履历详情中可以查看已学数据分析。'
            : '下方列表的必遵制度将根据相关方类型自动发送到相关方企业员工端，以便员工进行查阅学习，在相关方履历详情中可以查看已学数据分析。'
        }}
      </span>
    </div>
    <serverCom />
  </div>
</template>
<script setup lang="ts">
import serverCom from './comp/serverCom.vue'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
defineOptions({ name: 'PnlReMtIndex' }) // 人员履历
</script>

<style scoped lang="scss">
.w_main_box {
  width: 100%;
  height: 100%;
  /* flex: 1; */

  .crumbs_box {
    background-color: white;
  }

  .w_btn_bg {
    text-align: center;
    background: #527cff;
    color: white;
  }

  .w_server_title_box {
    font-family: MyCustomFont, sans-serif;
    width: 100%;
    height: 66px;
    margin-bottom: 10px;
    background: url('@/assets/img/must-bj.png') no-repeat;
    background-size: 100% 100%;
    line-height: 54px;
    font-size: 20px;

    .must {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #061032;
    }
  }
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

.ifm-child {
  .w_main_box {
    @apply flex flex-col;
    .work-flow {
      @apply flex-1 h-0;
    }
  }
}
</style>
