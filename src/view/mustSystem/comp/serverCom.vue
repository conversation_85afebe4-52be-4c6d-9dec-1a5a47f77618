<template>
  <div class="work-flow flex">
    <el-card
      v-if="ui.unitOrgType !== '1'"
      class="mr-[20px] org-tree"
      :class="!collapsed ? '' : 'p-20px'"
      :style="{ width: collapsed ? '323px' : '0' }"
    >
      <train-tree :collapsed="collapsed" @serach="searchData" type="must" />
    </el-card>
    <div class="w_server_content relative">
      <div class="w_server_title_box">
        <el-form :inline="true" :model="filterForm" class="demo-form-inline">
          <el-form-item :label="ui.zhLogo === 'yanchang' ? '承包商类型筛选' : '相关方类型筛选'">
            <el-select
              v-model="filterForm.interType"
              :placeholder="ui.zhLogo === 'yanchang' ? '请选择承包商类型' : '请选择相关方类型'"
              clearable
              @change="selectChange"
              style="width: 200px"
            >
              <el-option v-for="item in relevantTypeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="filterForm.keySearch"
              placeholder="请输入制度关键词搜索"
              clearable
              :suffix-icon="Search"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item class="float-right">
            <el-button @click="addSys" type="primary" color="#527CFF">+ 新增</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="w_server_table">
        <el-table :data="tableData" stripe style="width: 100%" fit>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column
            prop="interTypeName"
            :label="ui.zhLogo === 'yanchang' ? '适用承包商类型名称' : '适用相关方类型名称'"
            align="center"
            show-overflow-tooltip
            width="150"
          >
            <template #default="scope">
              {{ scope.row.interTypeName || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="ruleTypeName" label="制度类型" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.ruleTypeName || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="ruleName" label="必遵制度" align="center" show-overflow-tooltip width="240">
            <template #default="scope">
              <div
                class="flex items-center justify-center"
                style="width: 100%; overflow: hidden; cursor: pointer"
                @click="fileUpdown(scope.row)"
              >
                <!-- <svg-icon name="PDF" size="30"></svg-icon> -->
                <div class="w-[30px] mr-[5px]">
                  <img :src="getDocuImg(scope.row.fileAttachment.suffix)" alt="" />
                </div>
                <span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #527cff">
                  {{ scope.row.fileAttachment.fileName || '--' }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.createTime || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="createUserName" label="创建人" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.createUserName || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="readCount" label="阅读总次数" align="center" show-overflow-tooltip />
          <el-table-column label="操作" fixed="right" width="280" align="center">
            <template #default="scope">
              <el-button type="primary" plain size="small" @click="fileUpdown(scope.row)">预览</el-button>
              <el-button type="primary" plain size="small" @click="joinBlacklist(scope.row)">编辑</el-button>
              <el-button type="danger" plain size="small" @click="deleteSys(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="w_server_page_num">
        <el-pagination
          v-model:currentPage="pageNo"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="expand" @click="collapsedTree" v-if="ui.unitOrgType !== '1'"></div>
    </div>
    <!-- 删除弹框 -->
    <el-dialog v-model="deleteVisible" width="488" height="290" @close="deleteClose">
      <template #header>
        <titleTag title="删除必遵制度" />
      </template>
      <div class="pl-[30px] pr-[30px] !mt-[20px] delete"></div>
      <div class="text-center text-info">确定要删除吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteClose" plain>取消</el-button>
          <el-button type="primary" @click="deleteSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增 编辑-->
    <popup-side v-model="addlVisible" width="648px" :popupTitle="operate">
      <AddOrEdit
        :operate="operate"
        @close="closeFn"
        :data="trainData"
        :id="trainId"
        :orgCode="orgCode"
        :name="orgName"
      />
    </popup-side>
  </div>
</template>
<script setup lang="ts">
import TrainTree from '@/components/tree/index.vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import $API from '~/common/api'
import { useUserInfo } from '~/store'
import titleTag from '~/view/trainCourseManagement/comp/titleTag.vue'
import AddOrEdit from './AddOrEdit.vue'
// import { previewOrDownload } from './preview'
import doc from '@/assets/img/DOC.png'
import docx from '@/assets/img/DOCX.png'
import pdf from '@/assets/img/PDF.png'
import ppt from '@/assets/img/PPT.png'
import txt from '@/assets/img/TXT.png'
import xsl from '@/assets/img/XSL.png'
import config from '~/config'

const ui: any = useUserInfo()
const router = useRouter()
const collapsed = ref(true)
const ruleDialogRef = ref()
const innerVisible = ref(false)
const addlVisible = ref<boolean>(false)
const deleteVisible = ref<boolean>(false)
const isAdd = ref<boolean>(false) // 是否新增
const fileIcons = {
  '.pdf': pdf,
  '.doc': doc,
  '.docx': docx,
  '.xls': xsl,
  '.xlsx': xsl,
  '.ppt': ppt,
  '.pptx': ppt,
  '.txt': txt
}
const defaultIcon = pdf
const trainId = ref('') // 编辑id
const operate = ref('')
const trainData = ref<any>({}) // 详情
const tableData = ref<any>([])
const total = ref<number>(0)
const pageNo = ref<number>(1)
const pageSize = ref<number>(20)
const deleteId = ref<string>('')
const orgCode = ref<string>(ui.value.unitId) // 选择机构id
const relevantTypeList = ref<any>([])
const orgName = ref<string>(ui.value.unitName)
const filterForm = ref({
  interType: '',
  keySearch: ''
})

const collapsedTree = () => {
  collapsed.value = !collapsed.value
}

// 接受树结构选中数据
const searchData = (obj) => {
  orgCode.value = obj.orgCode
  orgName.value = obj.orgName
  isAdd.value = obj.isAdd
  getData()
}
const handleSizeChange = () => {
  pageNo.value = 1
  getData()
}
const handleCurrentChange = () => {
  getData()
}

// 获取相关方类型
const getRelatedType = async () => {
  let params = {
    pageNo: 1,
    pageSize: -1
  }
  const res: any = await $API.get({
    url: 'edu-inter-server/coop/getCoopTypeList',
    params
  })
  if (res.code == 'success') {
    relevantTypeList.value = res.data.rows
  }
}
// 返回对应文件图标
function getDocuImg(name: string) {
  if (!name || typeof name !== 'string') {
    return defaultIcon
  }
  for (const [extension, icon] of Object.entries(fileIcons)) {
    if (name.toLowerCase().endsWith(extension)) {
      return icon
    }
  }

  return defaultIcon
}
function selectChange(val) {
  console.log('val', val)
  getData()
}
function fileUpdown(row) {
  $API
    .post({
      url: 'edu-inter-server/interRule/previewRule',
      params: { ruleId: row.id }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        const lastIndex = res.data.filePath.lastIndexOf('.')
        const suffix = res.data.filePath.substring(lastIndex + 1)
        if (['doc', 'docx', 'ppt', 'pptx'].includes(suffix)) {
          // let b64Encoded = Base64.encode(config.downloadFileUrl + '/' + res.data.filePath)
          // const url = 'http://223.247.156.85:9092/onlinePreview?url=' + encodeURIComponent(b64Encoded)
          // window.open(url, '_blank')
          const newUrl = config.downloadFileUrl + res.data.filePath
          window.open(
            router.resolve({
              path: '/preview',
              query: { value: newUrl }
            }).href,
            '_blank'
          )
        } else {
          //其他文件格式比如pdf、图片、html
          window.open(config.downloadFileUrl + res.data.filePath)
        }
      }
    })
}
// 新增
const addSys = () => {
  operate.value = '新增必遵制度'
  addlVisible.value = true
}

function closeFn() {
  addlVisible.value = false
  getData()
}

function deleteSys(v: { id: string }) {
  deleteId.value = v.id
  deleteVisible.value = true
}
// 删除确认
const deleteSubmit = () => {
  $API
    .post({
      url: 'edu-inter-server/interRule/delRule',
      params: {
        ruleId: deleteId.value
      }
    })
    .then((res: any) => {
      if (res && res.code === 'success') {
        ElMessage.success('删除成功!')
        deleteVisible.value = false
        getData()
      }
    })
}
// 关闭删除弹框
const deleteClose = () => {
  deleteVisible.value = false
  // ElMessage.success('操作成功!')
}
// 详情接口
const getTrainDetail = (id) => {
  const params = { ruleId: id }
  return new Promise((resolve, reject) => {
    $API
      .post({
        url: 'edu-inter-server/interRule/queryDetail',
        params
      })
      .then((res: any) => {
        if (res && res.code === 'success') {
          trainData.value = res.data
          resolve(trainData.value)
        } else {
          reject()
        }
      })
  })
}
async function joinBlacklist(row) {
  await getTrainDetail(row.id)
  operate.value = '编辑必遵制度'
  trainId.value = row.id
  addlVisible.value = true
}

// 获取列表
async function getData() {
  const res: any = await $API.post({
    url: 'edu-inter-server/interRule/ruledPageList',
    data: {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      orgCode: orgCode.value ? orgCode.value : ui.value.unitId, //左侧机构id
      ...filterForm.value
    }
  })

  tableData.value = res.data?.rows
  total.value = res.data?.total
}
function refreshFetch() {
  getData()
}

getData()

watch(
  () => filterForm.value.keySearch,
  () => {
    getData()
  }
)

watch(
  () => innerVisible.value,
  () => {
    if (!innerVisible.value) {
      ruleDialogRef.value?.resetFields()
    }
  }
)

onMounted(() => {
  getRelatedType()
})

defineExpose({
  refreshFetch
})
defineOptions({ name: 'PnlReMtServerCom' })
</script>

<style scoped lang="scss">
.work-flow {
  height: calc(100vh - 195px);
}

.org-tree {
  // min-width: 310px;
  background-color: #ffffff;
  overflow: hidden;

  :deep(.el-tree) {
    .el-tree-node__content {
      //   height: 40px;
      margin-top: 4px;
      border-radius: 6px;
      /* height: auto; */
      padding: 5px 0;
      white-space: pre-line;

      .el-tree-node__label {
        font-size: 16px;
      }

      .el-tree-node__expand-icon {
        font-size: 15px;

        svg {
          width: 15px;
          height: 15px;
        }
      }
    }
  }

  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(82, 124, 255, 0.1);
    color: #527cff;
  }
}

.w_server_content {
  flex: 1;
  display: grid;
  width: 100%;
  height: 100%;
  padding: 20px 24px 20px 24px;
  background-color: rgba(255, 255, 255, 1);
  grid-template-columns: 1fr;
  grid-template-rows: 50px 1fr 50px;
  grid-row-gap: 20px;
  border-radius: 5px;

  .expand {
    background: url('@/assets/expand.png') no-repeat;
    width: 34px;
    height: 36px;
    background-size: 100% 100%;
    position: absolute;
    top: 45%;
    left: -17px;
    cursor: pointer;
  }

  .w_server_title_box {
    width: 100%;
    height: 54px;
    background-size: 100% 100%;
    line-height: 54px;
  }

  .w_server_table {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    .span_cursor {
      color: #527cff;
      cursor: pointer;
    }

    :deep(.el-table__header .el-table__cell) {
      background-color: rgba(245, 246, 249, 1);
      /* 表头背景色 */
      color: #606266;
      /* 表头字体颜色 */
      font-size: 14px;
      /* 表头字体大小 */
      height: 48px;
    }
  }

  .w_server_page_num {
    display: flex;
    justify-content: end;
  }
}

.demo-form-inline .el-input {
  --el-input-width: 280px;
}

.demo-form-inline .el-select {
  --el-select-width: 280px;
}

.delete {
  width: 72px;
  height: 72px;
  background: #d9dde8;
  margin: 0 auto;
  background: url(@/assets/image/exam-delete.png) no-repeat center;
  background-size: 100% 100%;
}

.text-info {
  margin-top: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #484a4d;
}

:deep(.el-scrollbar__view) {
  height: 100%;
}

:deep(.el-card.is-always-shadow) {
  box-shadow: none !important;
  background: #eef7ff;
  border: none;
}

:deep(.el-card__body) {
  height: 100%;
  padding: 0px;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}
</style>
