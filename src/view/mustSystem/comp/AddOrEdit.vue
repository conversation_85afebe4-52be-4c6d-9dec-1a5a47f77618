<template>
  <div class="relative popup-main" v-loading="loading">
    <div class="h-full">
      <el-scrollbar>
        <div class="w_dialog_from_box">
          <!-- <div class="mb-[22px] bg_title">培训课程信息</div> -->
          <el-form :model="formInline" class="" ref="ruleFormRef" :rules="baseRule" label-width="140px">
            <el-form-item :label="ui.zhLogo === 'yanchang' ? '适用承包商类型' : '适用相关方类型'" prop="interType">
              <el-radio-group v-model="formInline.interType" :disabled="operate === '编辑必遵制度'">
                <el-radio
                  v-for="item in relevantTypeList"
                  :key="item.id"
                  :value="item.id"
                  @change="changeRadio(item.name)"
                >
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="所属类型：" prop="ruleType">
              <el-select
                v-model="formInline.ruleType"
                placeholder="请选择所属类型"
                :disabled="operate === '编辑必遵制度'"
              >
                <el-option label="规章制度" value="0"></el-option>
                <el-option label="操作规程" value="1"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="制度规程名称：" prop="ruleName">
              <el-input v-model="formInline.ruleName" placeholder="请输入制度规程名称" maxlength="30" show-word-limit />
            </el-form-item>

            <div class="upload_file" v-if="operate === '新增必遵制度'">
              <el-form-item label="必遵制度：" prop="fileName">
                <el-input v-model="formInline.fileName" v-show="false"></el-input>
                <el-upload
                  accept=".pdf,.doc,.ppt,.docx"
                  class="avatar-uploader"
                  :limit="1"
                  v-model:file-list="fileList"
                  :on-exceed="handleExceed"
                  :on-success="handleSuccess"
                  :before-upload="beforeUpload"
                  name="uploadfile"
                  :action="url"
                >
                  <div>
                    <div class="w_file_btn">+ 上传文件</div>
                    <div class="w_file_t">支持上传1个文件，支持doc，docx，pdf，ppt等格式，大小不超过30MB</div>
                  </div>
                </el-upload>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="必遵制度：">
                <div class="w_content_file">
                  <div class="mr-5 w-100 text-[#000]" v-if="fileList[0].name.length > 30">
                    <el-tooltip effect="dark" :content="fileList[0].name" placement="top-start">
                      {{ fileList[0].name.slice(0, 15) + '...' + fileList[0].name.split('.')[1] }}
                    </el-tooltip>
                  </div>
                  <div class="mr-5 w-100 text-[#000]" v-else>{{ fileList[0].name }}</div>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </el-scrollbar>
      <div class="bottom_wrap">
        <el-button @click="close" plain>取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import type { ElForm, FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import $API from '~/common/api'
import config from '~/config'
import { useUserInfo } from '~/store'

const ui = useUserInfo()
const props = defineProps({
  operate: {
    type: String,
    default: 'add'
  },
  data: {
    type: Object,
    default: () => {
      return {}
    }
  },
  id: {
    type: String,
    default: ''
  },
  orgCode: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  }
})
const loading = ref(false)
const emit = defineEmits(['close'])
const url = ref(config.update_file + '/file/uploadfile') // 上传路径
const formInline = ref({
  ruleName: '', // 制度名称
  interType: '',
  interTypeName: '',
  ruleType: '',
  ruleTypeName: '',
  fileName: '',
  ruleFileId: '' //
})
const relevantTypeList = ref<any>([])
const ruleFormRef = ref<FormInstance>()
// 文件上传列表
const fileList = ref<any>([])
const baseRule = reactive<FormRules>({
  ruleName: [
    {
      required: true,
      message: '请输入制度规程名称',
      trigger: ['blur', 'change']
    }
    // {
    //   min: 3,
    //   max: 20,
    //   message: '请输入名字数大于3小于20',
    //   trigger: ['blur', 'change'],
    //   informType: 'warning',
    // },
  ],
  ruleType: [
    {
      required: true,
      message: '请选择所属类型',
      trigger: 'change'
    }
  ],
  interType: [
    {
      required: true,
      message: ui.value.zhLogo === 'yanchang' ? '请选择适用承包商类型' : '请选择适用相关方类型',
      trigger: 'change'
    }
  ],
  fileName: [
    {
      required: true,
      message: '请上传必遵制度',
      trigger: 'change'
    }
  ]
})
//超出文件
const handleExceed = () => {
  ElMessage.error('超出最大上传文件数量')
}

const beforeUpload = (rawFile) => {
  const isValidFormat = [
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ].includes(rawFile.type)
  if (!isValidFormat) {
    ElMessage.error('仅支持txt，doc，docx，xls，xlsx，pdf，ppt，pptx等格式文件!')
    return false
  } else if (rawFile.size / 1024 / 1024 > 30) {
    ElMessage.error('文件不能大于30MB!')
    return false
  } else {
    return true
  }
}
// 上传文件
const handleSuccess = () => {
  ElMessage({
    message: '上传文件成功',
    type: 'success'
  })
}
const changeRadio = (name) => {
  formInline.value.interTypeName = name
}
// 关闭新增或删除
const close = () => {
  if (props.operate === '新增必遵制度') {
    ruleFormRef.value?.resetFields()
  }
  emit('close')
}
// 新增/编辑 提交
const add = async () => {
  if (fileList.value) {
    let fileIdArr: string[] = []
    fileList.value.forEach((item: any) => {
      fileIdArr.push(item.response.data.id as string)
    })
    formInline.value.ruleFileId = fileIdArr.join(',')
  }
  formInline.value.ruleTypeName = formInline.value.ruleType === '0' ? '规章制度' : '操作规程'
  let addData = {
    ...formInline.value,
    createOrgCode: props.orgCode,
    createOrgName: props.name
  }
  let updateData = {
    ...formInline.value,
    id: props.id
  }
  let res: any = await $API.post({
    method: 'post',
    url: 'edu-inter-server/interRule/updateRuleInfo',
    data: props.operate === '新增必遵制度' ? addData : updateData
  })
  if (res.code == 'success') {
    props.operate === '新增必遵制度' ? ElMessage.success('新增成功') : ElMessage.success('编辑成功')
    emit('close')
  } else {
    // ElMessage.error(res.message)
  }
}
const submitForm = async () => {
  try {
    await ruleFormRef.value?.validate()
    add()
  } catch (e) {
    console.log(e)
  }
}
// 获取相关方类型
const getRelatedType = async () => {
  loading.value = true
  let params = {
    pageNo: 1,
    pageSize: -1
  }
  const res: any = await $API.get({
    url: 'edu-inter-server/coop/getCoopTypeList',
    params
  })
  if (res.code == 'success') {
    loading.value = false
    relevantTypeList.value = res.data.rows
  }
}
// 编辑更新属性
const updateObj = (formObj, data) => {
  Object.keys(formObj).forEach((key) => {
    // eslint-disable-next-line no-prototype-builtins
    if (data.hasOwnProperty(key)) {
      formObj[key] = data[key]
    }
  })
}

watch(
  () => props.data,
  (val) => {
    if (val && props.operate === '编辑必遵制度') {
      updateObj(formInline.value, val)
      if (val.fileAttachment && val.fileAttachment.id) {
        fileList.value.push({
          name: val.fileAttachment.fileName,
          response: {
            data: {
              id: val.fileAttachment.id
            }
          }
        })
      }
      console.log('fileList.value', fileList.value)
    }
  },
  { immediate: true }
)
watch(
  () => fileList.value.length,
  (val) => {
    if (val > 0) {
      formInline.value.fileName = val + ''
    } else {
      formInline.value.fileName = ''
    }
  },
  { immediate: true }
)
onMounted(() => {
  getRelatedType()
})
</script>

<style lang="scss" scoped>
.popup-main {
  height: calc(100% - 60px);

  .form_wrap {
    padding: 20px;
  }
}

.error-message {
  color: red;
  font-size: 12px;
}

.w_dialog_from_box {
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #e4e5eb;
  // border-top: none;
  padding: 20px;
  margin: 24px;
  margin-bottom: 0;

  &:nth-child(2) {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  .bg_title {
    font-weight: 500;
    font-size: 16px;
    color: #19191a;
  }

  .w_file_btn {
    width: 136px;
    height: 32px;
    border: 1px solid #527cff;
    text-align: center;
    color: #527cff;
    cursor: pointer;
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
  }

  //background: red;
  .w_file_t {
    color: #a8abb2;
  }

  .w_text_div {
    display: flex;
    align-items: center;

    .w_text_left {
      margin-right: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
    }

    .text_color {
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      color: #527cff;
      width: 70px;
      height: 30px;
      border: 1px solid rgb(82, 124, 255);
      border-radius: 5px;
      text-align: center;
      line-height: 30px;
    }
  }

  .exam_paper {
    width: 100%;
    display: flex;
    justify-content: space-between;

    :deep(.el-input) {
      // width: 79%;
    }
  }

  .time_input {
    :deep(.el-input) {
      display: inline;
      margin-right: 10px;
    }
  }

  .text {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
  }

  .upload_file {
    :deep(.el-form-item__label::before) {
      color: #f56c6c;
      content: '*';
      margin-right: 4px;
    }
  }

  :deep(.el-button--primary.is-plain) {
    margin-left: 10px;
  }
}

.exam-list-box {
  height: calc(100vh - 300px);
  overflow: hidden;
  // overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding-top: 15px;
  margin-bottom: 10px;

  .exam-list {
    @apply ml-[30px] mb-[15px] pl-[16px] pb-[10px] pt-[10px] flex w-full;
    background: #ffffff;
    border: 1px solid #ebeef5;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .exam-type {
      font-weight: 500;
      font-size: 16px;
      color: #313233;
      margin-bottom: 10px;
    }

    .exam-time {
      font-weight: 400;
      font-size: 14px;
      color: #484a4d;
      margin-right: 40px;
    }
  }

  .btn {
    margin-right: 10px;
  }

  :deep(.el-select__placeholder.is-transparent) {
    color: #1e1e1e;
  }

  :deep(.el-radio) {
    margin-right: 0;
    position: absolute;
    top: 50%;
    transform: translateY(-67%);
  }
}

.bottom_wrap {
  border-top: 1px solid #ebeef5;
  box-sizing: border-box;
  padding: 10px 20px;
  text-align: right;

  .el-button--primary {
    background-color: #527cff;
    border-color: #527cff;
  }
}

.btn-page {
  width: 100%;
  margin-top: 20px;
  padding-right: 20px;
  text-align: right;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px 4px 4px 4px;
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  border: 1px #335cff solid;
  border-radius: 4px;
}

:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .btn-next) {
  border: 1px #d9d9d9 solid;
  border-radius: 4px;
}

:deep(.el-dialog) {
  padding: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 5px !important;
}

:deep(.el-dialog__header.show-close) {
  padding-right: 0 !important;
  padding: 0;
  border-bottom: 0;
}

:deep(.dialog-footer) {
  @apply pb-[20px] pr-[20px];
}

:deep(.el-overlay) {
  z-index: 2013 !important;
}

:deep(.popup-container) {
  z-index: 2099 !important;
}

:deep(.el-radio-group) {
  display: block;
}

:deep(.el-upload-list.el-upload-list--text) {
  max-width: 330px;
}
</style>
