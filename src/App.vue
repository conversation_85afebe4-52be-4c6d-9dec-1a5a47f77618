<script setup lang="ts"></script>

<template>
  <div class="h-full w-full">
    <router-view></router-view>
  </div>
</template>

<style lang="scss">
.el-table__header thead tr {
  .el-table__cell {
    background-color: rgba(187, 204, 243, 1) !important;
  }
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: rgba(223, 238, 252, 1) !important;
}
</style>
