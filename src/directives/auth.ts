import { Directive } from 'vue'
import { useUserInfo } from '@/store'
/**
 * 按钮权限校验
 * @param el
 * @param binding
 * @type :export|add|edit|delete|batchImport|auth|resetPwd|cancel|locked|其他自定义
 */
function updateElVisible(el: Element, binding: any) {
  let flag = true
  const { buttonPermission } = useUserInfo().value
  if (buttonPermission.length) {
    const [path] = binding || []
    if (buttonPermission.includes(path)) {
      flag = false
    }
  }
  if (flag) {
    el.parentNode?.removeChild(el)
  }
}

const auth: Directive<HTMLElement> = {
  mounted(el, binding) {
    updateElVisible(el, binding.value)
  },
  beforeUpdate(el, binding) {
    updateElVisible(el, binding.value)
  }
}

export default auth
