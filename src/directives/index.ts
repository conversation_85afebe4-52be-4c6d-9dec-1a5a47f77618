import { App } from 'vue'

export function registerDirectives(app: App) {
  const requireDirective: any = import.meta.glob('./*.ts', { eager: true })

  Object.keys(requireDirective).forEach((fileName) => {
    if (fileName === './index.ts') return // 忽略 index.ts 文件

    const directiveName = fileName.replace(/^\.\/(.*)\.\w+$/, '$1') // 提取指令名称
    const directive = requireDirective[fileName]?.default
    app.directive(directiveName, directive)
  })
}
