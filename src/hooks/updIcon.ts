export function setFavicon(ui: any) {
  const icon = ui.zhLogoUrl + ui.zhLogo + '.png'
  // document.title = ui.zhName
  document.title = ui.zhLogo === 'yanchang' ? '承包商管理系统' : '相关方管理系统'
  // 创建新的 link 标签
  const newLink = document.createElement('link')
  newLink.rel = 'icon'
  newLink.type = 'image/x-icon'
  newLink.href = icon
  // 获取并移除原有的 favicon link
  const oldLinks = document.querySelector('link[rel="icon"]')
  if (oldLinks) oldLinks.parentNode?.removeChild(oldLinks)
  // 将新创建的 link 插入到 head 中
  document.head.appendChild(newLink)
}
