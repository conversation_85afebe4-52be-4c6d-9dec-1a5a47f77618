import { useWindowSize } from '@vueuse/core'
import { watch } from 'vue'

/**
 * 监听窗口宽度变化
 */
export const useWatchWindowSizeWidth = (cb: any) => {
  const windowSize = useWindowSize()
  watch(() => windowSize.width.value, cb, { immediate: true })
}

/**
 * 监听窗口高度度变化
 */
export const useWatchWindowSizeHeight = (cb: any) => {
  const windowSize = useWindowSize()
  watch(() => windowSize.height.value, cb, { immediate: true })
}

export const bigWidth = 7680
