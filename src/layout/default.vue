<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 02:28:38
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 19:55:11
 * @FilePath: /angang-edu-web/src/layout/default.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="main">
    <div class="common-layout">
      <el-container>
        <el-header v-if="!isInIfm">
          <headerComponent></headerComponent>
        </el-header>
        <el-container class="main-page">
          <el-aside :width="isCollapse ? '48px' : '240px'" v-if="!isInIfm">
            <sideMenu></sideMenu>
          </el-aside>
          <!--          面包屑导航 工作台特殊处理-->
          <template v-if="router.fullPath !== '/staging' && router.fullPath !== '/partnerPicture'">
            <div class="flex-1 safety_bg">
              <div class="h-50px flex items-center pl-15px" v-if="!isInIfm">
                <breadcrumbNavigation></breadcrumbNavigation>
              </div>
              <el-main>
                <div class="h-full w-full">
                  <router-view :key="router.fullPath"></router-view>
                </div>
              </el-main>
            </div>
          </template>
          <div v-else class="flex-1 safety_bg">
            <router-view :key="router.fullPath"></router-view>
          </div>
        </el-container>
      </el-container>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import sideMenu from '@/layout/sideMenu/index.vue'
import headerComponent from '@/layout/headerComponent.vue'
import { useRoute } from 'vue-router'
import breadcrumbNavigation from './breadcrumbNavigation.vue'
import { useCounterStore } from '@/store'
const router = useRoute()
console.log('🚀 ~ sideMenu:', router)

const isInIfm = window.__IFM_ENV__ // 是否是ifm微前端环境
console.log('>>>>>>>>>>>isInIfm: ', isInIfm)
const counterStore = useCounterStore()
const isCollapse = computed(() => counterStore.isCollapse)
</script>

<style lang="scss" scoped>
.main,
.common-layout {
  height: 100%;
  overflow: hidden;

  .main-page {
    .el-aside {
      --el-aside-width: auto;
      transition: all 0.3s;
    }
  }
}

.el-header {
  padding: 0;
  height: auto;
}

.el-main {
  height: calc(100% - 65px);
  padding: 0 15px;
  overflow: hidden;
}

.el-container {
  height: 100%;
  min-width: 1280px;
}

.safety_bg {
  background-color: rgba(200, 213, 255, 1);
}

:deep(.breadcrumb-navigation) {
  padding-left: 0px !important;
}

:deep(.breadcrumb-navigation .el-breadcrumb) {
  font-size: 16px;
}

.ifm-child {
  height: 100% !important;
  html,
  body {
    height: 100% !important;
  }
}

.ifm-child .el-main {
  height: 100% !important;
  padding: 0 !important;

  :deep(.org-tree) {
    @apply flex flex-col;
    height: 100% !important;
    .tree-w {
      @apply flex-1 h-0;
    }
  }
}
</style>
