<template>
  <div class="flex h-full w-full side-menu flex-col items-center overflow-hidden">
    <!-- <div v-if="systemName && systemName.trim().length > 0" class="flex items-center logo-info cursor-pointer"
      @click="router.push(menuList[0].resType == '0' ? `${menuList[0].children[0].resUrl}` : menuList[0].resUrl)">
      <div class="ml-10px" v-if="!isCollapse">
        <p class="title">{{ systemName }}</p>
      </div>
    </div>

    <div v-else class="flex items-center logo-info2 cursor-pointer" @click="
      router.push(
        menuList[0].resType == '0'
          ? menuList[0].children.length != 0
            ? menuList[0].children[0].resUrl
            : menuList[0].resUrl
          : menuList[0].resUrl
      )
      ">
      <img :src="logo" class="" alt="" />
    </div> -->

    <div class="menu-wrap w-full" :class="isCollapse ? 'collapse' : 'notCollapse'">
      <el-menu :collapse="isCollapse" unique-opened ref="menu">
        <template v-for="item in menuList" :key="item.id">
          <side-menu-item v-if="item.resUrl" :menu-data="item"></side-menu-item>
        </template>
      </el-menu>
    </div>
    <div
      class="toggle"
      :style="{
        top: '24px',
        left: counterStore.isCollapse ? '13px' : '20px'
      }"
      @click="setCollapse"
    >
      <svg-icon v-if="isCollapse" name="expand_1" :size="16" color="#ffffff"></svg-icon>
      <svg-icon v-else name="fold_1" :size="20" color="#ffffff"></svg-icon>
      <div :style="{ display: counterStore.isCollapse ? 'none' : 'block' }" class="ml-13px">
        {{ ui.zhLogo === 'yanchang' ? '承包商管理系统' : '相关方管理系统' }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRaw, ref, watch, onMounted, computed, nextTick } from 'vue'
import { useUserInfo, useCounterStore } from '~/store'
import { useRouter, useRoute } from 'vue-router'
import { MenuProvider } from 'element-plus'
import { useProvideMenu } from './useMenuContext'
import useLocale from '../locale'
import sideMenuItem from './menuItem.vue'

const ui = useUserInfo()
// import { menuData } from '~/layout/sideMenu/menuData'

interface Resource {
  id: string
  resName: string
  resIcon: string
  resAlias: string
  resUrl: string
  resType: string
  children: Resource[]
}
interface MenuMap {
  [key: string]: Resource
}
const counterStore = useCounterStore()
const userInfo = useUserInfo()

const router = useRouter()

const route = useRoute()

const menu = ref()

const menuIdMap: MenuMap = {}

const menuPathMap: MenuMap = {}

let menuList = ref([] as Resource[])

let rootMenu: MenuProvider | null = null

const isCollapse = computed(() => counterStore.isCollapse)
watch(route, handleRouteChange)

useProvideMenu({
  handlMenuItemClick: (item) => {
    router
      .push({
        path: item.resUrl
      })
      .catch((err) => {
        window.location.reload()
      })
  },

  setRootMenu(menu) {
    rootMenu = menu
  }
})
function setCollapse() {
  console.log('1')
  counterStore.isCollapse = !counterStore.isCollapse
}
let menuIdArr: Resource[] = []
function menuToMap(mList: Resource[]) {
  mList.forEach((item) => {
    menuIdArr.push(item)
    menuIdMap[item.id] = item
    menuPathMap[item.resUrl] = item
    if (!item.children) item.children = []
    item.children.length > 0 && menuToMap(item.children)
  })
  sessionStorage.setItem('menuIdArr', JSON.stringify(menuIdArr))
  sessionStorage.setItem('menuIdMap', JSON.stringify(menuIdMap))
  sessionStorage.setItem('menuPathMap', JSON.stringify(menuPathMap))
}

function handleRouteChange() {
  const { path } = route
  const paths = path.split('/')
  // parentPathName 父级路由名称不能传错,否则查询不到
  const { isChildComponents, parentPathName } = route.meta

  while (paths.length > 0) {
    const item = menuPathMap[paths.join('/')]

    if (item) {
      rootMenu!.activeIndex = item.id
      if (isChildComponents && parentPathName) {
        const curItem: any = item.children.find((child) => {
          return child.resUrl.includes(parentPathName as string)
        })
        if (curItem) rootMenu!.activeIndex = curItem.id
      }
      break
    } else {
      paths.pop()
    }
  }
}

const NINE_MENU_ID = '4da5c689986a4ca9b7fe196d1b289c04'

onMounted(async () => {
  const ui = userInfo.value
  const { t } = useLocale(ui.unitId)
  //sta 如果使用动态路由请放开下面代码
  const resourceList = Object.assign([], toRaw(ui.coopResourceList))
  //end

  //如果静态路由请放开下面代码
  // const resourceList = Object.assign([], toRaw(menuData))
  // console.log('resourceList', resourceList)
  //静态路由
  const index = resourceList.findIndex((item: any) => item.id === NINE_MENU_ID)
  const item: Resource = resourceList[index]
  if (item) {
    item.resName = t('val.nineMenuName')
  }
  if (ui.hasSupervise !== '1') {
    if (index !== -1) resourceList.splice(index, 1)
  }

  menuList.value = resourceList
  // menuList.value = menuData;

  menuToMap(menuList.value)
  await nextTick()
  handleRouteChange()
})
</script>

<script lang="ts">
export default {}
</script>

<style lang="scss">
@font-face {
  font-family: 'MyCustomFont';
  src: url('../../assets/font/YouSheBiaoTiHei-Bold.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

$menuHeight: 48px;

:deep(el-menu el-menu--inline) {
}

.side-menu {
  background: rgba(37, 40, 67, 1);
  position: relative;

  .toggle {
    // background: #fff;
    color: #fff;
    position: absolute;
    transform: translate(0%, -50%);
    transition: all 0.5s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .el-icon {
      width: 30px;
      height: 30px;

      svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  .logo-info {
    height: 80px;
    box-sizing: border-box;
    color: white;

    .title {
      // font-weight: bold;
      // font-size: 18px;

      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      line-height: 29px;
      text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .logo-info2 {
    width: 100%;
    height: 80px;
    padding: 0 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .menu-wrap {
    width: 100%;
    position: relative;
    // height: calc(100% - 140px);
    overflow-x: scroll;
    height: 100%;
    margin-top: 48px;

    &::-webkit-scrollbar {
      display: none;
    }

    .el-menu {
      background: rgba(37, 40, 67, 1);
      border: none;

      .el-sub-menu__title,
      .el-menu-item {
        color: white;
        opacity: 0.8;
        height: $menuHeight;

        &:hover {
          background: rgba(54, 61, 100, 1);
          opacity: 1;
        }
      }

      .el-sub-menu .el-menu-item {
        height: $menuHeight;
      }

      .el-menu-item.is-active {
        color: #fff;
        /* background: rgba(82, 120, 239, 1) !important; */
        opacity: 1;
        /* margin: 0 10px;
        border-radius: 4px; */

        &::before {
          content: '';
          position: absolute;
          left: 8px;
          right: 8px;
          /* width: 93%; */
          height: 100%;
          background: #5278ef;
          border-radius: 4px;
        }

        &::after {
          content: '';
          position: absolute;
          right: 0;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
          border-right: 8px solid #c8d5ff;
        }
      }
    }
  }

  .notCollapse {
    min-width: 300px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .notCollapse {
    min-width: 208px;

    .el-icon.el-sub-menu__icon-arrow {
      right: 30px;
    }
  }

  .el-menu--collapse {
    width: 100%;

    .mue-btn {
      text-align: center;
      display: flex;
      padding: 0 10px !important;
    }
  }

  .collapse {
    // min-width: unset;
    // width: 40px;

    .el-sub-menu__title {
      text-align: center;
      display: flex;
      padding: 0 11px !important;
    }

    .el-menu-item.is-active {
      color: #fff;
      /* background: rgba(82, 120, 239, 1) !important; */
      opacity: 1;
      /* margin: 0 10px;
        border-radius: 4px; */

      &::before {
        left: 0px !important;
        right: 8px;
      }
    }
  }
}
</style>
