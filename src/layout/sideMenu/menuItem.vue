<!--
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-16 08:59:00
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 14:48:36
 * @FilePath: /ehs-partner-mgr/src/layout/sideMenu/menuItem.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-sub-menu :index="md.id" v-if="hasChild" :class="{ 'child-menu-bg': !isTopLevel }">
    <template #title>
      <img v-if="isInIfm && md.resIconUrl" class="w-[20px] h-[20px]" :src="md.resIconUrl" alt="" />
      <svg-icon v-if="!isInIfm && md.resIcon" :color="svnColor" :name="md.resIcon" :size="20"></svg-icon>
      <span class="ml-10px" :style="fontSize">{{ md.resName }}</span>
    </template>
    <template v-for="item in md.children" :key="item.id">
      <side-menu-item v-if="item.resUrl" :menu-data="item"></side-menu-item>
    </template>
  </el-sub-menu>
  <el-menu-item
    :index="md.id"
    @click="handleClick"
    :class="{ 'child-menu-bg': !isTopLevel, 'mue-btn': md.parentId == -1 }"
    v-else
  >
    <img v-if="isInIfm && md.resIconUrl" class="w-[20px] h-[20px]" :src="md.resIconUrl" alt="" />
    <svg-icon
      class="relative"
      style="z-index: 10"
      v-if="!isInIfm && md.resIcon"
      :color="svnColor"
      :name="md.resIcon"
      :size="20"
    >
    </svg-icon>
    <span class="ml-10px relative" :style="fontSize">{{ md.resName }}</span>
  </el-menu-item>
</template>

<script lang="ts" setup>
import { computed, inject } from 'vue'
import { ElSubMenu, ElMenuItem, MenuProvider } from 'element-plus'
import svgIcon from '~/components/public/svgIcon.vue'
import { useMenuContext } from './useMenuContext'

const props = defineProps({
  menuData: {
    type: Object,
    default: () => ({})
  }
})

const rootMenu: MenuProvider = inject('rootMenu')!
const isInIfm = window.__IFM_ENV__ // 是否是ifm微前端环境
const menuContext = useMenuContext()

const md = computed(() => props.menuData)
const hasChild = computed(() => {
  // 菜单的 resType 等于 1 2 时不显示子集
  return md.value.children.length > 0 && !['1', '2'].includes(md.value.resType)
})

const svnColor = computed(() => {
  return rootMenu.activeIndex === md.value.id ? '#527CFF' : 'white'
})

const isTopLevel = computed(() => !!md.value.resIcon)

const fontSize = computed(() => ({
  fontSize: 16 + 'px'
}))

menuContext.setRootMenu(rootMenu)

function handleClick() {
  sessionStorage.removeItem('tabsPaneIndex')
  sessionStorage.removeItem('editableTabsValue')
  menuContext.handlMenuItemClick(md.value as any)
}
</script>

<script lang="ts">
export default {
  name: 'sideMenuItem'
}
</script>

<style lang="scss">
.child-menu-bg {
  // background: #2D8AEA !important;
}
</style>
