<template>
  <div class="h-full w-full global-header h-64px flex items-center justify-between">
    <div class="flex unit-name items-center mr-60px cursor-pointer" @click="goHome">
      <div class="img3" :style="{ backgroundImage: `url(${netLogo}` }"></div>
      <span class="text-30px sys_title">{{ ui.zhLogo === 'yanchang' ? '承包商管理系统' : '相关方管理系统' }}</span>
    </div>

    <div class="flex items-center ml-60px">
      <div class="flex items-center w-160px justify-between mr-30px">
        <div class="flex items-center cursor-pointer" style="visibility: hidden">
          <svg-icon name="help" :size="26"></svg-icon>
          <span style="color: #607590">帮助</span>
        </div>
        <i class="block w-2px h-16px" style="background: rgba(234, 234, 234, 1); visibility: hidden"></i>
      </div>
      <div class="user-info flex items-center" @click="showModifyPassword = true">
        <div class="img img-text">{{ userInitial }}</div>
        <span class="user-name">{{ ui.userName }}</span>
      </div>
    </div>
  </div>
  <popup-component v-model="noticeShow">
    <div class="popup-wraps relative">
      <div class="absolute bg-icon"></div>
      <div class="absolute bj"></div>
      <div class="absolute dbj"></div>
      <header class="flex justify-between items-center">
        <div class="flex-1 flex justify-center">
          <div class="noticePopupTitle text-center z-1 text-24px">公告</div>
        </div>
        <div class="h-16px w-16px min-h-16px min-w-16px cursor-pointer z-1 mt-6px" @click.stop="noticeShow = false">
          <!-- <img src="../../assets/image/noticePopup/gb.png" alt="" class="h-full w-full" /> -->
        </div>
      </header>
      <div class="content cursor-pointer">
        <div v-html="noticeMain"></div>
      </div>
    </div>
  </popup-component>
</template>

<script lang="ts" setup>
import { computed, ref, provide, onMounted } from 'vue'
import { useUserInfo } from '@/store'
const showPopover = ref(false)

provide('showMessage', showPopover)

const ui = useUserInfo()
const netLogo = ui.value.zhLogoUrl + ui.value.zhLogo + '.png'
const noticeShow = ref(false)
const noticeMain = ref('')

const userInitial = computed(() => {
  const name = ui.value.userName || ''
  return name.length >= 2 ? name.slice(-2) : name
})
function goHome() {
  // window.open(ui.value.zhPlatformUrl, '_target')
  window.open(ui.value.zhPlatformUrl, 'myWindow')
}

onMounted(() => {})

const showModifyPassword = ref(false)
</script>

<style lang="scss">
.global-header {
  background: rgba(37, 40, 67, 1);
  // padding-left: 60px;
  padding-right: 30px;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);

  svg {
    margin-right: 5px;
  }

  .unit-name {
    // border-left: 1px solid var(--el-border-color);
    height: 100%;
    padding-left: 14px;

    .img3 {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      background-size: 100% 100%;
    }

    .sys_title {
      font-family: MyCustomFont, sans-serif;
      font-weight: 400;
      font-size: 30px;
      color: #ffffff;
      line-height: 29px;
      text-shadow: 0px 2px 8px rgba(41, 47, 58, 0.2016);
    }
  }

  .message-badge {
    .el-badge__content {
      right: 22px;
    }
  }

  .notice {
    height: 36px;
    // line-height: 36px;
    padding: 0 20px;
    width: 50%;
    border-radius: 19px;
    background: #e7f4ff;
    box-sizing: border-box;
    width: 500px;
    // .el-carousel{
    //   line-height: 36px;
    // }
  }

  .exit {
    // background: rgba(236, 249, 254, 1);
    border-radius: 12px;
    padding: 0px 10px;
    height: 30px;
    color: #fd8595;
  }

  .message-box {
    position: relative;

    .myborder {
      position: absolute;
      right: -0px;
      top: -5px;
      width: 25px;
      height: 20px;
      border-radius: 100%;
      background: #f00;
      color: #ffffff;
      text-align: center;
      font-size: 12px;
      line-height: 20px;
    }
  }

  .user-info {
    font-size: 14px;
    font-weight: 700;

    .user-name {
      color: #ffffff;
    }
    .img {
      width: 40px;
      height: 40px;
      object-fit: cover;
      margin-right: 15px;
      border-radius: 50%;
      background-color: #527cff;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.message_popover {
  width: 400px !important;
}
</style>
<style lang="scss" scoped>
.popup-wraps {
  background: white;
  border-radius: 12px;
  width: 860px;
  height: 560px;
  box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.2);
  // background: linear-gradient(to bottom,rgb(143, 196, 252) 60px,#ffffff);

  header {
    height: 42px;
    // border-bottom: 1px solid var(--el-border-color);
    padding: 0 18px;
    box-sizing: border-box;
    font-size: 20px;
    font-weight: bold;
    line-height: 42px;
    color: #ffffff;
  }

  .bg-icon {
    background: url(../../assets/image/noticePopup/-s-gg_icon.png) no-repeat center;
    background-size: 100% 100%;
    width: 93px;
    height: 95px;
    right: 18px;
    top: 6px;
  }

  .bj {
    background: url(../../assets/image/noticePopup/-s-bj.png) no-repeat center;
    background-size: 100% 100%;
    width: 100%;
    height: 384px;
    right: 0px;
    left: 0;
    top: 0;
  }

  .dbj {
    background: url(../../assets/image/noticePopup/-s-dbj.png) no-repeat center;
    background-size: 100% 100%;
    width: 100%;
    height: 364px;
    right: 0px;
    left: 0;
    top: 0;
  }

  .content {
    padding: 46px;
  }

  .el-icon {
    cursor: pointer;
  }

  .noticePopupTitle {
    background: url(../../assets/image/noticePopup/btbj.png) no-repeat center;
    width: 343px;
    height: 42px;
    line-height: 42px;
    background-size: 100% 100%;
  }
}
</style>
