/*
 * Crypto-JS v1.1.0
 * http://code.google.com/p/crypto-js/
 * Copyright (c) 2009, <PERSON>. All rights reserved.
 * http://code.google.com/p/crypto-js/wiki/License
 */
;(function () {
  var b = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  window.Crypto = {}
  var a = (Crypto.util = {
    rotl: function (d, c) {
      return (d << c) | (d >>> (32 - c))
    },
    rotr: function (d, c) {
      return (d << (32 - c)) | (d >>> c)
    },
    endian: function (d) {
      if (d.constructor == Number) {
        return (a.rotl(d, 8) & 16711935) | (a.rotl(d, 24) & **********)
      }
      for (var c = 0; c < d.length; c++) {
        d[c] = a.endian(d[c])
      }
      return d
    },
    randomBytes: function (d) {
      for (var c = []; d > 0; d--) {
        c.push(Math.floor(Math.random() * 256))
      }
      return c
    },
    stringToBytes: function (e) {
      var c = []
      for (var d = 0; d < e.length; d++) {
        c.push(e.charCodeAt(d))
      }
      return c
    },
    bytesToString: function (c) {
      var e = []
      for (var d = 0; d < c.length; d++) {
        e.push(String.fromCharCode(c[d]))
      }
      return e.join('')
    },
    stringToWords: function (f) {
      var e = []
      for (var g = 0, d = 0; g < f.length; g++, d += 8) {
        e[d >>> 5] |= f.charCodeAt(g) << (24 - (d % 32))
      }
      return e
    },
    bytesToWords: function (d) {
      var f = []
      for (var e = 0, c = 0; e < d.length; e++, c += 8) {
        f[c >>> 5] |= d[e] << (24 - (c % 32))
      }
      return f
    },
    wordsToBytes: function (e) {
      var d = []
      for (var c = 0; c < e.length * 32; c += 8) {
        d.push((e[c >>> 5] >>> (24 - (c % 32))) & 255)
      }
      return d
    },
    bytesToHex: function (c) {
      var e = []
      for (var d = 0; d < c.length; d++) {
        e.push((c[d] >>> 4).toString(16))
        e.push((c[d] & 15).toString(16))
      }
      return e.join('')
    },
    hexToBytes: function (e) {
      var d = []
      for (var f = 0; f < e.length; f += 2) {
        d.push(parseInt(e.substr(f, 2), 16))
      }
      return d
    },
    bytesToBase64: function (d) {
      if (typeof btoa == 'function') {
        return btoa(a.bytesToString(d))
      }
      var c = [],
        f
      for (var e = 0; e < d.length; e++) {
        switch (e % 3) {
          case 0:
            c.push(b.charAt(d[e] >>> 2))
            f = (d[e] & 3) << 4
            break
          case 1:
            c.push(b.charAt(f | (d[e] >>> 4)))
            f = (d[e] & 15) << 2
            break
          case 2:
            c.push(b.charAt(f | (d[e] >>> 6)))
            c.push(b.charAt(d[e] & 63))
            f = -1
        }
      }
      if (f != undefined && f != -1) {
        c.push(b.charAt(f))
      }
      while (c.length % 4 != 0) {
        c.push('=')
      }
      return c.join('')
    },
    base64ToBytes: function (d) {
      if (typeof atob == 'function') {
        return a.stringToBytes(atob(d))
      }
      d = d.replace(/[^A-Z0-9+\/]/gi, '')
      var c = []
      for (var e = 0; e < d.length; e++) {
        switch (e % 4) {
          case 1:
            c.push((b.indexOf(d.charAt(e - 1)) << 2) | (b.indexOf(d.charAt(e)) >>> 4))
            break
          case 2:
            c.push(((b.indexOf(d.charAt(e - 1)) & 15) << 4) | (b.indexOf(d.charAt(e)) >>> 2))
            break
          case 3:
            c.push(((b.indexOf(d.charAt(e - 1)) & 3) << 6) | b.indexOf(d.charAt(e)))
            break
        }
      }
      return c
    },
  })
  Crypto.mode = {}
})()
;(function () {
  var b = Crypto.util
  var a = [
    1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080,
    310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078,
    604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671,
    3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051,
    2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909,
    275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222,
    2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298,
  ]
  var c = (Crypto.SHA256 = function (f, d) {
    var e = b.wordsToBytes(c._sha256(f))
    return d && d.asBytes ? e : d && d.asString ? b.bytesToString(e) : b.bytesToHex(e)
  })
  c._sha256 = function (q) {
    var y = b.stringToWords(q),
      z = q.length * 8,
      r = [1779033703, 3144134277, 1013904242, 2773480762, 1359893119, 2600822924, 528734635, 1541459225],
      s = [],
      K,
      J,
      I,
      G,
      F,
      E,
      D,
      C,
      B,
      A,
      p,
      o
    y[z >> 5] |= 128 << (24 - (z % 32))
    y[(((z + 64) >> 9) << 4) + 15] = z
    for (var B = 0; B < y.length; B += 16) {
      K = r[0]
      J = r[1]
      I = r[2]
      G = r[3]
      F = r[4]
      E = r[5]
      D = r[6]
      C = r[7]
      for (var A = 0; A < 64; A++) {
        if (A < 16) {
          s[A] = y[A + B]
        } else {
          var n = s[A - 15],
            u = s[A - 2],
            M = ((n << 25) | (n >>> 7)) ^ ((n << 14) | (n >>> 18)) ^ (n >>> 3),
            L = ((u << 15) | (u >>> 17)) ^ ((u << 13) | (u >>> 19)) ^ (u >>> 10)
          s[A] = M + (s[A - 7] >>> 0) + L + (s[A - 16] >>> 0)
        }
        var t = (F & E) ^ (~F & D),
          k = (K & J) ^ (K & I) ^ (J & I),
          x = ((K << 30) | (K >>> 2)) ^ ((K << 19) | (K >>> 13)) ^ ((K << 10) | (K >>> 22)),
          v = ((F << 26) | (F >>> 6)) ^ ((F << 21) | (F >>> 11)) ^ ((F << 7) | (F >>> 25))
        p = (C >>> 0) + v + t + a[A] + (s[A] >>> 0)
        o = x + k
        C = D
        D = E
        E = F
        F = G + p
        G = I
        I = J
        J = K
        K = p + o
      }
      r[0] += K
      r[1] += J
      r[2] += I
      r[3] += G
      r[4] += F
      r[5] += E
      r[6] += D
      r[7] += C
    }
    return r
  }
  c._blocksize = 16
})()
