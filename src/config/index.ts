const isPro = import.meta.env.PROD
let base_host = window.$SYS_CFG.baseHost
const serviceName = window.$SYS_CFG.baseUrl
if (isPro) {
  base_host = ''
}
const config = {
  downloadFileUrl: base_host + '/',

  workPreview: window.$SYS_CFG.workPreview,

  USER_IFNO_NAMESPACE: '@@web_userInfo_partner',

  base_host,

  base_url: serviceName,
  // 身份证识别
  ocr_url: base_host + '/third/ocr',

  update_file: serviceName + '/edu-file-server',

  appServer: window.$SYS_CFG.appServer, // app和web公用服务,用于图片查看https://test-bw.gsafetycloud.com/

  openGisUrl: window.$SYS_CFG.openGisUrl
}
export default config
