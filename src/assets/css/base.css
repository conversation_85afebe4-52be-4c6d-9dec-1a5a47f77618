/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-09 15:31:14
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-16 19:56:33
 * @FilePath: /ehs-partner-mgr/src/assets/css/base.css
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
* {
    font-size: 16px;
    font-family: Alibaba-PuHuiTi-Regular;
}

body {
    overflow-x: hidden;
}

.font-hp-regular {
    font-family: Alibaba-PuHuiTi-Regular;
}

.font-hp-bd {
    font-family: Alibaba-PuHuiTi-Bold;
}

.font-hp-md {
    font-family: Alibaba-PuHuiTi-Medium;
}

.font-hp-rl {
    font-family: Alibaba-PuHuiTi-Regular;
}

.font-din-num {
    font-family: 'D-DIN-PRO-500-Medium';
}

.shadow-btn {
    box-shadow:0 0 20px rgb(64 158 255 / 30%);
}

.gray-1 {
    color: #333;
}

.el-select--default {
    height: 41px;
}

.select-trigger {
    height: 100%;
}

.el-input--suffix{
    height: 100%;
}

.el-input__inner{
    height: 100%;
}

.el-table__header tr,
.el-table__header th {
    padding: 0;
    height: 43px;
    line-height: 43px;
}

.el-table__body tr,
.el-table__body td {
    padding: 0;
    height: 60px;
    line-height: 60px;
}

.el-select .el-input__inner {
    height: 40px;
    line-height: 40px;
}

.el-tabs .el-tabs__item {
    font-size: 16px;
}

.el-table .el-scrollbar__view {
    height: 100%;
}

.el-loading-mask {
    z-index: 999;
}

.line-item {
    min-height: 36px;
    width: 100%;
    line-height: 36px;
}

.gs-content-wrap {
    line-height: 36px;
}

.left-content {
    color:  #606266;
    margin-right: 15px;
}

.right-content {
    color: #333;
}

.theme-color {
    color: #1890FF;
}

.collapse {
    visibility: visible !important;
}
