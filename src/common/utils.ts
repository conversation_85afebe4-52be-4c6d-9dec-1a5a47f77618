/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-03 18:23:52
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-08 12:56:16
 * @FilePath: /ehs-partner-mgr/src/common/utils.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// import config from '@/config'
import { ref } from 'vue'
import config from '~/config'
export function isTibetUnit(unitId: string) {
  return unitId === '540102DZDA202206010001'
}
export const loading = ref(false)

/**
 * 延迟函数
 * @param t 毫秒
 */
export function sleep(t: number) {
  return new Promise((resolve) => setTimeout(resolve, t))
}

export function getUserInfo() {
  return (JSON.parse(localStorage.getItem(config.USER_IFNO_NAMESPACE) as string) || {})
}
