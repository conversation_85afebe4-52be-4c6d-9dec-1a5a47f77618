import config from '@/config'
/**
 * @param filename 文件名称
 * @param fileUrl
 * 生成下载预览功能的html
 */
import pdfSvg from '@/assets/svg/PDF.svg'
import docSvg from '@/assets/svg/DOCX.svg'
export const generateHTML = (filename: string, fileUrl: string, replaceName: string) => {
  if (filename) {
    const strArr = filename.split('.')
    const extension = strArr[strArr.length - 1]
    const ext = extension.toLowerCase()
    const newUrl = config.downloadFileUrl + fileUrl
    if (ext === 'pdf' || ext === 'doc' || ext === 'docx') {
      const url = `http://**************:9092/onlinePreview?url=${btoa(newUrl)}`
      if (ext === 'pdf') {
        return `<div  style="color: #527cff; display:block; height: 20px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 10px; text-align: left;" target="_blank"><img src=${pdfSvg} style="display: inline-block; vertical-align: middle; height: 20px; margin-right: 5px; cursor:pointer;"/>${filename}</div></span>`
      } else {
        return `<div  style="color: #527cff; display:block; height: 20px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 10px; text-align: left;" target="_blank"><img src=${docSvg} style="display: inline-block; vertical-align: middle; height: 20px; margin-right: 5px;cursor:pointer;"/> ${filename}</div></span>`
      }
    } else {
      return `<div  style="color: #527cff;cursor:pointer;">${replaceName ? replaceName : filename}</div></span>`
    }
  }
}
