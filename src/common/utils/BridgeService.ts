import sySend from 'sysend'

type MessageCallback = (msg: any) => void

/**
 * 跨窗口通信服务
 */
export class BridgeService {
  private readonly channel: string
  private listeners: Map<string, MessageCallback>
  public enableLogging: boolean

  constructor(channel: string, enableLogging = false) {
    this.channel = channel
    this.listeners = new Map()
    this.enableLogging = enableLogging
    this.init()
  }

  private init() {
    // 监听所有消息
    sySend.on(this.channel, (msg: any) => {
      const { eventName, data } = msg
      this.log(`Received message on event: ${eventName}`, data)
      const callback = this.listeners.get(eventName)
      if (callback) {
        callback(data)
      }
    })
  }

  static registerProxy() {
    sySend.proxy('http://localhost:5175') // 子应用 ip:port
  }

  /**
   * 发送消息到其他窗口
   * @param eventName 事件名
   * @param data 数据
   */
  public sendMessage(eventName: string, data: any) {
    this.log(`Sending message on event: ${eventName}`, data)
    sySend.broadcast(this.channel, { eventName, data })
  }

  /**
   * 注册消息事件监听
   * @param eventName 事件名
   * @param callback 回调函数
   */
  public onMessage(eventName: string, callback: MessageCallback) {
    this.listeners.set(eventName, callback)
    this.log(`Listener registered for event: ${eventName}`)
  }

  /**
   * 移除消息监听
   * @param eventName 事件名
   */
  public offMessage(eventName: string) {
    this.listeners.delete(eventName)
    this.log(`Listener removed for event: ${eventName}`)
  }

  /**
   * 清除所有监听事件
   */
  public clearListeners() {
    this.listeners.clear()
    this.log('All listeners cleared')
  }

  /**
   * 日志功能
   * @param message 日志信息
   * @param data 可选的附加数据
   */
  private log(message: string, data?: any) {
    if (this.enableLogging) {
      console.log(`[BridgeService] ${message}`, data || '')
    }
  }
}
