/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-04 01:53:18
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-10 19:43:37
 * @FilePath: /ehs-partner-mgr/src/common/registerComponent.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { App } from 'vue'
import * as icons from '@element-plus/icons-vue'
import svgIcon from '@/components/public/svgIcon.vue'
import { ToolTip as myTooltip, ToolTips as myTooltips } from '@tanzerfe/tanzer-ui'
import popupComponent from '@/components/public/popup/index.vue'
import popupSide from '~/components/public/popup/popupSide.vue'
import popupWrap from '~/components/public/popup/popupWrap.vue'
import popupVideo from '~/components/public/popup/popupVideo.vue'
import tableList from '@/components/public/tableList.vue'
import headerItem from '@/components/public/headerItem/index.vue'

import { ClickOutside } from 'element-plus'
export default function registerComponent(app: App) {
  Object.keys(icons).forEach((key) => {
    app.component(`Icon${key}`, icons[key])
  })
  app.component(svgIcon.name!, svgIcon)
  app.component(popupComponent.name!, popupComponent)

  app.component(popupSide.name!, popupSide)

  app.component(popupWrap.name!, popupWrap)

  app.component(popupVideo.name!, popupVideo)

  app.component(tableList.name!, tableList)

  app.component(headerItem.name!, headerItem)
  app.component('myTooltip', myTooltip)
  app.component('myTooltips', myTooltips)
  app.directive('clickoutside', ClickOutside)
  app.directive('isClick', (el, binding) => {
    let downTime: any = null
    let upTime: any = null
    el.addEventListener('mousedown', (event) => {
      downTime = new Date().getTime()
      el._stop = event.stopPropagation // 阻止点击事件冒泡
    })
    el.addEventListener('mouseup', () => {
      upTime = new Date().getTime()
      const selectionTxt = window.getSelection()?.toString().trim()
      if (upTime - downTime < 100 || selectionTxt!.length == 0) {
        el.setAttribute('event-type', 'click')
      } else {
        el.setAttribute('event-type', 'copy')
      }
      cb()
    })

    function cb() {
      const methodToCall = binding.value // 获取传递的方法
      const condition = binding.arg // 获取传递的参数
      if (typeof methodToCall === 'function') {
        console.log(el.getAttribute('event-type'))
        // 根据条件判断是否要调用方法
        if (el.getAttribute('event-type') === 'click') {
          methodToCall(condition) // 调用传递的方法，并传入参数
        }
      }
    }
  })
}
