/*
 * @Author: <EMAIL> <EMAIL>
 * @Date: 2024-07-03 18:23:54
 * @LastEditors: <EMAIL> <EMAIL>
 * @LastEditTime: 2024-07-17 17:29:37
 * @FilePath: /ehs-partner-mgr/vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import vue from '@vitejs/plugin-vue'
import { resolve as r } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import WindiCSS from 'vite-plugin-windicss'
import { envConfigPlugin, manifestPlugin } from '@tanzerfe/plugins-vite'

const resvole = (_path) => r(__dirname, _path)
export default defineConfig(() => {
  return {
    base: '/ehs-partner-mgr/',
    resolve: {
      alias: {
        '~': resvole('src'),
        '@': resvole('src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
          additionalData: "@use '@/assets/css/variables.scss';"
        }
      }
    },
    server: {
      port: 5150,
      open: true
    },
    build: {
      outDir: `/ehs-partner-mgr/`,
      manifest: true
    },
    plugins: [
      vue(),
      createSvgIconsPlugin({
        iconDirs: [resvole('src/assets/svg')],
        symbolId: 'icon-[name]',
        customDomId: '__svg__icons__dom__'
      }),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver()]
      }),
      WindiCSS({
        scan: {
          include: ['./App.vue', './src/**/*.vue']
        }
      }),
      manifestPlugin({
        output: `ehs-partner-mgr`,
        preload: ['com/config.js'], // index.html 内加载的js 根据实际项目填写
        exclude: [], // 忽略文件 默认空
        enableLog: false
      }),
      envConfigPlugin({
        outputDir: 'ehs-partner-mgr/com',
        obfuscate: true
      })
    ]
  }
})
